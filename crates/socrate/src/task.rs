use std::fmt::{Disp<PERSON>, Formatter};

pub type TaskId = u64;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed(String),
}

#[derive(Debug, Clone)]
pub enum TaskKind {
    WriteFile { path: String, contents: String },
    ExecuteCmd { cmd: String },
    ReadFile { path: String },
    SearchFiles { pattern: String },
    Search { query: String },
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Task {
    pub id: TaskId,
    pub goal: String,
    pub kind: TaskKind,
    pub status: TaskStatus,
}

impl Task {
    pub fn is_complete(&self) -> bool {
        matches!(self.status, TaskStatus::Completed)
    }
}

impl Display for Task {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "Task#{:?} – {:?}", self.id, self.kind)
    }
}
