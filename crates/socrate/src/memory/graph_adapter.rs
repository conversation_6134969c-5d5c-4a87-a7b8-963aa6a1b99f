/// Bridge verso matrix-context

use async_trait::async_trait;

/// Trait per adapter di memoria (compatibile con agent state)
#[async_trait]
pub trait MemoryAdapter: Send + Sync {
    async fn get(&self, key: &str) -> Option<String>;
    async fn set(&self, key: &str, value: &str);
    async fn delete(&self, key: &str);
}

/// Adapter concreto verso matrix-context (placeholder)
pub struct GraphMemoryAdapter;

#[async_trait]
impl MemoryAdapter for GraphMemoryAdapter {
    async fn get(&self, key: &str) -> Option<String> {
        println!("[GraphMemoryAdapter] get: {}", key);
        None // Da implementare: chiamata reale a matrix-context
    }

    async fn set(&self, key: &str, value: &str) {
        println!("[GraphMemoryAdapter] set: {} = {}", key, value);
        // Da implementare: chiamata reale a matrix-context
    }

    async fn delete(&self, key: &str) {
        println!("[GraphMemoryAdapter] delete: {}", key);
        // Da implementare: chiamata reale a matrix-context
    }
}
