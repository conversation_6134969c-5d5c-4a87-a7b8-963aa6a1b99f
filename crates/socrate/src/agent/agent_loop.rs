use crate::planner::plan::Step;
use crate::planner::Planner;
use crate::task::{Task, TaskId, TaskKind, TaskStatus};
use crate::tools;
use anyhow::Result;
use std::collections::VecDeque;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::RwLock;

static TASK_ID_COUNTER: AtomicU64 = AtomicU64::new(0);

#[async_trait::async_trait]
pub trait AgentLoop: Send + Sync {
    async fn run(&self, initial_goal: String) -> Result<()>;
}

pub struct DefaultAgentLoop {
    planner: Arc<dyn Planner + Send + Sync>,
    task_queue: Arc<RwLock<VecDeque<Task>>>,
}

impl DefaultAgentLoop {
    pub fn new(planner: Arc<dyn Planner + Send + Sync>) -> Self {
        Self {
            planner,
            task_queue: Arc::new(RwLock::new(VecDeque::new())),
        }
    }

    /// Converte uno `Step` dal planner in un `Task` eseguibile.
    fn step_to_task(&self, goal: &str, step: Step) -> Result<Task> {
        let kind = match step.tool_name.as_str() {
            "write_file" => TaskKind::WriteFile {
                path: step.params["path"].as_str().unwrap().to_string(),
                contents: step.params["contents"].as_str().unwrap().to_string(),
            },
            "execute_cmd" => TaskKind::ExecuteCmd {
                cmd: step.params["cmd"].as_str().unwrap().to_string(),
            },
            // Aggiungere altri tool qui
            _ => return Err(anyhow::anyhow!("Tool sconosciuto: {}", step.tool_name)),
        };

        Ok(Task {
            id: TASK_ID_COUNTER.fetch_add(1, Ordering::SeqCst),
            goal: goal.to_string(),
            kind,
            status: TaskStatus::Pending,
        })
    }
}

#[async_trait::async_trait]
impl AgentLoop for DefaultAgentLoop {
    async fn run(&self, initial_goal: String) -> Result<()> {
        // 1. Crea un task iniziale dall'obiettivo
        let initial_task = Task {
            id: TASK_ID_COUNTER.fetch_add(1, Ordering::SeqCst),
            goal: initial_goal.clone(),
            // Questo tipo di task potrebbe essere un "Plan" task per chiarezza
            kind: TaskKind::Search { query: initial_goal }, // Usiamo Search come placeholder
            status: TaskStatus::Pending,
        };

        // 2. Il planner scompone il task iniziale in una serie di step
        let steps = self.planner.plan(&initial_task);

        // 3. Converti gli step in task eseguibili e mettili in coda
        for step in steps {
            let task = self.step_to_task(&initial_task.goal, step)?;
            self.task_queue.write().await.push_back(task);
        }

        // 4. Esegui i task in coda
        while let Some(mut task) = self.task_queue.write().await.pop_front() {
            println!("Executing task: {}", task);
            task.status = TaskStatus::Running;
            
            // L'esecuzione ora è gestita dal dispatcher di tool
            let result = tools::execute(&task).await;
            
            task.status = result.unwrap_or_else(|e| TaskStatus::Failed(e.to_string()));
            println!("Task finished with status: {:?}", task.status);
        }

        Ok(())
    }
}
