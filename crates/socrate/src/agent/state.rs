use crate::mpc::MpcRegistry;
use crate::planner::Planner;
use crate::task::Task;
use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::RwLock;

pub type PlannerHandle = Arc<dyn Planner + Send + Sync>;

pub struct AgentState {
    pub planner: PlannerHandle,
    pub mpc_registry: Arc<RwLock<MpcRegistry>>,
    pub task_queue: Arc<RwLock<VecDeque<Task>>>,
    pub current_context: Option<String>,
    pub memory_adapter: Option<Arc<dyn crate::memory::graph_adapter::MemoryAdapter + Send + Sync>>,
}

impl AgentState {
    pub fn new(planner: PlannerHandle, mpc_registry: MpcRegistry) -> Self {
        Self {
            planner,
            mpc_registry: Arc::new(RwLock::new(mpc_registry)),
            task_queue: Arc::new(RwLock::new(VecDeque::new())),
            current_context: None,
            memory_adapter: None,
        }
    }

    pub async fn push_task(&self, task: Task) {
        self.task_queue.write().await.push_back(task);
    }

    pub async fn pop_task(&self) -> Option<Task> {
        self.task_queue.write().await.pop_front()
    }

    pub fn set_context(&mut self, ctx: String) {
        self.current_context = Some(ctx);
    }

    pub fn clear_context(&mut self) {
        self.current_context = None;
    }
}
