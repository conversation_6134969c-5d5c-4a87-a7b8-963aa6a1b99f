use super::agent_loop::{Agent<PERSON>oop, DefaultAgentLoop};
use crate::planner::kilo_port::<PERSON>loPlanner;
use anyhow::Result;
use std::sync::Arc;

use crate::tools;

/// Punto di ingresso principale di Socrate.
/// Istanzia e avvia l'agente con un obiettivo iniziale.
pub async fn start() -> Result<()> {
    println!("SOCRATE: Inizializzazione dell'agente...");

    // 1. Registra i tool disponibili
    tools::register_tools().await;
    println!("SOCRATE: Tool registrati.");

    // 2. Istanzia il planner
    let planner = Arc::new(KiloPlanner);

    // 3. Istanzia il loop dell'agente
    let agent_loop = Arc::new(DefaultAgentLoop::new(planner));

    // 4. Avvia il loop con un obiettivo di test
    let initial_goal = "Crea un file 'hello.rs', compilalo ed eseguilo.".to_string();
    println!("SOCRATE: Obiettivo iniziale -> {}", initial_goal);

    agent_loop.run(initial_goal).await?;

    println!("SOCRATE: Esecuzione completata.");
    Ok(())
}
