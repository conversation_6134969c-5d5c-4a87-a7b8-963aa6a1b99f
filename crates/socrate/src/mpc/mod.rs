pub mod context7;
pub mod memory_graph;
pub mod sequential;

use crate::config::MpcConfig;
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;

#[async_trait]
pub trait MPCModule: Send + Sync {
    async fn init(&self, cfg: &MpcConfig) -> anyhow::Result<()>;
    async fn invoke(&self, method: &str, params: serde_json::Value) -> anyhow::Result<serde_json::Value>;
}

/// Trait per tool wrapper che invoca un MPC tool
#[async_trait]
pub trait MpcTool: Send + Sync {
    async fn call(&self, registry: &MpcRegistry, module: &str, method: &str, params: serde_json::Value) -> anyhow::Result<serde_json::Value>;
}
pub struct MpcRegistry {
    modules: HashMap<String, Arc<dyn MPCModule>>,
}

impl Default for MpcRegistry {
    fn default() -> Self {
        Self::new()
    }
}

impl MpcRegistry {
    pub fn new() -> Self {
        Self {
            modules: HashMap::new(),
        }
    }

    pub fn register(&mut self, name: &str, module: Arc<dyn MPCModule>) {
        self.modules.insert(name.to_string(), module);
    }

    pub fn get_module(&self, name: &str) -> Option<&Arc<dyn MPCModule>> {
        self.modules.get(name)
    }
}
