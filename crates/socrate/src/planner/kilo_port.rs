use crate::planner::plan::Step;
use crate::planner::Planner;
use crate::task::Task;
use serde_json::json;

pub struct <PERSON><PERSON>Plan<PERSON>;

impl Planner for KiloPlanner {
    fn plan(&self, task: &Task) -> Vec<Step> {
        println!("KiloPlanner sta pianificando per il task: {}", task.goal);

        // Implementazione dummy per lo STADIO 2.
        // Prende il "goal" del task e lo scompone in step predefiniti.
        // In uno scenario reale, qui ci sarebbe la logica del planner di Kilo Code.
        vec![
            Step {
                tool_name: "write_file".to_string(),
                params: json!({
                    "path": "hello.rs",
                    "contents": "fn main() { println!(\"Hello from Socrate!\"); }"
                }),
            },
            Step {
                tool_name: "execute_cmd".to_string(),
                params: json!({ "cmd": "rustc hello.rs" }),
            },
            Step {
                tool_name: "execute_cmd".to_string(),
                params: json!({ "cmd": "./hello" }),
            },
        ]
    }
}
