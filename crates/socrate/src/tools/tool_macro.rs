// Macro per generare implementazioni di Tool
#[macro_export]
macro_rules! impl_tool {
    ($tool_name:ident, $task_kind:path, $action:block) => {
        pub struct $tool_name;

        #[async_trait::async_trait]
        impl Tool for $tool_name {
            async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus> {
                if let $task_kind = &task.kind {
                    $action
                } else {
                    Ok(TaskStatus::Failed(format!(
                        "TaskKind non supportato da {}",
                        stringify!($tool_name)
                    )))
                }
            }
        }
    };
}