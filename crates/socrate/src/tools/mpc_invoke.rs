use crate::mpc::{MpcRegistry, MpcTool};
use crate::task::{Task, TaskKind, TaskStatus};
use crate::tools::Tool;
use async_trait::async_trait;
use serde_json::json;
use std::sync::Arc;

/// Tool wrapper che invoca un metodo di un MPCModule tramite il registry
pub struct MpcInvokeTool {
    pub registry: Arc<MpcRegistry>,
    pub module: String,
    pub method: String,
}

#[async_trait]
impl Tool for MpcInvokeTool {
    async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus> {
        // Esempio: parametri dummy, in futuro estrarre da TaskKind
        let params = json!({ "input": "test" });
        let module = self.registry.get_module(&self.module);
        if let Some(module) = module {
            let result = module.invoke(&self.method, params).await?;
            println!("[MpcInvokeTool] result: {:?}", result);
            Ok(TaskStatus::Completed)
        } else {
            Ok(TaskStatus::Failed(format!("MPC module not found: {}", self.module)))
        }
    }
}
