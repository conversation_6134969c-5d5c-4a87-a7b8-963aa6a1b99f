use crate::task::{Task, TaskKind, TaskStatus};
use crate::tools::Tool;
use async_trait::async_trait;

/// Tool per eseguire comandi shell (dummy, ispirato a executeCommandTool di Kilo Code)
pub struct ExecuteCmdTool;

#[async_trait]
impl Tool for ExecuteCmdTool {
    async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus> {
        if let TaskKind::ExecuteCmd { cmd } = &task.kind {
            println!("Eseguo comando: {}", cmd);
            // Dummy: restituisce sempre completato
            Ok(TaskStatus::Completed)
        } else {
            Ok(TaskStatus::Failed("TaskKind non supportato da ExecuteCmdTool".to_string()))
        }
    }
}
