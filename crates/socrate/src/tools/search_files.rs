use crate::task::{Task, TaskKind, TaskStatus};
use crate::tools::Tool;
use async_trait::async_trait;

/// Tool per cercare file (dummy, ispirato a searchFilesTool di Kilo Code)
pub struct SearchFilesTool;

#[async_trait]
impl Tool for SearchFilesTool {
    async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus> {
        if let TaskKind::SearchFiles { pattern } = &task.kind {
            println!("Cerco file con pattern: {}", pattern);
            // Dummy: restituisce sempre completato
            Ok(TaskStatus::Completed)
        } else {
            Ok(TaskStatus::Failed("TaskKind non supportato da SearchFilesTool".to_string()))
        }
    }
}
