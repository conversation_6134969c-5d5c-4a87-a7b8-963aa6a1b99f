pub mod ast;
pub mod code_exec;
pub mod fs;
pub mod git;
pub mod search;
pub mod code_search;
pub mod write_file;
pub mod read_file;
pub mod search_files;
pub mod mpc_invoke;
#[macro_use]
pub mod tool_macro;

use crate::task::{Task, TaskKind, TaskStatus};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[async_trait::async_trait]
pub trait Tool: Send + Sync {
    async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus>;
}

static REGISTRY: once_cell::sync::Lazy<RwLock<HashMap<&'static str, Arc<dyn Tool>>>> =
    once_cell::sync::Lazy::new(|| RwLock::new(HashMap::new()));

use self::{
    fs::ExecuteCmdTool, read_file::ReadFileTool, write_file::WriteFileTool,
};

pub async fn register<T: Tool + 'static>(name: &'static str, tool: T) {
    REGISTRY.write().await.insert(name, Arc::new(tool));
}

/// Registra tutti i tool di base nel registry.
pub async fn register_tools() {
    register("write_file", WriteFileTool).await;
    register("execute_cmd", ExecuteCmdTool).await;
    register("read_file", ReadFileTool).await;
    // Aggiungi qui la registrazione per altri tool...
}

pub async fn execute(task: &Task) -> anyhow::Result<TaskStatus> {
    // semplice dispatch demo
    let registry = REGISTRY.read().await;
    let tool_name = match &task.kind {
        TaskKind::WriteFile { .. } => "write_file",
        TaskKind::ExecuteCmd { .. } => "execute_cmd",
        TaskKind::Search { .. } => "search",
        TaskKind::ReadFile { .. } => "read_file",
        TaskKind::SearchFiles { .. } => "search_files",
        // Tool KiloCode: codebaseSearchTool
        // Per ora alias su "search", in futuro separato
        // TaskKind::CodeSearch { .. } => "code_search",
    };

    let exec_tool = registry
        .get(tool_name)
        .ok_or_else(|| anyhow::anyhow!("Tool not found: {}", tool_name))?;
    exec_tool.execute(task).await
}
