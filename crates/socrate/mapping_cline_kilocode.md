# Mapping Socrate <-> Cline / Kilo Code

[...tabella e note precedenti...]

---

## Stato implementazione trait/struct principali

- [x] **AgentLoop trait** (`src/agent/agent_loop.rs`)  
      - Struct <PERSON><PERSON>ult<PERSON>gent<PERSON><PERSON>, trait AgentLoop, entry-point run_with_agent
- [x] **Planner trait** (`src/planner/mod.rs`)  
      - Trait Planner, implementazione plug-in in kilo_port.rs
- [x] **Tool trait & registry** (`src/tools/mod.rs`)  
      - Trait <PERSON>, registry dinamico, dispatch per tool Kilo/Matrix
- [x] **Esempio tool KiloCode** (`src/tools/code_search.rs`)  
      - CodeSearchTool: porting semplificato di codebaseSearchTool, pipeline plug-in funzionante

**Prossimi step:**
- Portare/adattare altri tool principali da Kilo Code (`core/tools/`) in `src/tools/`
- Rafforzare stato runtime e memory/context adapter
- Aggiornare test e documentazione

---
