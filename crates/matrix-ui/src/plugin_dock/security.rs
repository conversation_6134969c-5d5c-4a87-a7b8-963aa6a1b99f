//! Plugin Security Manager
//!
//! Implements security manager with permission system, sandboxing, and resource monitoring.

use super::{PluginDockError, PluginDockResult};

use std::{
    collections::{HashMap, HashSet},
    sync::{Arc, RwLock},
    time::{Duration, Instant},
};

use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Plugin permission types
#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum Permission {
    // File system permissions
    ReadFile(String),           // Read specific file or directory
    WriteFile(String),          // Write to specific file or directory
    ExecuteFile(String),        // Execute specific file
    
    // Network permissions
    NetworkAccess,              // General network access
    HttpRequest(String),        // HTTP requests to specific domain
    WebSocketConnect(String),   // WebSocket connections to specific domain
    
    // System permissions
    SystemCommand(String),      // Execute specific system command
    EnvironmentVariable(String), // Access specific environment variable
    ProcessSpawn,               // Spawn new processes
    
    // UI permissions
    CreateWindow,               // Create new windows
    ShowNotification,           // Show system notifications
    AccessClipboard,            // Access system clipboard
    
    // Plugin communication permissions
    SendMessage(String),        // Send messages to specific plugin
    BroadcastMessage,           // Broadcast messages to all plugins
    ReceiveMessage(String),     // Receive messages from specific plugin
    
    // Resource permissions
    MemoryLimit(u64),          // Memory usage limit in bytes
    CpuLimit(u64),             // CPU usage limit as percentage (scaled by 100)
    DiskLimit(u64),            // Disk usage limit in bytes
    NetworkBandwidth(u64),     // Network bandwidth limit in bytes/sec
}

impl Eq for Permission {}

impl std::hash::Hash for Permission {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        std::mem::discriminant(self).hash(state);
        match self {
            Permission::ReadFile(s) | Permission::WriteFile(s) | Permission::ExecuteFile(s) |
            Permission::HttpRequest(s) | Permission::WebSocketConnect(s) |
            Permission::SystemCommand(s) | Permission::EnvironmentVariable(s) |
            Permission::SendMessage(s) | Permission::ReceiveMessage(s) => s.hash(state),
            Permission::MemoryLimit(n) | Permission::CpuLimit(n) | 
            Permission::DiskLimit(n) | Permission::NetworkBandwidth(n) => n.hash(state),
            _ => {}
        }
    }
}

/// Set of permissions for a plugin
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PermissionSet {
    pub permissions: HashSet<Permission>,
    pub denied_permissions: HashSet<Permission>,
}

impl PermissionSet {
    /// Create a new empty permission set
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Add a permission
    pub fn allow(mut self, permission: Permission) -> Self {
        self.permissions.insert(permission);
        self
    }
    
    /// Deny a permission explicitly
    pub fn deny(mut self, permission: Permission) -> Self {
        self.denied_permissions.insert(permission);
        self
    }
    
    /// Check if a permission is allowed
    pub fn has_permission(&self, permission: &Permission) -> bool {
        // Explicit denial takes precedence
        if self.denied_permissions.contains(permission) {
            return false;
        }
        
        // Check for explicit permission or wildcard permissions
        self.permissions.contains(permission) || self.has_wildcard_permission(permission)
    }
    
    /// Check for wildcard permissions (e.g., ReadFile("*") allows ReadFile("any/path"))
    fn has_wildcard_permission(&self, permission: &Permission) -> bool {
        match permission {
            Permission::ReadFile(path) => {
                self.permissions.iter().any(|p| match p {
                    Permission::ReadFile(allowed_path) => {
                        allowed_path == "*" || path.starts_with(allowed_path)
                    },
                    _ => false,
                })
            },
            Permission::WriteFile(path) => {
                self.permissions.iter().any(|p| match p {
                    Permission::WriteFile(allowed_path) => {
                        allowed_path == "*" || path.starts_with(allowed_path)
                    },
                    _ => false,
                })
            },
            Permission::HttpRequest(domain) => {
                self.permissions.iter().any(|p| match p {
                    Permission::HttpRequest(allowed_domain) => {
                        allowed_domain == "*" || domain.ends_with(allowed_domain)
                    },
                    _ => false,
                })
            },
            _ => false,
        }
    }
    
    /// Create a restricted permission set for basic plugin functionality
    pub fn basic() -> Self {
        Self::new()
            .allow(Permission::ShowNotification)
            .allow(Permission::ReceiveMessage("*".to_string()))
            .allow(Permission::MemoryLimit(100 * 1024 * 1024)) // 100MB
            .allow(Permission::CpuLimit(1000)) // 10% CPU (scaled by 100)
    }
    
    /// Create a permission set for file system access
    pub fn file_system(base_path: &str) -> Self {
        Self::basic()
            .allow(Permission::ReadFile(base_path.to_string()))
            .allow(Permission::WriteFile(base_path.to_string()))
    }
    
    /// Create a permission set for network access
    pub fn network(allowed_domains: Vec<String>) -> Self {
        let mut set = Self::basic().allow(Permission::NetworkAccess);
        for domain in allowed_domains {
            set = set.allow(Permission::HttpRequest(domain));
        }
        set
    }
}

/// Resource usage tracking
#[derive(Debug, Clone, Default)]
pub struct ResourceUsage {
    pub memory_bytes: u64,
    pub cpu_percentage: f64,
    pub disk_bytes: u64,
    pub network_bytes_per_sec: u64,
    pub file_handles: u32,
    pub thread_count: u32,
    pub event_queue_size: u32,
    pub last_updated: Option<Instant>,
}

impl ResourceUsage {
    /// Check if any resource exceeds the given limits
    pub fn exceeds_limits(&self, limits: &ResourceLimits) -> Vec<String> {
        let mut violations = Vec::new();
        
        if let Some(limit) = limits.max_memory_bytes {
            if self.memory_bytes > limit {
                violations.push(format!("Memory usage {} exceeds limit {}", self.memory_bytes, limit));
            }
        }
        
        if let Some(limit) = limits.max_cpu_percentage {
            if self.cpu_percentage > limit {
                violations.push(format!("CPU usage {}% exceeds limit {}%", self.cpu_percentage, limit));
            }
        }
        
        if let Some(limit) = limits.max_disk_bytes {
            if self.disk_bytes > limit {
                violations.push(format!("Disk usage {} exceeds limit {}", self.disk_bytes, limit));
            }
        }
        
        if let Some(limit) = limits.max_network_bytes_per_sec {
            if self.network_bytes_per_sec > limit {
                violations.push(format!("Network usage {} exceeds limit {}", self.network_bytes_per_sec, limit));
            }
        }
        
        if let Some(limit) = limits.max_file_handles {
            if self.file_handles > limit {
                violations.push(format!("File handles {} exceeds limit {}", self.file_handles, limit));
            }
        }
        
        if let Some(limit) = limits.max_threads {
            if self.thread_count > limit {
                violations.push(format!("Thread count {} exceeds limit {}", self.thread_count, limit));
            }
        }
        
        violations
    }
}

/// Resource limits for a plugin
#[derive(Debug, Clone)]
pub struct ResourceLimits {
    pub max_memory_bytes: Option<u64>,
    pub max_cpu_percentage: Option<f64>,
    pub max_disk_bytes: Option<u64>,
    pub max_network_bytes_per_sec: Option<u64>,
    pub max_file_handles: Option<u32>,
    pub max_threads: Option<u32>,
    pub max_event_queue_size: Option<u32>,
    pub enforcement_enabled: bool,
    pub warning_threshold: f64, // Percentage of limit to trigger warning (0.0-1.0)
}

impl Default for ResourceLimits {
    fn default() -> Self {
        Self {
            max_memory_bytes: Some(100 * 1024 * 1024), // 100MB
            max_cpu_percentage: Some(10.0), // 10%
            max_disk_bytes: Some(1024 * 1024 * 1024), // 1GB
            max_network_bytes_per_sec: Some(1024 * 1024), // 1MB/s
            max_file_handles: Some(100),
            max_threads: Some(10),
            max_event_queue_size: Some(1000),
            enforcement_enabled: true,
            warning_threshold: 0.8, // Warn at 80% of limit
        }
    }
}

/// Plugin sandbox configuration
#[derive(Debug, Clone)]
pub struct PluginSandbox {
    pub plugin_id: String,
    pub permissions: PermissionSet,
    pub resource_limits: ResourceLimits,
    pub isolation_level: IsolationLevel,
    pub created_at: Instant,
    pub last_violation: Option<Instant>,
    pub violation_count: u32,
}

/// Sandbox isolation levels
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum IsolationLevel {
    /// No isolation - plugin runs with full access
    None,
    /// Basic isolation - limited permissions and resource monitoring
    Basic,
    /// Strict isolation - heavily restricted permissions and strict resource limits
    Strict,
    /// Complete isolation - plugin runs in completely isolated environment
    Complete,
}

/// Security violation types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityViolation {
    PermissionDenied {
        plugin_id: String,
        permission: Permission,
        attempted_action: String,
    },
    ResourceLimitExceeded {
        plugin_id: String,
        resource_type: String,
        current_usage: f64,
        limit: f64,
    },
    SandboxEscape {
        plugin_id: String,
        details: String,
    },
    UnauthorizedAccess {
        plugin_id: String,
        resource: String,
        details: String,
    },
}

/// Security manager implementation
pub struct SecurityManager {
    /// Plugin sandboxes by plugin ID
    sandboxes: RwLock<HashMap<String, PluginSandbox>>,
    
    /// Resource usage tracking
    resource_usage: RwLock<HashMap<String, ResourceUsage>>,
    
    /// Security violations log
    violations: RwLock<Vec<(Instant, SecurityViolation)>>,
    
    /// Global security settings
    global_settings: RwLock<SecuritySettings>,
    
    /// Permission validators
    validators: RwLock<Vec<Box<dyn PermissionValidator>>>,
    
    /// Resource monitor for continuous monitoring
    resource_monitor: Arc<ResourceMonitor>,
}

/// Resource monitor for continuous resource tracking
pub struct ResourceMonitor {
    /// Monitoring enabled flag
    enabled: RwLock<bool>,
    
    /// Monitoring interval
    interval: RwLock<Duration>,
    
    /// Resource collectors by plugin ID
    collectors: RwLock<HashMap<String, Box<dyn ResourceCollector>>>,
    
    /// Warning callbacks
    warning_callbacks: RwLock<Vec<Box<dyn Fn(&str, &ResourceUsage, &ResourceLimits) + Send + Sync>>>,
}

/// Resource collector trait for gathering plugin resource usage
pub trait ResourceCollector: Send + Sync {
    /// Collect current resource usage for the plugin
    fn collect_usage(&self, plugin_id: &str) -> Result<ResourceUsage, String>;
    
    /// Get collector name
    fn name(&self) -> &str;
}

impl ResourceMonitor {
    pub fn new() -> Self {
        Self {
            enabled: RwLock::new(true),
            interval: RwLock::new(Duration::from_secs(1)),
            collectors: RwLock::new(HashMap::new()),
            warning_callbacks: RwLock::new(Vec::new()),
        }
    }
    
    /// Add a resource collector for a plugin
    pub fn add_collector(&self, plugin_id: String, collector: Box<dyn ResourceCollector>) -> Result<(), String> {
        let mut collectors = self.collectors.write()
            .map_err(|_| "Failed to acquire collectors lock")?;
        collectors.insert(plugin_id, collector);
        Ok(())
    }
    
    /// Remove a resource collector
    pub fn remove_collector(&self, plugin_id: &str) -> Result<(), String> {
        let mut collectors = self.collectors.write()
            .map_err(|_| "Failed to acquire collectors lock")?;
        collectors.remove(plugin_id);
        Ok(())
    }
    
    /// Collect resource usage for all plugins
    pub fn collect_all_usage(&self) -> HashMap<String, ResourceUsage> {
        let collectors = match self.collectors.read() {
            Ok(collectors) => collectors,
            Err(_) => return HashMap::new(),
        };
        
        let mut usage_map = HashMap::new();
        
        for (plugin_id, collector) in collectors.iter() {
            match collector.collect_usage(plugin_id) {
                Ok(usage) => {
                    usage_map.insert(plugin_id.clone(), usage);
                },
                Err(e) => {
                    log::warn!("Failed to collect usage for plugin {}: {}", plugin_id, e);
                }
            }
        }
        
        usage_map
    }
    
    /// Add a warning callback
    pub fn add_warning_callback<F>(&self, callback: F) -> Result<(), String>
    where
        F: Fn(&str, &ResourceUsage, &ResourceLimits) + Send + Sync + 'static,
    {
        let mut callbacks = self.warning_callbacks.write()
            .map_err(|_| "Failed to acquire warning callbacks lock")?;
        callbacks.push(Box::new(callback));
        Ok(())
    }
    
    /// Trigger warning callbacks
    pub fn trigger_warnings(&self, plugin_id: &str, usage: &ResourceUsage, limits: &ResourceLimits) {
        if let Ok(callbacks) = self.warning_callbacks.read() {
            for callback in callbacks.iter() {
                callback(plugin_id, usage, limits);
            }
        }
    }
    
    /// Set monitoring enabled/disabled
    pub fn set_enabled(&self, enabled: bool) -> Result<(), String> {
        let mut enabled_flag = self.enabled.write()
            .map_err(|_| "Failed to acquire enabled lock")?;
        *enabled_flag = enabled;
        Ok(())
    }
    
    /// Check if monitoring is enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled.read().map(|e| *e).unwrap_or(false)
    }
}

/// Global security settings
#[derive(Debug, Clone)]
pub struct SecuritySettings {
    pub default_isolation_level: IsolationLevel,
    pub max_violations_before_shutdown: u32,
    pub violation_window: Duration,
    pub resource_monitoring_interval: Duration,
    pub strict_mode: bool,
}

impl Default for SecuritySettings {
    fn default() -> Self {
        Self {
            default_isolation_level: IsolationLevel::Basic,
            max_violations_before_shutdown: 5,
            violation_window: Duration::from_secs(5 * 60),
            resource_monitoring_interval: Duration::from_secs(1),
            strict_mode: false,
        }
    }
}

/// Permission validator trait
pub trait PermissionValidator: Send + Sync {
    /// Validate if a permission should be granted
    fn validate(&self, plugin_id: &str, permission: &Permission) -> bool;
    
    /// Get validator name
    fn name(&self) -> &str;
}

impl SecurityManager {
    /// Create a new security manager
    pub fn new() -> Self {
        Self {
            sandboxes: RwLock::new(HashMap::new()),
            resource_usage: RwLock::new(HashMap::new()),
            violations: RwLock::new(Vec::new()),
            global_settings: RwLock::new(SecuritySettings::default()),
            validators: RwLock::new(Vec::new()),
            resource_monitor: Arc::new(ResourceMonitor::new()),
        }
    }
    
    /// Get the resource monitor
    pub fn resource_monitor(&self) -> Arc<ResourceMonitor> {
        self.resource_monitor.clone()
    }

    /// Create a sandbox for a plugin
    pub fn create_sandbox(
        &self,
        plugin_id: &str,
        permissions: PermissionSet,
    ) -> PluginDockResult<()> {
        let settings = self.global_settings.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire settings lock".to_string()))?;
        
        let resource_limits = self.extract_resource_limits(&permissions);
        
        let sandbox = PluginSandbox {
            plugin_id: plugin_id.to_string(),
            permissions,
            resource_limits,
            isolation_level: settings.default_isolation_level,
            created_at: Instant::now(),
            last_violation: None,
            violation_count: 0,
        };
        
        {
            let mut sandboxes = self.sandboxes.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
            sandboxes.insert(plugin_id.to_string(), sandbox);
        }
        
        // Initialize resource usage tracking
        {
            let mut usage = self.resource_usage.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire resource usage lock".to_string()))?;
            usage.insert(plugin_id.to_string(), ResourceUsage::default());
        }
        
        Ok(())
    }

    /// Remove a sandbox for a plugin
    pub fn remove_sandbox(&self, plugin_id: &str) -> PluginDockResult<()> {
        {
            let mut sandboxes = self.sandboxes.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
            sandboxes.remove(plugin_id);
        }
        
        {
            let mut usage = self.resource_usage.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire resource usage lock".to_string()))?;
            usage.remove(plugin_id);
        }
        
        Ok(())
    }

    /// Validate permissions for a plugin
    pub fn validate_permissions(
        &self,
        plugin_id: &str,
        permissions: &PermissionSet,
    ) -> PluginDockResult<()> {
        let validators = self.validators.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire validators lock".to_string()))?;
        
        for permission in &permissions.permissions {
            // Check with all validators
            for validator in validators.iter() {
                if !validator.validate(plugin_id, permission) {
                    return Err(PluginDockError::Security(
                        format!("Permission {:?} denied by validator {}", permission, validator.name())
                    ));
                }
            }
        }
        
        Ok(())
    }

    /// Check if a plugin has a specific permission
    pub fn check_permission(
        &self,
        plugin_id: &str,
        permission: &Permission,
    ) -> PluginDockResult<bool> {
        let sandboxes = self.sandboxes.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
        
        if let Some(sandbox) = sandboxes.get(plugin_id) {
            Ok(sandbox.permissions.has_permission(permission))
        } else {
            Err(PluginDockError::PluginNotFound(plugin_id.to_string()))
        }
    }

    /// Check resource limits for a plugin
    pub fn check_resource_limits(
        &self,
        plugin_id: &str,
        usage: &ResourceUsage,
    ) -> PluginDockResult<()> {
        let sandboxes = self.sandboxes.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
        
        if let Some(sandbox) = sandboxes.get(plugin_id) {
            let violations = usage.exceeds_limits(&sandbox.resource_limits);
            
            if !violations.is_empty() {
                // Record violations
                for violation_msg in &violations {
                    self.record_violation(SecurityViolation::ResourceLimitExceeded {
                        plugin_id: plugin_id.to_string(),
                        resource_type: "multiple".to_string(),
                        current_usage: usage.memory_bytes as f64,
                        limit: sandbox.resource_limits.max_memory_bytes.unwrap_or(0) as f64,
                    })?;
                }
                
                return Err(PluginDockError::ResourceLimitExceeded(
                    format!("Resource limits exceeded: {}", violations.join(", "))
                ));
            }
            
            // Check for warnings (usage approaching limits)
            self.check_resource_warnings(plugin_id, usage, &sandbox.resource_limits)?;
        }
        
        Ok(())
    }
    
    /// Check if resource usage is approaching limits and trigger warnings
    fn check_resource_warnings(
        &self,
        plugin_id: &str,
        usage: &ResourceUsage,
        limits: &ResourceLimits,
    ) -> PluginDockResult<()> {
        let warning_threshold = limits.warning_threshold;
        
        // Check memory warning
        if let Some(limit) = limits.max_memory_bytes {
            let usage_ratio = usage.memory_bytes as f64 / limit as f64;
            if usage_ratio >= warning_threshold {
                self.resource_monitor.trigger_warnings(plugin_id, usage, limits);
            }
        }
        
        // Check CPU warning
        if let Some(limit) = limits.max_cpu_percentage {
            let usage_ratio = usage.cpu_percentage / limit;
            if usage_ratio >= warning_threshold {
                self.resource_monitor.trigger_warnings(plugin_id, usage, limits);
            }
        }
        
        // Check other resources...
        
        Ok(())
    }

    /// Update resource usage for a plugin
    pub fn update_resource_usage(
        &self,
        plugin_id: &str,
        usage: ResourceUsage,
    ) -> PluginDockResult<()> {
        // Check if usage exceeds limits
        self.check_resource_limits(plugin_id, &usage)?;
        
        // Update stored usage
        {
            let mut resource_usage = self.resource_usage.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire resource usage lock".to_string()))?;
            resource_usage.insert(plugin_id.to_string(), usage);
        }
        
        Ok(())
    }
    
    /// Start continuous resource monitoring for a plugin
    pub fn start_monitoring(&self, plugin_id: &str, collector: Box<dyn ResourceCollector>) -> PluginDockResult<()> {
        self.resource_monitor.add_collector(plugin_id.to_string(), collector)
            .map_err(|e| PluginDockError::Security(e))?;
        Ok(())
    }
    
    /// Stop resource monitoring for a plugin
    pub fn stop_monitoring(&self, plugin_id: &str) -> PluginDockResult<()> {
        self.resource_monitor.remove_collector(plugin_id)
            .map_err(|e| PluginDockError::Security(e))?;
        Ok(())
    }
    
    /// Collect current resource usage for all monitored plugins
    pub fn collect_all_resource_usage(&self) -> HashMap<String, ResourceUsage> {
        self.resource_monitor.collect_all_usage()
    }

    /// Record a security violation
    pub fn record_violation(&self, violation: SecurityViolation) -> PluginDockResult<()> {
        let plugin_id = match &violation {
            SecurityViolation::PermissionDenied { plugin_id, .. } |
            SecurityViolation::ResourceLimitExceeded { plugin_id, .. } |
            SecurityViolation::SandboxEscape { plugin_id, .. } |
            SecurityViolation::UnauthorizedAccess { plugin_id, .. } => plugin_id.clone(),
        };
        
        // Record violation
        {
            let mut violations = self.violations.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire violations lock".to_string()))?;
            violations.push((Instant::now(), violation.clone()));
        }
        
        // Update sandbox violation count
        {
            let mut sandboxes = self.sandboxes.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
            if let Some(sandbox) = sandboxes.get_mut(&plugin_id) {
                sandbox.violation_count += 1;
                sandbox.last_violation = Some(Instant::now());
            }
        }
        
        // Check if plugin should be shut down
        self.check_violation_threshold(&plugin_id)?;
        
        Ok(())
    }

    /// Get security violations for a plugin
    pub fn get_violations(&self, plugin_id: &str) -> PluginDockResult<Vec<SecurityViolation>> {
        let violations = self.violations.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire violations lock".to_string()))?;
        
        let plugin_violations = violations.iter()
            .filter_map(|(_, violation)| {
                let violation_plugin_id = match violation {
                    SecurityViolation::PermissionDenied { plugin_id, .. } |
                    SecurityViolation::ResourceLimitExceeded { plugin_id, .. } |
                    SecurityViolation::SandboxEscape { plugin_id, .. } |
                    SecurityViolation::UnauthorizedAccess { plugin_id, .. } => plugin_id,
                };
                
                if violation_plugin_id == plugin_id {
                    Some(violation.clone())
                } else {
                    None
                }
            })
            .collect();
        
        Ok(plugin_violations)
    }

    /// Add a permission validator
    pub fn add_validator(&self, validator: Box<dyn PermissionValidator>) -> PluginDockResult<()> {
        let mut validators = self.validators.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire validators lock".to_string()))?;
        validators.push(validator);
        Ok(())
    }

    /// Update global security settings
    pub fn update_settings(&self, settings: SecuritySettings) -> PluginDockResult<()> {
        let mut global_settings = self.global_settings.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire settings lock".to_string()))?;
        *global_settings = settings;
        Ok(())
    }

    /// Get current resource usage for a plugin
    pub fn get_resource_usage(&self, plugin_id: &str) -> PluginDockResult<ResourceUsage> {
        let usage = self.resource_usage.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire resource usage lock".to_string()))?;
        
        usage.get(plugin_id)
            .cloned()
            .ok_or_else(|| PluginDockError::PluginNotFound(plugin_id.to_string()))
    }

    /// Extract resource limits from permissions
    fn extract_resource_limits(&self, permissions: &PermissionSet) -> ResourceLimits {
        let mut limits = ResourceLimits::default();
        
        for permission in &permissions.permissions {
            match permission {
                Permission::MemoryLimit(bytes) => {
                    limits.max_memory_bytes = Some(*bytes);
                },
                Permission::CpuLimit(percentage) => {
                    limits.max_cpu_percentage = Some(*percentage as f64 / 100.0);
                },
                Permission::DiskLimit(bytes) => {
                    limits.max_disk_bytes = Some(*bytes);
                },
                Permission::NetworkBandwidth(bytes_per_sec) => {
                    limits.max_network_bytes_per_sec = Some(*bytes_per_sec);
                },
                _ => {}
            }
        }
        
        limits
    }

    /// Check if plugin has exceeded violation threshold
    fn check_violation_threshold(&self, plugin_id: &str) -> PluginDockResult<()> {
        let settings = self.global_settings.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire settings lock".to_string()))?;
        
        let sandboxes = self.sandboxes.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire sandboxes lock".to_string()))?;
        
        if let Some(sandbox) = sandboxes.get(plugin_id) {
            if sandbox.violation_count >= settings.max_violations_before_shutdown {
                return Err(PluginDockError::Security(
                    format!("Plugin {} exceeded maximum violations and should be shut down", plugin_id)
                ));
            }
        }
        
        Ok(())
    }
}

/// Default file system validator
pub struct FileSystemValidator {
    allowed_base_paths: Vec<String>,
}

impl FileSystemValidator {
    pub fn new(allowed_base_paths: Vec<String>) -> Self {
        Self { allowed_base_paths }
    }
}

impl PermissionValidator for FileSystemValidator {
    fn validate(&self, _plugin_id: &str, permission: &Permission) -> bool {
        match permission {
            Permission::ReadFile(path) | Permission::WriteFile(path) => {
                self.allowed_base_paths.iter().any(|base| path.starts_with(base))
            },
            _ => true, // Allow non-file-system permissions
        }
    }
    
    fn name(&self) -> &str {
        "FileSystemValidator"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_set_basic() {
        let permissions = PermissionSet::basic();
        
        assert!(permissions.has_permission(&Permission::ShowNotification));
        assert!(permissions.has_permission(&Permission::ReceiveMessage("test".to_string())));
        assert!(!permissions.has_permission(&Permission::NetworkAccess));
    }

    #[test]
    fn test_permission_wildcard() {
        let permissions = PermissionSet::new()
            .allow(Permission::ReadFile("/home/<USER>/*".to_string()));
        
        assert!(permissions.has_permission(&Permission::ReadFile("/home/<USER>/documents/file.txt".to_string())));
        assert!(!permissions.has_permission(&Permission::ReadFile("/etc/passwd".to_string())));
    }

    #[test]
    fn test_security_manager_sandbox_creation() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::basic();
        
        let result = security_manager.create_sandbox("test-plugin", permissions);
        assert!(result.is_ok());
        
        let has_permission = security_manager.check_permission(
            "test-plugin",
            &Permission::ShowNotification
        ).unwrap();
        assert!(has_permission);
    }

    #[test]
    fn test_resource_limit_checking() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::new()
            .allow(Permission::MemoryLimit(1024)); // 1KB limit
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Should pass
        let normal_usage = ResourceUsage {
            memory_bytes: 512,
            cpu_percentage: 0.0,
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 0,
            thread_count: 0,
            event_queue_size: 0,
            last_updated: Some(Instant::now()),
        };
        let result = security_manager.check_resource_limits("test-plugin", &normal_usage);
        assert!(result.is_ok());
        
        // Should fail
        let excessive_usage = ResourceUsage {
            memory_bytes: 2048,
            cpu_percentage: 0.0,
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 0,
            thread_count: 0,
            event_queue_size: 0,
            last_updated: Some(Instant::now()),
        };
        let result = security_manager.check_resource_limits("test-plugin", &excessive_usage);
        assert!(result.is_err());
    }

    #[test]
    fn test_file_system_validator() {
        let validator = FileSystemValidator::new(vec!["/home/<USER>".to_string()]);
        
        assert!(validator.validate("test", &Permission::ReadFile("/home/<USER>/file.txt".to_string())));
        assert!(!validator.validate("test", &Permission::ReadFile("/etc/passwd".to_string())));
        assert!(validator.validate("test", &Permission::ShowNotification)); // Non-FS permission
    }
}