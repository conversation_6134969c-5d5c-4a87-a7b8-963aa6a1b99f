//! Plugin Hot Reload Manager
//!
//! Implements hot-reload functionality, update notifications, and dependency resolution.

use super::{PluginDockError, PluginDockR<PERSON>ult, PluginEvent, PluginEventBus};

use std::{
    collections::{HashMap, HashSet},
    path::{Path, PathBuf},
    sync::{Arc, RwLock},
    time::{Duration, Instant, SystemTime},
};

use tokio::{
    fs,
    sync::{mpsc, watch},
    time::interval,
};

use notify::{Watcher, RecursiveMode, Event, EventKind};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Plugin update notification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateNotification {
    pub plugin_id: String,
    pub current_version: String,
    pub available_version: String,
    pub update_type: UpdateType,
    pub changelog: Option<String>,
    pub download_url: Option<String>,
    pub requires_restart: bool,
    pub timestamp: SystemTime,
}

/// Type of plugin update
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum UpdateType {
    Major,
    Minor,
    Patch,
    Hotfix,
    Security,
}

/// Plugin dependency information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginDependency {
    pub plugin_id: String,
    pub version_requirement: String,
    pub optional: bool,
    pub features: Vec<String>,
}

/// Dependency resolution result
#[derive(Debug, Clone)]
pub struct DependencyResolution {
    pub plugin_id: String,
    pub dependencies: Vec<ResolvedDependency>,
    pub conflicts: Vec<DependencyConflict>,
    pub missing: Vec<String>,
}

/// Resolved dependency
#[derive(Debug, Clone)]
pub struct ResolvedDependency {
    pub plugin_id: String,
    pub version: String,
    pub source: DependencySource,
}

/// Dependency conflict
#[derive(Debug, Clone)]
pub struct DependencyConflict {
    pub plugin_id: String,
    pub required_version: String,
    pub available_version: String,
    pub conflicting_plugins: Vec<String>,
}

/// Source of a dependency
#[derive(Debug, Clone)]
pub enum DependencySource {
    Local(PathBuf),
    Registry(String),
    Git { url: String, branch: Option<String> },
    Http(String),
}

/// Plugin manifest for hot reload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginManifest {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub dependencies: Vec<PluginDependency>,
    pub hot_reload_enabled: bool,
    pub watch_paths: Vec<String>,
    pub build_command: Option<String>,
    pub entry_point: String,
}

/// Hot reload configuration
#[derive(Debug, Clone)]
pub struct HotReloadConfig {
    pub enabled: bool,
    pub watch_interval: Duration,
    pub debounce_delay: Duration,
    pub max_reload_attempts: u32,
    pub auto_update_check: bool,
    pub update_check_interval: Duration,
}

impl Default for HotReloadConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            watch_interval: Duration::from_millis(500),
            debounce_delay: Duration::from_millis(100),
            max_reload_attempts: 3,
            auto_update_check: true,
            update_check_interval: Duration::from_secs(3600), // 1 hour
        }
    }
}

/// Hot reload manager implementation
pub struct HotReloadManager {
    /// Configuration
    config: RwLock<HotReloadConfig>,
    
    /// Plugin manifests by plugin ID
    manifests: RwLock<HashMap<String, PluginManifest>>,
    
    /// File watchers by plugin ID
    watchers: RwLock<HashMap<String, Box<dyn Watcher + Send>>>,
    
    /// Pending updates
    pending_updates: RwLock<HashMap<String, UpdateNotification>>,
    
    /// Dependency resolver
    dependency_resolver: Arc<DependencyResolver>,
    
    /// Event bus for notifications
    event_bus: Arc<PluginEventBus>,
    
    /// Update check sender
    update_check_sender: watch::Sender<bool>,
    
    /// Reload attempt counters
    reload_attempts: RwLock<HashMap<String, u32>>,
}

impl HotReloadManager {
    /// Create a new hot reload manager
    pub fn new() -> Self {
        let (update_check_sender, _) = watch::channel(false);
        
        Self {
            config: RwLock::new(HotReloadConfig::default()),
            manifests: RwLock::new(HashMap::new()),
            watchers: RwLock::new(HashMap::new()),
            pending_updates: RwLock::new(HashMap::new()),
            dependency_resolver: Arc::new(DependencyResolver::new()),
            event_bus: Arc::new(PluginEventBus::new()),
            update_check_sender,
            reload_attempts: RwLock::new(HashMap::new()),
        }
    }

    /// Register a plugin for hot reload
    pub async fn register_plugin(
        &self,
        plugin_id: String,
        manifest_path: PathBuf,
    ) -> PluginDockResult<()> {
        // Load plugin manifest
        let manifest = self.load_manifest(&manifest_path).await?;
        
        // Validate manifest
        if manifest.id != plugin_id {
            return Err(PluginDockError::HotReload(
                format!("Plugin ID mismatch: expected {}, got {}", plugin_id, manifest.id)
            ));
        }
        
        // Store manifest
        {
            let mut manifests = self.manifests.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
            manifests.insert(plugin_id.clone(), manifest.clone());
        }
        
        // Setup file watcher if hot reload is enabled
        if manifest.hot_reload_enabled {
            self.setup_file_watcher(&plugin_id, &manifest).await?;
        }
        
        // Reset reload attempt counter
        {
            let mut attempts = self.reload_attempts.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire reload attempts lock".to_string()))?;
            attempts.insert(plugin_id.clone(), 0);
        }
        
        Ok(())
    }

    /// Unregister a plugin from hot reload
    pub fn unregister_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        // Remove manifest
        {
            let mut manifests = self.manifests.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
            manifests.remove(plugin_id);
        }
        
        // Remove file watcher
        {
            let mut watchers = self.watchers.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire watchers lock".to_string()))?;
            watchers.remove(plugin_id);
        }
        
        // Remove pending updates
        {
            let mut updates = self.pending_updates.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire pending updates lock".to_string()))?;
            updates.remove(plugin_id);
        }
        
        // Remove reload attempts
        {
            let mut attempts = self.reload_attempts.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire reload attempts lock".to_string()))?;
            attempts.remove(plugin_id);
        }
        
        Ok(())
    }

    /// Trigger hot reload for a plugin
    pub async fn reload_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        let manifest = {
            let manifests = self.manifests.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
            manifests.get(plugin_id).cloned()
                .ok_or_else(|| PluginDockError::PluginNotFound(plugin_id.to_string()))?
        };
        
        // Check reload attempt limit
        let current_attempts = {
            let attempts = self.reload_attempts.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire reload attempts lock".to_string()))?;
            attempts.get(plugin_id).copied().unwrap_or(0)
        };
        
        let config = self.config.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire config lock".to_string()))?;
        
        if current_attempts >= config.max_reload_attempts {
            return Err(PluginDockError::HotReload(
                format!("Plugin {} exceeded maximum reload attempts ({})", plugin_id, config.max_reload_attempts)
            ));
        }
        
        // Increment attempt counter
        {
            let mut attempts = self.reload_attempts.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire reload attempts lock".to_string()))?;
            attempts.insert(plugin_id.to_string(), current_attempts + 1);
        }
        
        // Build plugin if build command is specified
        if let Some(build_command) = &manifest.build_command {
            self.build_plugin(plugin_id, build_command).await?;
        }
        
        // Resolve dependencies
        let resolution = self.dependency_resolver.resolve_dependencies(plugin_id, &manifest.dependencies).await?;
        
        if !resolution.conflicts.is_empty() {
            return Err(PluginDockError::HotReload(
                format!("Dependency conflicts found for plugin {}: {:?}", plugin_id, resolution.conflicts)
            ));
        }
        
        // Emit reload event
        self.event_bus.emit(PluginEvent::PluginCommand {
            plugin_id: plugin_id.to_string(),
            command: super::PluginCommand::Reload,
        })?;
        
        // Reset attempt counter on successful reload
        {
            let mut attempts = self.reload_attempts.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire reload attempts lock".to_string()))?;
            attempts.insert(plugin_id.to_string(), 0);
        }
        
        Ok(())
    }

    /// Check for plugin updates
    pub async fn check_updates(&self) -> PluginDockResult<Vec<UpdateNotification>> {
        let manifests = {
            let manifests = self.manifests.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
            manifests.clone()
        };
        
        let mut updates = Vec::new();
        
        for (plugin_id, manifest) in manifests {
            if let Ok(update) = self.check_plugin_update(&plugin_id, &manifest).await {
                updates.push(update);
            }
        }
        
        // Store pending updates
        {
            let mut pending = self.pending_updates.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire pending updates lock".to_string()))?;
            for update in &updates {
                pending.insert(update.plugin_id.clone(), update.clone());
            }
        }
        
        Ok(updates)
    }

    /// Get pending updates for a plugin
    pub fn get_pending_updates(&self, plugin_id: &str) -> PluginDockResult<Option<UpdateNotification>> {
        let updates = self.pending_updates.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire pending updates lock".to_string()))?;
        Ok(updates.get(plugin_id).cloned())
    }

    /// Apply a plugin update
    pub async fn apply_update(&self, plugin_id: &str) -> PluginDockResult<()> {
        let update = {
            let updates = self.pending_updates.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire pending updates lock".to_string()))?;
            updates.get(plugin_id).cloned()
                .ok_or_else(|| PluginDockError::HotReload(format!("No pending update for plugin {}", plugin_id)))?
        };
        
        // Download and install update
        if let Some(download_url) = &update.download_url {
            self.download_and_install_update(plugin_id, download_url).await?;
        }
        
        // Reload plugin if it doesn't require restart
        if !update.requires_restart {
            self.reload_plugin(plugin_id).await?;
        }
        
        // Remove from pending updates
        {
            let mut updates = self.pending_updates.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire pending updates lock".to_string()))?;
            updates.remove(plugin_id);
        }
        
        // Emit update applied event
        self.event_bus.emit(PluginEvent::PluginCommand {
            plugin_id: plugin_id.to_string(),
            command: super::PluginCommand::UpdateConfig(serde_json::json!({
                "version": update.available_version,
                "updated_at": SystemTime::now()
            })),
        })?;
        
        Ok(())
    }

    /// Update hot reload configuration
    pub fn update_config(&self, config: HotReloadConfig) -> PluginDockResult<()> {
        let mut current_config = self.config.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire config lock".to_string()))?;
        *current_config = config;
        Ok(())
    }

    /// Get dependency resolver
    pub fn dependency_resolver(&self) -> Arc<DependencyResolver> {
        self.dependency_resolver.clone()
    }

    /// Start automatic update checking
    pub fn start_update_checker(&self) -> PluginDockResult<()> {
        let config = self.config.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire config lock".to_string()))?;
        
        if !config.auto_update_check {
            return Ok(());
        }
        
        let update_check_interval = config.update_check_interval;
        let mut update_check_receiver = self.update_check_sender.subscribe();
        let event_bus = self.event_bus.clone();
        let manager_weak = Arc::downgrade(&Arc::new(self.clone()));
        
        tokio::spawn(async move {
            let mut interval = interval(update_check_interval);
            
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        if let Some(manager) = manager_weak.upgrade() {
                            if let Ok(updates) = manager.check_updates().await {
                                for update in updates {
                                    // Emit update notification event
                                    let _ = event_bus.emit(PluginEvent::UpdateAvailable {
                                        plugin_id: update.plugin_id.clone(),
                                        current_version: update.current_version.clone(),
                                        available_version: update.available_version.clone(),
                                        update_type: update.update_type,
                                        requires_restart: update.requires_restart,
                                    });
                                }
                            }
                        } else {
                            break; // Manager dropped, exit loop
                        }
                    }
                    _ = update_check_receiver.changed() => {
                        // Manual update check triggered
                        if *update_check_receiver.borrow() {
                            if let Some(manager) = manager_weak.upgrade() {
                                if let Ok(updates) = manager.check_updates().await {
                                    for update in updates {
                                        let _ = event_bus.emit(PluginEvent::UpdateAvailable {
                                            plugin_id: update.plugin_id.clone(),
                                            current_version: update.current_version.clone(),
                                            available_version: update.available_version.clone(),
                                            update_type: update.update_type,
                                            requires_restart: update.requires_restart,
                                        });
                                    }
                                }
                            } else {
                                break; // Manager dropped, exit loop
                            }
                        }
                    }
                }
            }
        });
        
        Ok(())
    }

    /// Trigger manual update check
    pub fn trigger_update_check(&self) -> PluginDockResult<()> {
        self.update_check_sender.send(true)
            .map_err(|_| PluginDockError::HotReload("Failed to trigger update check".to_string()))?;
        Ok(())
    }

    /// Get plugin lifecycle events
    pub fn get_lifecycle_events(&self, plugin_id: &str) -> PluginDockResult<Vec<PluginLifecycleEvent>> {
        // This would typically be stored in a database or persistent storage
        // For now, return mock events
        Ok(vec![
            PluginLifecycleEvent {
                plugin_id: plugin_id.to_string(),
                event_type: LifecycleEventType::Registered,
                timestamp: SystemTime::now(),
                details: Some("Plugin registered successfully".to_string()),
            },
            PluginLifecycleEvent {
                plugin_id: plugin_id.to_string(),
                event_type: LifecycleEventType::Loaded,
                timestamp: SystemTime::now(),
                details: Some("Plugin loaded and initialized".to_string()),
            },
        ])
    }

    /// Record plugin lifecycle event
    pub fn record_lifecycle_event(&self, event: PluginLifecycleEvent) -> PluginDockResult<()> {
        // Emit lifecycle event
        self.event_bus.emit(PluginEvent::LifecycleEvent {
            plugin_id: event.plugin_id.clone(),
            event_type: event.event_type,
            timestamp: event.timestamp,
            details: event.details.clone(),
        })?;
        
        // In a real implementation, this would persist the event to storage
        log::info!("Plugin lifecycle event: {:?}", event);
        
        Ok(())
    }

    /// Disable plugin while maintaining configuration
    pub async fn disable_plugin(&self, plugin_id: &str, preserve_config: bool) -> PluginDockResult<()> {
        // Get current manifest
        let manifest = {
            let manifests = self.manifests.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
            manifests.get(plugin_id).cloned()
        };

        if let Some(manifest) = manifest {
            // Record lifecycle event
            self.record_lifecycle_event(PluginLifecycleEvent {
                plugin_id: plugin_id.to_string(),
                event_type: LifecycleEventType::Disabled,
                timestamp: SystemTime::now(),
                details: Some(format!("Plugin disabled, preserve_config: {}", preserve_config)),
            })?;

            // Emit disable event
            self.event_bus.emit(PluginEvent::PluginCommand {
                plugin_id: plugin_id.to_string(),
                command: super::PluginCommand::Shutdown,
            })?;

            // Remove from active manifests but preserve config if requested
            if !preserve_config {
                let mut manifests = self.manifests.write()
                    .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
                manifests.remove(plugin_id);
            }

            // Stop file watching
            {
                let mut watchers = self.watchers.write()
                    .map_err(|_| PluginDockError::Security("Failed to acquire watchers lock".to_string()))?;
                watchers.remove(plugin_id);
            }
        }

        Ok(())
    }

    /// Enable previously disabled plugin
    pub async fn enable_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        // Check if plugin has stored configuration
        let manifests = self.manifests.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire manifests lock".to_string()))?;
        
        if let Some(manifest) = manifests.get(plugin_id) {
            // Setup file watcher if hot reload is enabled
            if manifest.hot_reload_enabled {
                self.setup_file_watcher(plugin_id, manifest).await?;
            }

            // Record lifecycle event
            self.record_lifecycle_event(PluginLifecycleEvent {
                plugin_id: plugin_id.to_string(),
                event_type: LifecycleEventType::Enabled,
                timestamp: SystemTime::now(),
                details: Some("Plugin re-enabled".to_string()),
            })?;

            // Emit enable event
            self.event_bus.emit(PluginEvent::PluginCommand {
                plugin_id: plugin_id.to_string(),
                command: super::PluginCommand::Initialize,
            })?;
        } else {
            return Err(PluginDockError::PluginNotFound(format!("No configuration found for plugin {}", plugin_id)));
        }

        Ok(())
    }

    /// Load plugin manifest from file
    async fn load_manifest(&self, manifest_path: &Path) -> PluginDockResult<PluginManifest> {
        let content = fs::read_to_string(manifest_path).await
            .map_err(|e| PluginDockError::HotReload(format!("Failed to read manifest: {}", e)))?;
        
        let manifest: PluginManifest = toml::from_str(&content)
            .map_err(|e| PluginDockError::HotReload(format!("Failed to parse manifest: {}", e)))?;
        
        Ok(manifest)
    }

    /// Setup file watcher for a plugin
    async fn setup_file_watcher(
        &self,
        plugin_id: &str,
        manifest: &PluginManifest,
    ) -> PluginDockResult<()> {
        let (tx, mut rx) = mpsc::unbounded_channel();
        let plugin_id_clone = plugin_id.to_string();
        let event_bus = self.event_bus.clone();
        
        // Create file watcher
        let mut watcher = notify::recommended_watcher(move |res: Result<Event, notify::Error>| {
            if let Ok(event) = res {
                if matches!(event.kind, EventKind::Modify(_) | EventKind::Create(_)) {
                    let _ = tx.send(event);
                }
            }
        }).map_err(|e| PluginDockError::HotReload(format!("Failed to create watcher: {}", e)))?;
        
        // Watch specified paths
        for watch_path in &manifest.watch_paths {
            let path = Path::new(watch_path);
            watcher.watch(path, RecursiveMode::Recursive)
                .map_err(|e| PluginDockError::HotReload(format!("Failed to watch path {}: {}", watch_path, e)))?;
        }
        
        // Store watcher
        {
            let mut watchers = self.watchers.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire watchers lock".to_string()))?;
            watchers.insert(plugin_id.to_string(), Box::new(watcher));
        }
        
        // Spawn file change handler
        let config = self.config.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire config lock".to_string()))?;
        let debounce_delay = config.debounce_delay;
        
        tokio::spawn(async move {
            let mut last_event_time = Instant::now();
            
            while let Some(_event) = rx.recv().await {
                let now = Instant::now();
                
                // Debounce file changes
                if now.duration_since(last_event_time) > debounce_delay {
                    last_event_time = now;
                    
                    // Emit file change event
                    let _ = event_bus.emit(PluginEvent::PluginCommand {
                        plugin_id: plugin_id_clone.clone(),
                        command: super::PluginCommand::Reload,
                    });
                }
            }
        });
        
        Ok(())
    }

    /// Build plugin using build command
    async fn build_plugin(&self, plugin_id: &str, build_command: &str) -> PluginDockResult<()> {
        use tokio::process::Command;
        
        let output = Command::new("sh")
            .arg("-c")
            .arg(build_command)
            .output()
            .await
            .map_err(|e| PluginDockError::HotReload(format!("Failed to execute build command: {}", e)))?;
        
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PluginDockError::HotReload(
                format!("Build failed for plugin {}: {}", plugin_id, stderr)
            ));
        }
        
        Ok(())
    }

    /// Check for updates for a specific plugin
    async fn check_plugin_update(
        &self,
        plugin_id: &str,
        manifest: &PluginManifest,
    ) -> PluginDockResult<UpdateNotification> {
        // This is a simplified implementation
        // In a real system, this would check against a plugin registry or repository
        
        // For now, return a mock update notification
        Ok(UpdateNotification {
            plugin_id: plugin_id.to_string(),
            current_version: manifest.version.clone(),
            available_version: "1.0.1".to_string(), // Mock version
            update_type: UpdateType::Patch,
            changelog: Some("Bug fixes and improvements".to_string()),
            download_url: Some(format!("https://registry.example.com/plugins/{}/1.0.1", plugin_id)),
            requires_restart: false,
            timestamp: SystemTime::now(),
        })
    }

    /// Download and install plugin update
    async fn download_and_install_update(
        &self,
        plugin_id: &str,
        download_url: &str,
    ) -> PluginDockResult<()> {
        // This is a simplified implementation
        // In a real system, this would download the plugin package and install it
        
        log::info!("Downloading update for plugin {} from {}", plugin_id, download_url);
        
        // Mock download and installation
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        Ok(())
    }
}

/// Dependency resolver implementation
pub struct DependencyResolver {
    /// Local plugin registry
    local_registry: RwLock<HashMap<String, PluginManifest>>,
    
    /// Remote registry URLs
    remote_registries: RwLock<Vec<String>>,
}

impl DependencyResolver {
    /// Create a new dependency resolver
    pub fn new() -> Self {
        Self {
            local_registry: RwLock::new(HashMap::new()),
            remote_registries: RwLock::new(vec![
                "https://registry.matrix-ide.com".to_string(),
            ]),
        }
    }

    /// Resolve dependencies for a plugin
    pub async fn resolve_dependencies(
        &self,
        plugin_id: &str,
        dependencies: &[PluginDependency],
    ) -> PluginDockResult<DependencyResolution> {
        let mut resolved = Vec::new();
        let mut conflicts = Vec::new();
        let mut missing = Vec::new();
        
        for dependency in dependencies {
            match self.resolve_single_dependency(dependency).await {
                Ok(resolved_dep) => resolved.push(resolved_dep),
                Err(PluginDockError::PluginNotFound(_)) => {
                    if !dependency.optional {
                        missing.push(dependency.plugin_id.clone());
                    }
                },
                Err(_) => {
                    // Handle version conflicts
                    conflicts.push(DependencyConflict {
                        plugin_id: dependency.plugin_id.clone(),
                        required_version: dependency.version_requirement.clone(),
                        available_version: "unknown".to_string(),
                        conflicting_plugins: vec![plugin_id.to_string()],
                    });
                }
            }
        }
        
        Ok(DependencyResolution {
            plugin_id: plugin_id.to_string(),
            dependencies: resolved,
            conflicts,
            missing,
        })
    }

    /// Add a plugin to the local registry
    pub fn add_to_registry(&self, manifest: PluginManifest) -> PluginDockResult<()> {
        let mut registry = self.local_registry.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire registry lock".to_string()))?;
        registry.insert(manifest.id.clone(), manifest);
        Ok(())
    }

    /// Resolve a single dependency
    async fn resolve_single_dependency(
        &self,
        dependency: &PluginDependency,
    ) -> PluginDockResult<ResolvedDependency> {
        // Check local registry first
        {
            let registry = self.local_registry.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire registry lock".to_string()))?;
            
            if let Some(manifest) = registry.get(&dependency.plugin_id) {
                if self.version_satisfies(&manifest.version, &dependency.version_requirement) {
                    return Ok(ResolvedDependency {
                        plugin_id: dependency.plugin_id.clone(),
                        version: manifest.version.clone(),
                        source: DependencySource::Local(PathBuf::from("local")),
                    });
                }
            }
        }
        
        // Check remote registries
        // This is a simplified implementation
        Err(PluginDockError::PluginNotFound(dependency.plugin_id.clone()))
    }

    /// Check if a version satisfies a requirement
    fn version_satisfies(&self, version: &str, requirement: &str) -> bool {
        // Simplified version checking
        // In a real implementation, this would use proper semantic versioning
        version == requirement || requirement == "*"
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_hot_reload_manager_creation() {
        let manager = HotReloadManager::new();
        let config = HotReloadConfig::default();
        
        manager.update_config(config).unwrap();
    }

    #[tokio::test]
    async fn test_dependency_resolver() {
        let resolver = DependencyResolver::new();
        
        let manifest = PluginManifest {
            id: "test-plugin".to_string(),
            name: "Test Plugin".to_string(),
            version: "1.0.0".to_string(),
            description: "Test".to_string(),
            author: "Test Author".to_string(),
            dependencies: vec![],
            hot_reload_enabled: true,
            watch_paths: vec![],
            build_command: None,
            entry_point: "lib.rs".to_string(),
        };
        
        resolver.add_to_registry(manifest).unwrap();
        
        let dependencies = vec![
            PluginDependency {
                plugin_id: "test-plugin".to_string(),
                version_requirement: "1.0.0".to_string(),
                optional: false,
                features: vec![],
            }
        ];
        
        let resolution = resolver.resolve_dependencies("main-plugin", &dependencies).await.unwrap();
        assert_eq!(resolution.dependencies.len(), 1);
        assert_eq!(resolution.conflicts.len(), 0);
        assert_eq!(resolution.missing.len(), 0);
    }

    #[test]
    fn test_update_notification() {
        let notification = UpdateNotification {
            plugin_id: "test-plugin".to_string(),
            current_version: "1.0.0".to_string(),
            available_version: "1.0.1".to_string(),
            update_type: UpdateType::Patch,
            changelog: Some("Bug fixes".to_string()),
            download_url: None,
            requires_restart: false,
            timestamp: SystemTime::now(),
        };
        
        assert_eq!(notification.plugin_id, "test-plugin");
        assert_eq!(notification.update_type, UpdateType::Patch);
    }
}