//! Comprehensive tests for the plugin dock system

use super::*;
use crate::{
    core::UIEventBus,
    theme::ThemeManager,
    plugin_dock::{
        hot_reload::{UpdateType, UpdateNotification, PluginManifest, PluginDependency, HotReloadConfig},
        security::FileSystemValidator,
    },
};
use std::{time::SystemTime, sync::RwLock};

use std::sync::Arc;
use tokio::time::{timeout, Duration};

/// Create a test plugin dock manager
fn create_test_dock_manager() -> PluginDockManager {
    let ui_event_bus = Arc::new(UIEventBus::new().unwrap());
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    PluginDockManager::new(ui_event_bus, theme_manager)
}

/// Create test permissions
fn create_test_permissions() -> PermissionSet {
    PermissionSet::new()
        .allow(Permission::ShowNotification)
        .allow(Permission::MemoryLimit(50 * 1024 * 1024)) // 50MB
        .allow(Permission::CpuLimit(500)) // 5% (scaled by 100)
}

#[cfg(test)]
mod dock_manager_tests {
    use super::*;

    #[test]
    fn test_dock_manager_creation() {
        let dock_manager = create_test_dock_manager();
        let layout = dock_manager.get_layout();
        
        assert_eq!(layout.area, DockArea::Right);
        assert_eq!(layout.slots.len(), 0);
        assert!(layout.total_width > 0.0);
        assert!(layout.total_height > 0.0);
    }

    #[test]
    fn test_slot_allocation_and_deallocation() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        
        // Allocate slot
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        // Verify slot exists
        assert!(dock_manager.get_slot(slot_id).is_some());
        assert_eq!(dock_manager.get_plugin_slot("test-plugin"), Some(slot_id));
        
        // Verify layout updated
        let layout = dock_manager.get_layout();
        assert_eq!(layout.slots.len(), 1);
        
        // Deallocate slot
        dock_manager.deallocate_slot(slot_id)
            .expect("Should deallocate slot successfully");
        
        // Verify slot removed
        assert!(dock_manager.get_slot(slot_id).is_none());
        assert_eq!(dock_manager.get_plugin_slot("test-plugin"), None);
        
        // Verify layout updated
        let layout = dock_manager.get_layout();
        assert_eq!(layout.slots.len(), 0);
    }

    #[test]
    fn test_plugin_registration() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        let permissions = create_test_permissions();
        
        // Allocate slot
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        // Register plugin
        dock_manager.register_plugin("test-plugin".to_string(), slot_id, permissions)
            .expect("Should register plugin successfully");
        
        // Verify plugin registered
        let slot = dock_manager.get_slot(slot_id).unwrap();
        assert_eq!(slot.get_plugin_id(), Some("test-plugin".to_string()));
        
        // Unregister plugin
        dock_manager.unregister_plugin("test-plugin")
            .expect("Should unregister plugin successfully");
        
        // Verify plugin unregistered
        let slot = dock_manager.get_slot(slot_id).unwrap();
        assert_eq!(slot.get_plugin_id(), None);
    }

    #[test]
    fn test_slot_resizing() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        // Resize slot
        dock_manager.resize_slot(slot_id, (400.0, 300.0))
            .expect("Should resize slot successfully");
        
        let slot = dock_manager.get_slot(slot_id).unwrap();
        let position = slot.get_position();
        assert_eq!(position.width, 400.0);
        assert_eq!(position.height, 300.0);
    }

    #[test]
    fn test_max_slots_limit() {
        let dock_manager = create_test_dock_manager();
        let mut config = DockConfig::default();
        config.max_slots = 2;
        dock_manager.update_config(config).unwrap();
        
        let constraints = SizeConstraints::default();
        
        // Allocate maximum slots
        dock_manager.allocate_slot("plugin1".to_string(), constraints).unwrap();
        dock_manager.allocate_slot("plugin2".to_string(), constraints).unwrap();
        
        // Third allocation should fail
        let result = dock_manager.allocate_slot("plugin3".to_string(), constraints);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginDockError::SlotNotAvailable(_)));
    }

    #[test]
    fn test_resource_usage_tracking() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        let permissions = create_test_permissions();
        
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints).unwrap();
        dock_manager.register_plugin("test-plugin".to_string(), slot_id, permissions).unwrap();
        
        // Update resource usage within limits
        let result = dock_manager.update_resource_usage("test-plugin", 30.0 * 1024.0 * 1024.0); // 30MB
        assert!(result.is_ok());
        
        // Update resource usage exceeding limits
        let result = dock_manager.update_resource_usage("test-plugin", 100.0 * 1024.0 * 1024.0); // 100MB
        assert!(result.is_err());
    }
}

#[cfg(test)]
mod plugin_slot_tests {
    use super::*;

    fn create_test_slot() -> PluginSlot {
        let config = SlotConfig {
            plugin_id: None,
            constraints: SizeConstraints::default(),
            resizable: true,
            closable: true,
            title: "Test Slot".to_string(),
        };
        PluginSlot::new(SlotId::new(), config).unwrap()
    }

    #[test]
    fn test_slot_creation() {
        let slot = create_test_slot();
        assert_eq!(slot.get_state(), SlotState::Empty);
        assert!(slot.get_plugin_id().is_none());
        assert!(slot.is_visible());
    }

    #[test]
    fn test_plugin_assignment() {
        let mut slot = create_test_slot();
        
        slot.assign_plugin("test-plugin".to_string()).unwrap();
        assert_eq!(slot.get_plugin_id(), Some("test-plugin".to_string()));
        assert_eq!(slot.get_state(), SlotState::Loading);
        
        slot.activate().unwrap();
        assert_eq!(slot.get_state(), SlotState::Active);
    }

    #[test]
    fn test_plugin_unassignment() {
        let mut slot = create_test_slot();
        
        slot.assign_plugin("test-plugin".to_string()).unwrap();
        slot.unassign_plugin().unwrap();
        
        assert!(slot.get_plugin_id().is_none());
        assert_eq!(slot.get_state(), SlotState::Empty);
    }

    #[test]
    fn test_slot_resize_constraints() {
        let mut slot = create_test_slot();
        
        // Valid resize
        slot.resize(400.0, 300.0).unwrap();
        let pos = slot.get_position();
        assert_eq!(pos.width, 400.0);
        assert_eq!(pos.height, 300.0);
        
        // Invalid resize (below minimum)
        let result = slot.resize(100.0, 50.0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginSlotError::SizeConstraintViolation(_)));
    }

    #[test]
    fn test_state_transitions() {
        let slot = create_test_slot();
        
        // Valid transitions
        assert!(slot.set_state(SlotState::Loading).is_ok());
        assert!(slot.set_state(SlotState::Active).is_ok());
        assert!(slot.set_state(SlotState::Error).is_ok());
        
        // Invalid transition
        let result = slot.set_state(SlotState::Resizing);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginSlotError::InvalidStateTransition { .. }));
    }

    #[test]
    fn test_error_handling() {
        let slot = create_test_slot();
        
        slot.set_error("Test error message".to_string()).unwrap();
        assert_eq!(slot.get_state(), SlotState::Error);
        assert_eq!(slot.get_error_message(), Some("Test error message".to_string()));
    }
}

#[cfg(test)]
mod event_bus_tests {
    use super::*;
    use async_trait::async_trait;

    struct TestEventHandler {
        plugin_id: String,
        received_events: Arc<RwLock<Vec<PluginEvent>>>,
    }

    impl TestEventHandler {
        fn new(plugin_id: String) -> Self {
            Self {
                plugin_id,
                received_events: Arc::new(RwLock::new(Vec::new())),
            }
        }
        
        fn get_received_events(&self) -> Vec<PluginEvent> {
            self.received_events.read().unwrap().clone()
        }
    }

    #[async_trait]
    impl PluginEventHandler for TestEventHandler {
        async fn handle_event(&self, event: PluginEvent) -> PluginDockResult<()> {
            self.received_events.write().unwrap().push(event);
            Ok(())
        }
        
        fn plugin_id(&self) -> &str {
            &self.plugin_id
        }
    }

    #[tokio::test]
    async fn test_plugin_registration() {
        let event_bus = PluginEventBus::new();
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        let test_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        
        event_bus.emit(test_event.clone()).unwrap();
        
        let received = timeout(Duration::from_millis(100), receiver.recv()).await;
        assert!(received.is_ok());
        
        if let Ok(Some(event)) = received {
            match event {
                PluginEvent::PluginCommand { plugin_id, .. } => {
                    assert_eq!(plugin_id, "test-plugin");
                },
                _ => panic!("Unexpected event type"),
            }
        }
    }

    #[tokio::test]
    async fn test_event_filtering() {
        let event_bus = PluginEventBus::new();
        let mut security_filter = SecurityFilter::new();
        security_filter.allow_command("test-plugin".to_string(), "Initialize".to_string());
        
        event_bus.add_filter(Box::new(security_filter)).unwrap();
        
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Send allowed command
        let allowed_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        event_bus.emit(allowed_event).unwrap();
        
        // Send disallowed command
        let disallowed_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Shutdown,
        };
        event_bus.emit(disallowed_event).unwrap();
        
        // Should only receive the allowed event
        let received = timeout(Duration::from_millis(100), receiver.recv()).await;
        assert!(received.is_ok());
        
        // Should not receive the disallowed event
        let not_received = timeout(Duration::from_millis(50), receiver.recv()).await;
        assert!(not_received.is_err()); // Timeout expected
    }

    #[test]
    fn test_plugin_data_creation() {
        let data = PluginData::new(
            "test-data".to_string(),
            serde_json::json!({"key": "value"})
        ).with_metadata("source".to_string(), "test".to_string());
        
        assert_eq!(data.data_type, "test-data");
        assert_eq!(data.metadata.get("source"), Some(&"test".to_string()));
        assert!(data.timestamp > 0);
    }

    #[tokio::test]
    async fn test_broadcast_messaging() {
        let event_bus = PluginEventBus::new();
        
        let mut receiver1 = event_bus.register_plugin("plugin1".to_string()).unwrap();
        let mut receiver2 = event_bus.register_plugin("plugin2".to_string()).unwrap();
        
        let broadcast_event = PluginEvent::PluginMessage {
            from_plugin: "system".to_string(),
            to_plugin: None, // Broadcast
            message: PluginData::new("broadcast".to_string(), serde_json::json!({"msg": "hello"})),
        };
        
        event_bus.emit(broadcast_event).unwrap();
        
        // Both plugins should receive the broadcast
        let received1 = timeout(Duration::from_millis(100), receiver1.recv()).await;
        let received2 = timeout(Duration::from_millis(100), receiver2.recv()).await;
        
        assert!(received1.is_ok());
        assert!(received2.is_ok());
    }
}

#[cfg(test)]
mod security_tests {
    use super::*;

    #[test]
    fn test_permission_set_basic() {
        let permissions = PermissionSet::basic();
        
        assert!(permissions.has_permission(&Permission::ShowNotification));
        assert!(permissions.has_permission(&Permission::ReceiveMessage("test".to_string())));
        assert!(!permissions.has_permission(&Permission::NetworkAccess));
    }

    #[test]
    fn test_permission_wildcard() {
        let permissions = PermissionSet::new()
            .allow(Permission::ReadFile("/home/<USER>/*".to_string()));
        
        assert!(permissions.has_permission(&Permission::ReadFile("/home/<USER>/documents/file.txt".to_string())));
        assert!(!permissions.has_permission(&Permission::ReadFile("/etc/passwd".to_string())));
    }

    #[test]
    fn test_permission_denial() {
        let permissions = PermissionSet::new()
            .allow(Permission::ReadFile("*".to_string()))
            .deny(Permission::ReadFile("/etc/passwd".to_string()));
        
        assert!(permissions.has_permission(&Permission::ReadFile("/home/<USER>/file.txt".to_string())));
        assert!(!permissions.has_permission(&Permission::ReadFile("/etc/passwd".to_string())));
    }

    #[test]
    fn test_security_manager_sandbox_creation() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::basic();
        
        let result = security_manager.create_sandbox("test-plugin", permissions);
        assert!(result.is_ok());
        
        let has_permission = security_manager.check_permission(
            "test-plugin",
            &Permission::ShowNotification
        ).unwrap();
        assert!(has_permission);
    }

    #[test]
    fn test_resource_limit_checking() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::new()
            .allow(Permission::MemoryLimit(1024)); // 1KB limit
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Should pass
        let normal_usage = ResourceUsage {
            memory_bytes: 512,
            cpu_percentage: 0.0,
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 0,
            thread_count: 0,
            event_queue_size: 0,
            last_updated: Some(std::time::Instant::now()),
        };
        let result = security_manager.check_resource_limits("test-plugin", &normal_usage);
        assert!(result.is_ok());
        
        // Should fail
        let excessive_usage = ResourceUsage {
            memory_bytes: 2048,
            cpu_percentage: 0.0,
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 0,
            thread_count: 0,
            event_queue_size: 0,
            last_updated: Some(std::time::Instant::now()),
        };
        let result = security_manager.check_resource_limits("test-plugin", &excessive_usage);
        assert!(result.is_err());
    }

    #[test]
    fn test_file_system_validator() {
        let validator = FileSystemValidator::new(vec!["/home/<USER>".to_string()]);
        
        assert!(validator.validate("test", &Permission::ReadFile("/home/<USER>/file.txt".to_string())));
        assert!(!validator.validate("test", &Permission::ReadFile("/etc/passwd".to_string())));
        assert!(validator.validate("test", &Permission::ShowNotification)); // Non-FS permission
    }

    #[test]
    fn test_security_violation_recording() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::basic();
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        let violation = SecurityViolation::PermissionDenied {
            plugin_id: "test-plugin".to_string(),
            permission: Permission::NetworkAccess,
            attempted_action: "HTTP request".to_string(),
        };
        
        security_manager.record_violation(violation).unwrap();
        
        let violations = security_manager.get_violations("test-plugin").unwrap();
        assert_eq!(violations.len(), 1);
    }
}

#[cfg(test)]
mod hot_reload_tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_hot_reload_manager_creation() {
        let manager = HotReloadManager::new();
        let config = HotReloadConfig::default();
        
        manager.update_config(config).unwrap();
    }

    #[tokio::test]
    async fn test_dependency_resolver() {
        let resolver = DependencyResolver::new();
        
        let manifest = PluginManifest {
            id: "test-plugin".to_string(),
            name: "Test Plugin".to_string(),
            version: "1.0.0".to_string(),
            description: "Test".to_string(),
            author: "Test Author".to_string(),
            dependencies: vec![],
            hot_reload_enabled: true,
            watch_paths: vec![],
            build_command: None,
            entry_point: "lib.rs".to_string(),
        };
        
        resolver.add_to_registry(manifest).unwrap();
        
        let dependencies = vec![
            PluginDependency {
                plugin_id: "test-plugin".to_string(),
                version_requirement: "1.0.0".to_string(),
                optional: false,
                features: vec![],
            }
        ];
        
        let resolution = resolver.resolve_dependencies("main-plugin", &dependencies).await.unwrap();
        assert_eq!(resolution.dependencies.len(), 1);
        assert_eq!(resolution.conflicts.len(), 0);
        assert_eq!(resolution.missing.len(), 0);
    }

    #[test]
    fn test_update_notification() {
        let notification = UpdateNotification {
            plugin_id: "test-plugin".to_string(),
            current_version: "1.0.0".to_string(),
            available_version: "1.0.1".to_string(),
            update_type: UpdateType::Patch,
            changelog: Some("Bug fixes".to_string()),
            download_url: None,
            requires_restart: false,
            timestamp: SystemTime::now(),
        };
        
        assert_eq!(notification.plugin_id, "test-plugin");
        assert_eq!(notification.update_type, UpdateType::Patch);
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[tokio::test]
    async fn test_full_plugin_lifecycle() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        let permissions = create_test_permissions();
        
        // 1. Allocate slot
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot");
        
        // 2. Register plugin
        dock_manager.register_plugin("test-plugin".to_string(), slot_id, permissions)
            .expect("Should register plugin");
        
        // 3. Verify plugin is active
        let slot = dock_manager.get_slot(slot_id).unwrap();
        assert_eq!(slot.get_plugin_id(), Some("test-plugin".to_string()));
        
        // 4. Update resource usage
        dock_manager.update_resource_usage("test-plugin", 10.0 * 1024.0 * 1024.0) // 10MB
            .expect("Should update resource usage");
        
        // 5. Resize slot
        dock_manager.resize_slot(slot_id, (500.0, 400.0))
            .expect("Should resize slot");
        
        // 6. Unregister plugin
        dock_manager.unregister_plugin("test-plugin")
            .expect("Should unregister plugin");
        
        // 7. Deallocate slot
        dock_manager.deallocate_slot(slot_id)
            .expect("Should deallocate slot");
        
        // 8. Verify cleanup
        assert!(dock_manager.get_slot(slot_id).is_none());
        assert_eq!(dock_manager.get_plugin_slot("test-plugin"), None);
    }

    #[tokio::test]
    async fn test_multiple_plugins_interaction() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        let permissions = create_test_permissions();
        
        // Allocate slots for multiple plugins
        let slot1 = dock_manager.allocate_slot("plugin1".to_string(), constraints).unwrap();
        let slot2 = dock_manager.allocate_slot("plugin2".to_string(), constraints).unwrap();
        let slot3 = dock_manager.allocate_slot("plugin3".to_string(), constraints).unwrap();
        
        // Register plugins
        dock_manager.register_plugin("plugin1".to_string(), slot1, permissions.clone()).unwrap();
        dock_manager.register_plugin("plugin2".to_string(), slot2, permissions.clone()).unwrap();
        dock_manager.register_plugin("plugin3".to_string(), slot3, permissions.clone()).unwrap();
        
        // Verify all plugins are active
        let active_slots = dock_manager.get_active_slots();
        assert_eq!(active_slots.len(), 3);
        
        // Test layout with multiple slots
        let layout = dock_manager.get_layout();
        assert_eq!(layout.slots.len(), 3);
        assert!(layout.total_height > constraints.preferred_height * 3.0);
        
        // Test resource usage for all plugins
        for plugin_id in ["plugin1", "plugin2", "plugin3"] {
            dock_manager.update_resource_usage(plugin_id, 5.0 * 1024.0 * 1024.0).unwrap(); // 5MB each
        }
        
        // Cleanup
        for plugin_id in ["plugin1", "plugin2", "plugin3"] {
            dock_manager.unregister_plugin(plugin_id).unwrap();
        }
        
        for slot_id in [slot1, slot2, slot3] {
            dock_manager.deallocate_slot(slot_id).unwrap();
        }
    }

    #[tokio::test]
    async fn test_security_and_event_bus_integration() {
        let dock_manager = create_test_dock_manager();
        let event_bus = dock_manager.event_bus();
        let security_manager = dock_manager.security_manager();
        
        // Register plugin for events
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Create sandbox
        let permissions = PermissionSet::basic();
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Send security event
        let security_event = PluginEvent::SecurityViolation {
            plugin_id: "test-plugin".to_string(),
            violation_type: "permission_denied".to_string(),
            details: "Attempted unauthorized file access".to_string(),
        };
        
        event_bus.emit(security_event).unwrap();
        
        // Verify event received
        let received = timeout(Duration::from_millis(100), receiver.recv()).await;
        assert!(received.is_ok());
        
        if let Ok(Some(PluginEvent::SecurityViolation { plugin_id, .. })) = received {
            assert_eq!(plugin_id, "test-plugin");
        } else {
            panic!("Expected security violation event");
        }
    }
}