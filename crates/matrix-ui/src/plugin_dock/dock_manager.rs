//! Plugin Dock Manager
//!
//! Implements the PluginDockManager with dynamic slot allocation and plugin lifecycle management.

use super::{
    PluginSlot, SlotId, SlotState, SlotConfig, PluginSlotError,
    PluginEventBus, PluginEvent, PluginCommand,
    SecurityManager, PermissionSet, ResourceUsage,
    HotReloadManager,
    DockArea, DockPosition, SizeConstraints, PluginDockError, PluginDockResult
};

use crate::{
    core::{UIEventBus, UIEvent},
    theme::ThemeManager,
};

use floem::{
    reactive::{RwSignal, ReadSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, v_stack, h_stack, empty, label, Decorators},
    style::{Style, Position},
    IntoView,
};

use std::{
    collections::HashMap,
    sync::{Arc, RwLock},
};

use uuid::Uuid;
use serde::{Serialize, Deserialize};

/// Plugin dock layout configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockLayout {
    pub area: DockArea,
    pub slots: Vec<SlotLayout>,
    pub total_width: f64,
    pub total_height: f64,
    pub spacing: f64,
}

/// Individual slot layout within the dock
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SlotLayout {
    pub slot_id: SlotId,
    pub position: DockPosition,
    pub constraints: SizeConstraints,
    pub visible: bool,
    pub order: usize,
}

/// Plugin dock configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockConfig {
    pub default_area: DockArea,
    pub max_slots: usize,
    pub auto_resize: bool,
    pub allow_reordering: bool,
    pub spacing: f64,
    pub padding: f64,
}

impl Default for DockConfig {
    fn default() -> Self {
        Self {
            default_area: DockArea::Right,
            max_slots: 10,
            auto_resize: true,
            allow_reordering: true,
            spacing: 8.0,
            padding: 12.0,
        }
    }
}

/// Plugin dock manager implementation
pub struct PluginDockManager {
    /// Reactive signal for plugin slots
    slots: RwSignal<HashMap<SlotId, PluginSlot>>,
    
    /// Reactive signal for dock layout
    layout: RwSignal<DockLayout>,
    
    /// Plugin event bus for communication
    event_bus: Arc<PluginEventBus>,
    
    /// Security manager for plugin isolation
    security_manager: Arc<SecurityManager>,
    
    /// Hot reload manager for plugin updates
    hot_reload_manager: Arc<HotReloadManager>,
    
    /// UI event bus for system integration
    ui_event_bus: Arc<UIEventBus>,
    
    /// Theme manager for styling
    theme_manager: Arc<ThemeManager>,
    
    /// Dock configuration
    config: RwSignal<DockConfig>,
    
    /// Plugin registry mapping plugin IDs to slot IDs
    plugin_registry: RwLock<HashMap<String, SlotId>>,
    
    /// Resource usage tracking
    resource_usage: RwSignal<HashMap<String, f64>>,
}

impl PluginDockManager {
    /// Create a new plugin dock manager
    pub fn new(
        ui_event_bus: Arc<UIEventBus>,
        theme_manager: Arc<ThemeManager>,
    ) -> Self {
        let config = DockConfig::default();
        let initial_layout = DockLayout {
            area: config.default_area,
            slots: Vec::new(),
            total_width: 300.0,
            total_height: 600.0,
            spacing: config.spacing,
        };

        Self {
            slots: create_rw_signal(HashMap::new()),
            layout: create_rw_signal(initial_layout),
            event_bus: Arc::new(PluginEventBus::new()),
            security_manager: Arc::new(SecurityManager::new()),
            hot_reload_manager: Arc::new(HotReloadManager::new()),
            ui_event_bus,
            theme_manager,
            config: create_rw_signal(config),
            plugin_registry: RwLock::new(HashMap::new()),
            resource_usage: create_rw_signal(HashMap::new()),
        }
    }

    /// Allocate a new plugin slot
    pub fn allocate_slot(
        &self,
        plugin_id: String,
        constraints: SizeConstraints,
    ) -> PluginDockResult<SlotId> {
        let slot_id = SlotId::new();
        
        // Check if we've reached the maximum number of slots
        let current_slots = self.slots.get();
        let config = self.config.get();
        
        if current_slots.len() >= config.max_slots {
            return Err(PluginDockError::SlotNotAvailable(
                format!("Maximum number of slots ({}) reached", config.max_slots)
            ));
        }

        // Create slot configuration
        let slot_config = SlotConfig {
            plugin_id: Some(plugin_id.clone()),
            constraints,
            resizable: true,
            closable: true,
            title: format!("Plugin: {}", plugin_id),
        };

        // Create the plugin slot
        let plugin_slot = PluginSlot::new(slot_id, slot_config)?;

        // Add to slots
        self.slots.update(|slots| {
            slots.insert(slot_id, plugin_slot);
        });

        // Register plugin to slot mapping
        {
            let mut registry = self.plugin_registry.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin registry lock".to_string()))?;
            registry.insert(plugin_id.clone(), slot_id);
        }

        // Update layout
        self.update_layout()?;

        // Emit slot allocation event
        self.event_bus.emit(PluginEvent::SlotAllocated {
            slot_id,
            plugin_id,
        })?;

        Ok(slot_id)
    }

    /// Deallocate a plugin slot
    pub fn deallocate_slot(&self, slot_id: SlotId) -> PluginDockResult<()> {
        // Get the plugin ID before removing the slot
        let plugin_id = {
            let slots = self.slots.get();
            slots.get(&slot_id)
                .and_then(|slot| slot.get_plugin_id())
        };

        // Remove from slots
        self.slots.update(|slots| {
            slots.remove(&slot_id);
        });

        // Remove from plugin registry if plugin was assigned
        if let Some(plugin_id) = plugin_id {
            let mut registry = self.plugin_registry.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin registry lock".to_string()))?;
            registry.remove(&plugin_id);

            // Emit slot deallocation event
            self.event_bus.emit(PluginEvent::SlotDeallocated {
                slot_id,
                plugin_id,
            })?;
        }

        // Update layout
        self.update_layout()?;

        Ok(())
    }

    /// Register a plugin in an existing slot
    pub fn register_plugin(
        &self,
        plugin_id: String,
        slot_id: SlotId,
        permissions: PermissionSet,
    ) -> PluginDockResult<()> {
        // Validate permissions through security manager
        self.security_manager.validate_permissions(&plugin_id, &permissions)?;

        // Update slot with plugin information
        self.slots.update(|slots| {
            if let Some(slot) = slots.get_mut(&slot_id) {
                slot.assign_plugin(plugin_id.clone());
            }
        });

        // Register in plugin registry
        {
            let mut registry = self.plugin_registry.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin registry lock".to_string()))?;
            registry.insert(plugin_id.clone(), slot_id);
        }

        // Setup security sandbox for the plugin
        self.security_manager.create_sandbox(&plugin_id, permissions)?;

        // Emit plugin registration event
        self.event_bus.emit(PluginEvent::PluginRegistered {
            plugin_id,
            slot_id,
        })?;

        Ok(())
    }

    /// Unregister a plugin from its slot
    pub fn unregister_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        // Find the slot for this plugin
        let slot_id = {
            let registry = self.plugin_registry.read()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin registry lock".to_string()))?;
            registry.get(plugin_id).copied()
        };

        if let Some(slot_id) = slot_id {
            // Remove plugin from slot
            self.slots.update(|slots| {
                if let Some(slot) = slots.get_mut(&slot_id) {
                    slot.unassign_plugin();
                }
            });

            // Remove from plugin registry
            {
                let mut registry = self.plugin_registry.write()
                    .map_err(|_| PluginDockError::Security("Failed to acquire plugin registry lock".to_string()))?;
                registry.remove(plugin_id);
            }

            // Remove security sandbox
            self.security_manager.remove_sandbox(plugin_id)?;

            // Emit plugin unregistration event
            self.event_bus.emit(PluginEvent::PluginUnregistered {
                plugin_id: plugin_id.to_string(),
                slot_id,
            })?;
        }

        Ok(())
    }

    /// Resize a plugin slot
    pub fn resize_slot(
        &self,
        slot_id: SlotId,
        new_size: (f64, f64),
    ) -> PluginDockResult<()> {
        // Update slot size
        self.slots.update(|slots| {
            if let Some(slot) = slots.get_mut(&slot_id) {
                slot.resize(new_size.0, new_size.1);
            }
        });

        // Update layout
        self.update_layout()?;

        // Emit resize event
        self.event_bus.emit(PluginEvent::SlotResized {
            slot_id,
            width: new_size.0,
            height: new_size.1,
        })?;

        Ok(())
    }

    /// Reposition a plugin slot
    pub fn reposition_slot(
        &self,
        slot_id: SlotId,
        new_position: (f64, f64),
    ) -> PluginDockResult<()> {
        // Check if reordering is allowed
        let config = self.config.get();
        if !config.allow_reordering {
            return Err(PluginDockError::Security("Slot reordering is disabled".to_string()));
        }

        // Update slot position
        self.slots.update(|slots| {
            if let Some(slot) = slots.get_mut(&slot_id) {
                slot.set_position(new_position.0, new_position.1);
            }
        });

        // Update layout
        self.update_layout()?;

        // Emit reposition event
        self.event_bus.emit(PluginEvent::SlotRepositioned {
            slot_id,
            x: new_position.0,
            y: new_position.1,
        })?;

        Ok(())
    }

    /// Get plugin slot by ID
    pub fn get_slot(&self, slot_id: SlotId) -> Option<PluginSlot> {
        let slots = self.slots.get();
        slots.get(&slot_id).cloned()
    }

    /// Get slot ID for a plugin
    pub fn get_plugin_slot(&self, plugin_id: &str) -> Option<SlotId> {
        let registry = self.plugin_registry.read().ok()?;
        registry.get(plugin_id).copied()
    }

    /// Get all active plugin slots
    pub fn get_active_slots(&self) -> Vec<(SlotId, PluginSlot)> {
        let slots = self.slots.get();
        slots.iter()
            .filter(|(_, slot)| slot.get_state() == SlotState::Active)
            .map(|(id, slot)| (*id, slot.clone()))
            .collect()
    }

    /// Update resource usage for a plugin
    pub fn update_resource_usage(&self, plugin_id: &str, memory_bytes: f64) -> PluginDockResult<()> {
        // Create resource usage struct
        let usage = ResourceUsage {
            memory_bytes: memory_bytes as u64,
            cpu_percentage: 0.0, // Would be collected from actual monitoring
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 0,
            thread_count: 0,
            event_queue_size: 0,
            last_updated: Some(std::time::Instant::now()),
        };
        
        // Check resource limits and update
        self.security_manager.update_resource_usage(plugin_id, usage)?;

        // Update local tracking
        self.resource_usage.update(|usage_map| {
            usage_map.insert(plugin_id.to_string(), memory_bytes);
        });

        Ok(())
    }

    /// Get current dock layout
    pub fn get_layout(&self) -> DockLayout {
        self.layout.get()
    }

    /// Update dock configuration
    pub fn update_config(&self, new_config: DockConfig) -> PluginDockResult<()> {
        self.config.set(new_config);
        self.update_layout()?;
        Ok(())
    }

    /// Get event bus for plugin communication
    pub fn event_bus(&self) -> Arc<PluginEventBus> {
        self.event_bus.clone()
    }

    /// Get security manager
    pub fn security_manager(&self) -> Arc<SecurityManager> {
        self.security_manager.clone()
    }

    /// Get hot reload manager
    pub fn hot_reload_manager(&self) -> Arc<HotReloadManager> {
        self.hot_reload_manager.clone()
    }

    /// Update the dock layout based on current slots
    fn update_layout(&self) -> PluginDockResult<()> {
        let slots = self.slots.get();
        let config = self.config.get();
        
        let mut slot_layouts = Vec::new();
        let mut current_y = config.padding;
        
        // Sort slots by their order
        let mut sorted_slots: Vec<_> = slots.iter().collect();
        sorted_slots.sort_by_key(|(_, slot)| slot.get_order());
        
        for (slot_id, slot) in sorted_slots {
            let constraints = slot.get_constraints();
            let position = DockPosition {
                x: config.padding,
                y: current_y,
                width: constraints.preferred_width,
                height: constraints.preferred_height,
            };
            
            slot_layouts.push(SlotLayout {
                slot_id: *slot_id,
                position,
                constraints,
                visible: slot.is_visible(),
                order: slot.get_order(),
            });
            
            current_y += constraints.preferred_height + config.spacing;
        }
        
        let total_height = current_y + config.padding;
        let total_width = slot_layouts.iter()
            .map(|layout| layout.position.width + 2.0 * config.padding)
            .max_by(|a, b| a.partial_cmp(b).unwrap())
            .unwrap_or(300.0);
        
        let new_layout = DockLayout {
            area: config.default_area,
            slots: slot_layouts,
            total_width,
            total_height,
            spacing: config.spacing,
        };
        
        self.layout.set(new_layout);
        Ok(())
    }
}

/// Floem view implementation for the plugin dock
impl PluginDockManager {
    /// Create the dock view
    pub fn create_view(&self) -> impl IntoView {
        let slots_signal = self.slots;
        let layout_signal = self.layout;
        
        container(
            v_stack((
                label(|| "Plugin Dock"),
                container(
                    label(move || {
                        let slots = slots_signal.get();
                        format!("Slots: {}", slots.len())
                    })
                )
            ))
        )
        .style(move |s| {
            let layout = layout_signal.get();
            s.width(layout.total_width)
                .height(layout.total_height)
                .background(floem::peniko::Color::rgb8(248, 248, 248))
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::UIEventBus;
    use crate::theme::ThemeManager;
    use std::sync::Arc;

    fn create_test_dock_manager() -> PluginDockManager {
        let ui_event_bus = Arc::new(UIEventBus::new().unwrap());
        let theme_manager = Arc::new(ThemeManager::new().unwrap());
        PluginDockManager::new(ui_event_bus, theme_manager)
    }

    #[test]
    fn test_slot_allocation() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        assert!(dock_manager.get_slot(slot_id).is_some());
        assert_eq!(dock_manager.get_plugin_slot("test-plugin"), Some(slot_id));
    }

    #[test]
    fn test_slot_deallocation() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        dock_manager.deallocate_slot(slot_id)
            .expect("Should deallocate slot successfully");
        
        assert!(dock_manager.get_slot(slot_id).is_none());
        assert_eq!(dock_manager.get_plugin_slot("test-plugin"), None);
    }

    #[test]
    fn test_plugin_registration() {
        let dock_manager = create_test_dock_manager();
        let constraints = SizeConstraints::default();
        let permissions = PermissionSet::default();
        
        let slot_id = dock_manager.allocate_slot("test-plugin".to_string(), constraints)
            .expect("Should allocate slot successfully");
        
        dock_manager.register_plugin("test-plugin".to_string(), slot_id, permissions)
            .expect("Should register plugin successfully");
        
        let slot = dock_manager.get_slot(slot_id).unwrap();
        assert_eq!(slot.get_plugin_id(), Some("test-plugin".to_string()));
    }

    #[test]
    fn test_max_slots_limit() {
        let dock_manager = create_test_dock_manager();
        let mut config = DockConfig::default();
        config.max_slots = 2;
        dock_manager.update_config(config).unwrap();
        
        let constraints = SizeConstraints::default();
        
        // Allocate maximum number of slots
        dock_manager.allocate_slot("plugin1".to_string(), constraints).unwrap();
        dock_manager.allocate_slot("plugin2".to_string(), constraints).unwrap();
        
        // Third allocation should fail
        let result = dock_manager.allocate_slot("plugin3".to_string(), constraints);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginDockError::SlotNotAvailable(_)));
    }
}