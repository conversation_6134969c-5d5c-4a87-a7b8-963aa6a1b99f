//! Plugin Dock System Architecture
//!
//! This module implements the plugin dock system that provides dynamic slot allocation,
//! plugin registration, lifecycle management, and secure communication between plugins.

pub mod dock_manager;
pub mod plugin_slot;
pub mod event_bus;
pub mod security;
pub mod hot_reload;

#[cfg(test)]
mod tests;

#[cfg(test)]
mod security_tests;

pub use dock_manager::{PluginDockManager, DockLayout, DockConfig};
pub use plugin_slot::{PluginSlot, SlotId, SlotState, SlotConfig, PluginSlotError};
pub use event_bus::{
    PluginEventBus, PluginEvent, PluginCommand, PluginData, PluginEventHandler,
    EventFilter, SecurityFilter, RateLimiter
};
pub use security::{
    SecurityManager, PermissionSet, PluginSandbox, ResourceLimits, ResourceUsage,
    Permission, SecurityViolation, IsolationLevel, ResourceMonitor, ResourceCollector,
    TrustLevel, SecurityContext
};
pub use hot_reload::{HotReloadManager, UpdateNotification, DependencyResolver};

use crate::error::UiError;
use floem::reactive::{RwSignal, ReadSignal};
use std::collections::HashMap;
use uuid::Uuid;
use serde::{Serialize, Deserialize};

/// Plugin dock system errors
#[derive(Debug, thiserror::Error)]
pub enum PluginDockError {
    #[error("UI error: {0}")]
    UiError(#[from] UiError),
    
    #[error("Plugin slot error: {0}")]
    PluginSlot(#[from] PluginSlotError),
    
    #[error("Security error: {0}")]
    Security(String),
    
    #[error("Hot reload error: {0}")]
    HotReload(String),
    
    #[error("Plugin not found: {0}")]
    PluginNotFound(String),
    
    #[error("Slot not available: {0}")]
    SlotNotAvailable(String),
    
    #[error("Resource limit exceeded: {0}")]
    ResourceLimitExceeded(String),
}

/// Plugin dock system result type
pub type PluginDockResult<T> = Result<T, PluginDockError>;

/// Plugin dock area enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DockArea {
    Right,
    Left,
    Top,
    Bottom,
}

/// Plugin dock position
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct DockPosition {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

/// Plugin dock size constraints
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct SizeConstraints {
    pub min_width: f64,
    pub min_height: f64,
    pub max_width: Option<f64>,
    pub max_height: Option<f64>,
    pub preferred_width: f64,
    pub preferred_height: f64,
}

impl Default for SizeConstraints {
    fn default() -> Self {
        Self {
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
            preferred_width: 300.0,
            preferred_height: 400.0,
        }
    }
}

/// Plugin dock system state
#[derive(Clone)]
pub struct PluginDockState {
    pub slots: ReadSignal<HashMap<SlotId, PluginSlot>>,
    pub layout: ReadSignal<DockLayout>,
    pub active_plugins: ReadSignal<Vec<String>>,
    pub resource_usage: ReadSignal<HashMap<String, f64>>,
}