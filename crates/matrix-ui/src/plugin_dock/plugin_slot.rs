//! Plugin Slot Implementation
//!
//! Implements individual plugin slots with resizing, positioning capabilities,
//! and plugin lifecycle management.

use super::{DockPosition, SizeConstraints, PluginDockError, PluginDockResult};

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, v_stack, h_stack, label, button, empty, Decorators},
    style::{Style, Background},
    View, IntoView,
};

use std::fmt;
use uuid::Uuid;
use serde::{Serialize, Deserialize};

/// Unique identifier for plugin slots
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SlotId(Uuid);

impl SlotId {
    /// Create a new slot ID
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    /// Get the inner UUID
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl fmt::Display for SlotId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "slot-{}", self.0)
    }
}

/// Plugin slot state enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SlotState {
    /// Slot is empty and available
    Empty,
    /// Slot has a plugin assigned and is active
    Active,
    /// Slot is loading a plugin
    Loading,
    /// Slot has an error
    Error,
    /// Slot is being resized
    Resizing,
    /// Slot is hidden
    Hidden,
}

/// Plugin slot configuration
#[derive(Debug, Clone)]
pub struct SlotConfig {
    /// Plugin ID assigned to this slot (if any)
    pub plugin_id: Option<String>,
    /// Size constraints for the slot
    pub constraints: SizeConstraints,
    /// Whether the slot can be resized
    pub resizable: bool,
    /// Whether the slot can be closed
    pub closable: bool,
    /// Title displayed in the slot header
    pub title: String,
}

/// Plugin slot errors
#[derive(Debug, thiserror::Error)]
pub enum PluginSlotError {
    #[error("Invalid slot state transition from {from:?} to {to:?}")]
    InvalidStateTransition { from: SlotState, to: SlotState },
    
    #[error("Plugin already assigned to slot: {0}")]
    PluginAlreadyAssigned(String),
    
    #[error("No plugin assigned to slot")]
    NoPluginAssigned,
    
    #[error("Size constraint violation: {0}")]
    SizeConstraintViolation(String),
    
    #[error("Slot operation not allowed in current state: {0:?}")]
    OperationNotAllowed(SlotState),
    
    #[error("Resize operation failed: {0}")]
    ResizeFailed(String),
}

/// Plugin slot implementation
#[derive(Debug, Clone)]
pub struct PluginSlot {
    /// Unique slot identifier
    id: SlotId,
    
    /// Current slot state
    state: RwSignal<SlotState>,
    
    /// Plugin ID assigned to this slot
    plugin_id: RwSignal<Option<String>>,
    
    /// Current position of the slot
    position: RwSignal<DockPosition>,
    
    /// Current size constraints
    constraints: RwSignal<SizeConstraints>,
    
    /// Slot configuration
    config: RwSignal<SlotConfig>,
    
    /// Whether the slot is visible
    visible: RwSignal<bool>,
    
    /// Display order in the dock
    order: RwSignal<usize>,
    
    /// Error message (if any)
    error_message: RwSignal<Option<String>>,
}

impl PluginSlot {
    /// Create a new plugin slot
    pub fn new(id: SlotId, config: SlotConfig) -> PluginDockResult<Self> {
        let initial_position = DockPosition {
            x: 0.0,
            y: 0.0,
            width: config.constraints.preferred_width,
            height: config.constraints.preferred_height,
        };

        let initial_state = if config.plugin_id.is_some() {
            SlotState::Loading
        } else {
            SlotState::Empty
        };

        Ok(Self {
            id,
            state: create_rw_signal(initial_state),
            plugin_id: create_rw_signal(config.plugin_id.clone()),
            position: create_rw_signal(initial_position),
            constraints: create_rw_signal(config.constraints),
            config: create_rw_signal(config),
            visible: create_rw_signal(true),
            order: create_rw_signal(0),
            error_message: create_rw_signal(None),
        })
    }

    /// Get the slot ID
    pub fn id(&self) -> SlotId {
        self.id
    }

    /// Get the current state
    pub fn get_state(&self) -> SlotState {
        self.state.get()
    }

    /// Set the slot state
    pub fn set_state(&self, new_state: SlotState) -> Result<(), PluginSlotError> {
        let current_state = self.state.get();
        
        // Validate state transition
        if !self.is_valid_state_transition(current_state, new_state) {
            return Err(PluginSlotError::InvalidStateTransition {
                from: current_state,
                to: new_state,
            });
        }

        self.state.set(new_state);
        
        // Clear error message on successful state change
        if new_state != SlotState::Error {
            self.error_message.set(None);
        }

        Ok(())
    }

    /// Assign a plugin to this slot
    pub fn assign_plugin(&mut self, plugin_id: String) -> Result<(), PluginSlotError> {
        let current_plugin = self.plugin_id.get();
        
        if current_plugin.is_some() {
            return Err(PluginSlotError::PluginAlreadyAssigned(
                current_plugin.unwrap()
            ));
        }

        self.plugin_id.set(Some(plugin_id.clone()));
        self.set_state(SlotState::Loading)?;
        
        // Update slot title
        self.config.update(|config| {
            config.title = format!("Plugin: {}", plugin_id);
        });

        Ok(())
    }

    /// Unassign the plugin from this slot
    pub fn unassign_plugin(&mut self) -> Result<(), PluginSlotError> {
        if self.plugin_id.get().is_none() {
            return Err(PluginSlotError::NoPluginAssigned);
        }

        self.plugin_id.set(None);
        self.set_state(SlotState::Empty)?;
        
        // Reset slot title
        self.config.update(|config| {
            config.title = "Empty Slot".to_string();
        });

        Ok(())
    }

    /// Activate the slot (plugin loaded successfully)
    pub fn activate(&self) -> Result<(), PluginSlotError> {
        if self.plugin_id.get().is_none() {
            return Err(PluginSlotError::NoPluginAssigned);
        }

        self.set_state(SlotState::Active)
    }

    /// Set slot to error state
    pub fn set_error(&self, error_message: String) -> Result<(), PluginSlotError> {
        self.error_message.set(Some(error_message));
        self.set_state(SlotState::Error)
    }

    /// Resize the slot
    pub fn resize(&mut self, width: f64, height: f64) -> Result<(), PluginSlotError> {
        let constraints = self.constraints.get();
        
        // Validate size constraints
        if width < constraints.min_width {
            return Err(PluginSlotError::SizeConstraintViolation(
                format!("Width {} is below minimum {}", width, constraints.min_width)
            ));
        }
        
        if height < constraints.min_height {
            return Err(PluginSlotError::SizeConstraintViolation(
                format!("Height {} is below minimum {}", height, constraints.min_height)
            ));
        }
        
        if let Some(max_width) = constraints.max_width {
            if width > max_width {
                return Err(PluginSlotError::SizeConstraintViolation(
                    format!("Width {} exceeds maximum {}", width, max_width)
                ));
            }
        }
        
        if let Some(max_height) = constraints.max_height {
            if height > max_height {
                return Err(PluginSlotError::SizeConstraintViolation(
                    format!("Height {} exceeds maximum {}", height, max_height)
                ));
            }
        }

        // Update position with new size
        self.position.update(|pos| {
            pos.width = width;
            pos.height = height;
        });

        Ok(())
    }

    /// Set the slot position
    pub fn set_position(&mut self, x: f64, y: f64) {
        self.position.update(|pos| {
            pos.x = x;
            pos.y = y;
        });
    }

    /// Get the plugin ID assigned to this slot
    pub fn get_plugin_id(&self) -> Option<String> {
        self.plugin_id.get()
    }

    /// Get the current position
    pub fn get_position(&self) -> DockPosition {
        self.position.get()
    }

    /// Get the size constraints
    pub fn get_constraints(&self) -> SizeConstraints {
        self.constraints.get()
    }

    /// Check if the slot is visible
    pub fn is_visible(&self) -> bool {
        self.visible.get()
    }

    /// Set slot visibility
    pub fn set_visible(&self, visible: bool) {
        self.visible.set(visible);
    }

    /// Get the display order
    pub fn get_order(&self) -> usize {
        self.order.get()
    }

    /// Set the display order
    pub fn set_order(&self, order: usize) {
        self.order.set(order);
    }

    /// Get the current error message
    pub fn get_error_message(&self) -> Option<String> {
        self.error_message.get()
    }

    /// Check if a state transition is valid
    fn is_valid_state_transition(&self, from: SlotState, to: SlotState) -> bool {
        use SlotState::*;
        
        match (from, to) {
            // From Empty
            (Empty, Loading) => true,
            (Empty, Hidden) => true,
            
            // From Loading
            (Loading, Active) => true,
            (Loading, Error) => true,
            (Loading, Empty) => true,
            
            // From Active
            (Active, Loading) => true,
            (Active, Error) => true,
            (Active, Empty) => true,
            (Active, Hidden) => true,
            (Active, Resizing) => true,
            
            // From Error
            (Error, Loading) => true,
            (Error, Empty) => true,
            (Error, Hidden) => true,
            
            // From Resizing
            (Resizing, Active) => true,
            (Resizing, Error) => true,
            
            // From Hidden
            (Hidden, Empty) => true,
            (Hidden, Loading) => true,
            (Hidden, Active) => true,
            
            // Same state
            (state1, state2) if state1 == state2 => true,
            
            // All other transitions are invalid
            _ => false,
        }
    }
}

impl PartialEq for PluginSlot {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id &&
        self.state.get() == other.state.get() &&
        self.plugin_id.get() == other.plugin_id.get() &&
        self.position.get() == other.position.get() &&
        self.constraints.get() == other.constraints.get() &&
        self.visible.get() == other.visible.get() &&
        self.order.get() == other.order.get() &&
        self.error_message.get() == other.error_message.get()
    }
}

/// Floem view implementation for plugin slots
impl PluginSlot {
    /// Create the slot view
    pub fn create_view(&self) -> impl IntoView {
        let state_signal = self.state;
        let plugin_id_signal = self.plugin_id;
        let position_signal = self.position;

        container(
            v_stack((
                // Slot header
                label(move || {
                    format!("Slot: {:?}", state_signal.get())
                }),
                
                // Slot content
                label(move || {
                    match plugin_id_signal.get() {
                        Some(id) => format!("Plugin: {}", id),
                        None => "Empty Slot".to_string(),
                    }
                })
            ))
        )
        .style(move |s| {
            let pos = position_signal.get();
            s.width(pos.width)
                .height(pos.height)
                .border(1.0)
                .border_color(floem::peniko::Color::GRAY)
                .border_radius(4.0)
                .background(floem::peniko::Color::WHITE)
                .padding(8.0)
        })
    }


}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_slot() -> PluginSlot {
        let config = SlotConfig {
            plugin_id: None,
            constraints: SizeConstraints::default(),
            resizable: true,
            closable: true,
            title: "Test Slot".to_string(),
        };
        PluginSlot::new(SlotId::new(), config).unwrap()
    }

    #[test]
    fn test_slot_creation() {
        let slot = create_test_slot();
        assert_eq!(slot.get_state(), SlotState::Empty);
        assert!(slot.get_plugin_id().is_none());
        assert!(slot.is_visible());
    }

    #[test]
    fn test_plugin_assignment() {
        let mut slot = create_test_slot();
        
        slot.assign_plugin("test-plugin".to_string()).unwrap();
        assert_eq!(slot.get_plugin_id(), Some("test-plugin".to_string()));
        assert_eq!(slot.get_state(), SlotState::Loading);
        
        slot.activate().unwrap();
        assert_eq!(slot.get_state(), SlotState::Active);
    }

    #[test]
    fn test_plugin_unassignment() {
        let mut slot = create_test_slot();
        
        slot.assign_plugin("test-plugin".to_string()).unwrap();
        slot.unassign_plugin().unwrap();
        
        assert!(slot.get_plugin_id().is_none());
        assert_eq!(slot.get_state(), SlotState::Empty);
    }

    #[test]
    fn test_resize_constraints() {
        let mut slot = create_test_slot();
        
        // Valid resize
        slot.resize(400.0, 300.0).unwrap();
        let pos = slot.get_position();
        assert_eq!(pos.width, 400.0);
        assert_eq!(pos.height, 300.0);
        
        // Invalid resize (below minimum)
        let result = slot.resize(100.0, 50.0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginSlotError::SizeConstraintViolation(_)));
    }

    #[test]
    fn test_state_transitions() {
        let slot = create_test_slot();
        
        // Valid transitions
        assert!(slot.set_state(SlotState::Loading).is_ok());
        assert!(slot.set_state(SlotState::Active).is_ok());
        assert!(slot.set_state(SlotState::Error).is_ok());
        
        // Invalid transition
        let result = slot.set_state(SlotState::Resizing);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), PluginSlotError::InvalidStateTransition { .. }));
    }

    #[test]
    fn test_error_handling() {
        let slot = create_test_slot();
        
        slot.set_error("Test error message".to_string()).unwrap();
        assert_eq!(slot.get_state(), SlotState::Error);
        assert_eq!(slot.get_error_message(), Some("Test error message".to_string()));
    }
}