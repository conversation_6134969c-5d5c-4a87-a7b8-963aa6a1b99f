//! Comprehensive tests for plugin isolation and security boundaries

use super::*;
use crate::plugin_dock::{
    PluginEventBus, PluginEvent, PluginCommand, PluginData,
    SecurityManager, PermissionSet, Permission, ResourceUsage,
    SecurityViolation, TrustLevel, SecurityContext, SecurityFilter,
    FileSystemValidator,
};

use std::{
    sync::{Arc, atomic::{AtomicU32, Ordering}},
    time::{Duration, Instant},
    thread,
};
use tokio::time::timeout;

/// Mock resource collector for testing
struct MockResourceCollector {
    memory_usage: AtomicU32,
    cpu_usage: AtomicU32,
}

impl MockResourceCollector {
    fn new() -> Self {
        Self {
            memory_usage: AtomicU32::new(50 * 1024 * 1024), // 50MB
            cpu_usage: AtomicU32::new(5), // 5%
        }
    }
    
    fn set_memory_usage(&self, bytes: u32) {
        self.memory_usage.store(bytes, Ordering::Relaxed);
    }
    
    fn set_cpu_usage(&self, percentage: u32) {
        self.cpu_usage.store(percentage, Ordering::Relaxed);
    }
}

impl ResourceCollector for MockResourceCollector {
    fn collect_usage(&self, _plugin_id: &str) -> Result<ResourceUsage, String> {
        Ok(ResourceUsage {
            memory_bytes: self.memory_usage.load(Ordering::Relaxed) as u64,
            cpu_percentage: self.cpu_usage.load(Ordering::Relaxed) as f64,
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 10,
            thread_count: 3,
            event_queue_size: 50,
            last_updated: Some(Instant::now()),
        })
    }
    
    fn name(&self) -> &str {
        "MockResourceCollector"
    }
}

#[cfg(test)]
mod event_bus_security_tests {
    use super::*;

    #[tokio::test]
    async fn test_plugin_blocking() {
        let event_bus = PluginEventBus::new();
        
        // Register plugin
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Block the plugin
        event_bus.block_plugin("test-plugin").unwrap();
        
        // Try to register again - should fail
        let result = event_bus.register_plugin("test-plugin".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("blocked"));
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        let event_bus = PluginEventBus::new();
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Set very low trust level for strict rate limiting
        event_bus.set_plugin_trust_level("test-plugin", TrustLevel::Untrusted).unwrap();
        
        // Send events rapidly - should hit rate limit
        let mut success_count = 0;
        let mut rate_limited_count = 0;
        
        for i in 0..20 {
            let event = PluginEvent::PluginMessage {
                from_plugin: "test-plugin".to_string(),
                to_plugin: None,
                message: PluginData::new("test".to_string(), serde_json::json!({"id": i})),
            };
            
            match event_bus.emit(event) {
                Ok(_) => success_count += 1,
                Err(PluginDockError::Security(msg)) if msg.contains("Rate limit") => {
                    rate_limited_count += 1;
                },
                Err(e) => panic!("Unexpected error: {:?}", e),
            }
        }
        
        // Should have some rate limited events
        assert!(rate_limited_count > 0, "Expected some events to be rate limited");
        assert!(success_count > 0, "Expected some events to succeed");
        
        // Check statistics
        let stats = event_bus.get_statistics().unwrap();
        assert!(stats.rate_limited_events > 0);
    }

    #[tokio::test]
    async fn test_trust_level_permissions() {
        let event_bus = PluginEventBus::new();
        
        // Test different trust levels
        let trust_levels = [
            TrustLevel::Untrusted,
            TrustLevel::Basic,
            TrustLevel::Trusted,
            TrustLevel::System,
        ];
        
        for (i, trust_level) in trust_levels.iter().enumerate() {
            let plugin_id = format!("plugin-{}", i);
            let mut receiver = event_bus.register_plugin(plugin_id.clone()).unwrap();
            event_bus.set_plugin_trust_level(&plugin_id, *trust_level).unwrap();
            
            // Test event sending based on trust level
            let event = PluginEvent::PluginMessage {
                from_plugin: plugin_id.clone(),
                to_plugin: None,
                message: PluginData::new("test".to_string(), serde_json::json!({"trust": format!("{:?}", trust_level)})),
            };
            
            let result = event_bus.emit(event);
            
            // All trust levels should be able to send basic messages
            assert!(result.is_ok(), "Plugin with trust level {:?} should be able to send messages", trust_level);
        }
    }

    #[tokio::test]
    async fn test_event_filtering_security() {
        let event_bus = PluginEventBus::new();
        
        // Add a security filter that blocks certain commands
        let mut security_filter = SecurityFilter::new();
        security_filter.allow_command("trusted-plugin".to_string(), "Initialize".to_string());
        security_filter.allow_command("trusted-plugin".to_string(), "ExecuteAction".to_string());
        
        event_bus.add_filter(Box::new(security_filter)).unwrap();
        
        let mut receiver1 = event_bus.register_plugin("trusted-plugin".to_string()).unwrap();
        let mut receiver2 = event_bus.register_plugin("untrusted-plugin".to_string()).unwrap();
        
        // Send allowed command to trusted plugin
        let allowed_event = PluginEvent::PluginCommand {
            plugin_id: "trusted-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        event_bus.emit(allowed_event).unwrap();
        
        // Send disallowed command to untrusted plugin
        let disallowed_event = PluginEvent::PluginCommand {
            plugin_id: "untrusted-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        event_bus.emit(disallowed_event).unwrap();
        
        // Trusted plugin should receive the event
        let received = timeout(Duration::from_millis(100), receiver1.recv()).await;
        assert!(received.is_ok());
        
        // Untrusted plugin should not receive the event (filtered out)
        let not_received = timeout(Duration::from_millis(50), receiver2.recv()).await;
        assert!(not_received.is_err()); // Timeout expected
        
        // Check statistics
        let stats = event_bus.get_statistics().unwrap();
        assert!(stats.filtered_events > 0);
    }

    #[tokio::test]
    async fn test_plugin_isolation() {
        let event_bus = PluginEventBus::new();
        
        let mut receiver1 = event_bus.register_plugin("plugin1".to_string()).unwrap();
        let mut receiver2 = event_bus.register_plugin("plugin2".to_string()).unwrap();
        
        // Send direct message from plugin1 to plugin2
        let direct_message = PluginEvent::PluginMessage {
            from_plugin: "plugin1".to_string(),
            to_plugin: Some("plugin2".to_string()),
            message: PluginData::new("direct".to_string(), serde_json::json!({"msg": "hello"})),
        };
        
        event_bus.emit(direct_message).unwrap();
        
        // Plugin2 should receive the message
        let received = timeout(Duration::from_millis(100), receiver2.recv()).await;
        assert!(received.is_ok());
        
        // Plugin1 should not receive its own message
        let not_received = timeout(Duration::from_millis(50), receiver1.recv()).await;
        assert!(not_received.is_err());
        
        // Send broadcast message
        let broadcast_message = PluginEvent::PluginMessage {
            from_plugin: "plugin1".to_string(),
            to_plugin: None, // Broadcast
            message: PluginData::new("broadcast".to_string(), serde_json::json!({"msg": "hello all"})),
        };
        
        event_bus.emit(broadcast_message).unwrap();
        
        // Plugin2 should receive the broadcast
        let received = timeout(Duration::from_millis(100), receiver2.recv()).await;
        assert!(received.is_ok());
        
        // Plugin1 should not receive its own broadcast
        let not_received = timeout(Duration::from_millis(50), receiver1.recv()).await;
        assert!(not_received.is_err());
    }
}

#[cfg(test)]
mod security_manager_tests {
    use super::*;

    #[test]
    fn test_permission_validation() {
        let security_manager = SecurityManager::new();
        
        // Create permissions with file system access
        let permissions = PermissionSet::new()
            .allow(Permission::ReadFile("/home/<USER>/*".to_string()))
            .allow(Permission::WriteFile("/home/<USER>/documents/*".to_string()))
            .deny(Permission::ReadFile("/etc/passwd".to_string()));
        
        // Add file system validator
        let validator = FileSystemValidator::new(vec!["/home/<USER>".to_string()]);
        security_manager.add_validator(Box::new(validator)).unwrap();
        
        // Validate permissions - should succeed
        let result = security_manager.validate_permissions("test-plugin", &permissions);
        assert!(result.is_ok());
        
        // Create invalid permissions
        let invalid_permissions = PermissionSet::new()
            .allow(Permission::ReadFile("/etc/*".to_string()));
        
        // Validate invalid permissions - should fail
        let result = security_manager.validate_permissions("test-plugin", &invalid_permissions);
        assert!(result.is_err());
    }

    #[test]
    fn test_resource_limit_enforcement() {
        let security_manager = SecurityManager::new();
        
        // Create sandbox with strict limits
        let permissions = PermissionSet::new()
            .allow(Permission::MemoryLimit(10 * 1024 * 1024)) // 10MB
            .allow(Permission::CpuLimit(500)); // 5%
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Test usage within limits
        let normal_usage = ResourceUsage {
            memory_bytes: 5 * 1024 * 1024, // 5MB
            cpu_percentage: 3.0, // 3%
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 5,
            thread_count: 2,
            event_queue_size: 10,
            last_updated: Some(Instant::now()),
        };
        
        let result = security_manager.update_resource_usage("test-plugin", normal_usage);
        assert!(result.is_ok());
        
        // Test usage exceeding limits
        let excessive_usage = ResourceUsage {
            memory_bytes: 20 * 1024 * 1024, // 20MB (exceeds 10MB limit)
            cpu_percentage: 8.0, // 8% (exceeds 5% limit)
            disk_bytes: 0,
            network_bytes_per_sec: 0,
            file_handles: 5,
            thread_count: 2,
            event_queue_size: 10,
            last_updated: Some(Instant::now()),
        };
        
        let result = security_manager.update_resource_usage("test-plugin", excessive_usage);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Resource limits exceeded"));
    }

    #[test]
    fn test_security_violation_tracking() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::basic();
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Record multiple violations
        let violations = vec![
            SecurityViolation::PermissionDenied {
                plugin_id: "test-plugin".to_string(),
                permission: Permission::NetworkAccess,
                attempted_action: "HTTP request".to_string(),
            },
            SecurityViolation::ResourceLimitExceeded {
                plugin_id: "test-plugin".to_string(),
                resource_type: "memory".to_string(),
                current_usage: 200.0,
                limit: 100.0,
            },
            SecurityViolation::UnauthorizedAccess {
                plugin_id: "test-plugin".to_string(),
                resource: "/etc/passwd".to_string(),
                details: "Attempted to read system file".to_string(),
            },
        ];
        
        for violation in violations {
            security_manager.record_violation(violation).unwrap();
        }
        
        // Check recorded violations
        let recorded_violations = security_manager.get_violations("test-plugin").unwrap();
        assert_eq!(recorded_violations.len(), 3);
        
        // Check violation types
        let violation_types: Vec<_> = recorded_violations.iter().map(|v| {
            match v {
                SecurityViolation::PermissionDenied { .. } => "PermissionDenied",
                SecurityViolation::ResourceLimitExceeded { .. } => "ResourceLimitExceeded",
                SecurityViolation::UnauthorizedAccess { .. } => "UnauthorizedAccess",
                SecurityViolation::SandboxEscape { .. } => "SandboxEscape",
            }
        }).collect();
        
        assert!(violation_types.contains(&"PermissionDenied"));
        assert!(violation_types.contains(&"ResourceLimitExceeded"));
        assert!(violation_types.contains(&"UnauthorizedAccess"));
    }

    #[test]
    fn test_resource_monitoring() {
        let security_manager = SecurityManager::new();
        let permissions = PermissionSet::basic();
        
        security_manager.create_sandbox("test-plugin", permissions).unwrap();
        
        // Add mock resource collector
        let collector = Box::new(MockResourceCollector::new());
        security_manager.start_monitoring("test-plugin", collector).unwrap();
        
        // Collect resource usage
        let usage_map = security_manager.collect_all_resource_usage();
        assert!(usage_map.contains_key("test-plugin"));
        
        let usage = &usage_map["test-plugin"];
        assert_eq!(usage.memory_bytes, 50 * 1024 * 1024); // 50MB as set in mock
        assert_eq!(usage.cpu_percentage, 5.0); // 5% as set in mock
        
        // Stop monitoring
        security_manager.stop_monitoring("test-plugin").unwrap();
        
        // Should no longer collect usage
        let usage_map = security_manager.collect_all_resource_usage();
        assert!(!usage_map.contains_key("test-plugin"));
    }

    #[test]
    fn test_sandbox_isolation_levels() {
        let security_manager = SecurityManager::new();
        
        // Test different isolation levels
        let isolation_levels = [
            IsolationLevel::None,
            IsolationLevel::Basic,
            IsolationLevel::Strict,
            IsolationLevel::Complete,
        ];
        
        for (i, level) in isolation_levels.iter().enumerate() {
            let plugin_id = format!("plugin-{}", i);
            let mut permissions = PermissionSet::basic();
            
            // Adjust permissions based on isolation level
            match level {
                IsolationLevel::None => {
                    permissions = permissions.allow(Permission::NetworkAccess)
                        .allow(Permission::ProcessSpawn);
                },
                IsolationLevel::Basic => {
                    permissions = permissions.allow(Permission::ShowNotification);
                },
                IsolationLevel::Strict => {
                    permissions = permissions.deny(Permission::NetworkAccess)
                        .deny(Permission::ProcessSpawn);
                },
                IsolationLevel::Complete => {
                    permissions = PermissionSet::new(); // Minimal permissions
                },
            }
            
            let result = security_manager.create_sandbox(&plugin_id, permissions);
            assert!(result.is_ok(), "Failed to create sandbox with isolation level {:?}", level);
        }
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[tokio::test]
    async fn test_full_security_integration() {
        let event_bus = Arc::new(PluginEventBus::new());
        let security_manager = Arc::new(SecurityManager::new());
        
        // Setup plugins with different trust levels
        let plugins = vec![
            ("system-plugin", TrustLevel::System),
            ("trusted-plugin", TrustLevel::Trusted),
            ("basic-plugin", TrustLevel::Basic),
            ("untrusted-plugin", TrustLevel::Untrusted),
        ];
        
        let mut receivers = Vec::new();
        
        for (plugin_id, trust_level) in plugins {
            // Register plugin in event bus
            let receiver = event_bus.register_plugin(plugin_id.to_string()).unwrap();
            event_bus.set_plugin_trust_level(plugin_id, trust_level).unwrap();
            receivers.push((plugin_id, receiver));
            
            // Create security sandbox
            let permissions = match trust_level {
                TrustLevel::System => PermissionSet::new()
                    .allow(Permission::NetworkAccess)
                    .allow(Permission::ProcessSpawn)
                    .allow(Permission::ReadFile("*".to_string()))
                    .allow(Permission::WriteFile("*".to_string())),
                TrustLevel::Trusted => PermissionSet::new()
                    .allow(Permission::NetworkAccess)
                    .allow(Permission::ReadFile("/home/<USER>/*".to_string()))
                    .allow(Permission::WriteFile("/home/<USER>/*".to_string())),
                TrustLevel::Basic => PermissionSet::basic()
                    .allow(Permission::ReadFile("/home/<USER>/documents/*".to_string())),
                TrustLevel::Untrusted => PermissionSet::basic(),
            };
            
            security_manager.create_sandbox(plugin_id, permissions).unwrap();
            
            // Start resource monitoring
            let collector = Box::new(MockResourceCollector::new());
            security_manager.start_monitoring(plugin_id, collector).unwrap();
        }
        
        // Test cross-plugin communication with security constraints
        let system_message = PluginEvent::PluginMessage {
            from_plugin: "system-plugin".to_string(),
            to_plugin: Some("untrusted-plugin".to_string()),
            message: PluginData::new("system_command".to_string(), serde_json::json!({"cmd": "status"})),
        };
        
        event_bus.emit(system_message).unwrap();
        
        // Untrusted plugin should receive system message
        let (_, mut untrusted_receiver) = receivers.into_iter()
            .find(|(id, _)| *id == "untrusted-plugin")
            .unwrap();
        
        let received = timeout(Duration::from_millis(100), untrusted_receiver.recv()).await;
        assert!(received.is_ok());
        
        // Test resource monitoring
        let usage_map = security_manager.collect_all_resource_usage();
        assert_eq!(usage_map.len(), 4); // All plugins should be monitored
        
        // Test security violation handling
        let violation = SecurityViolation::PermissionDenied {
            plugin_id: "untrusted-plugin".to_string(),
            permission: Permission::NetworkAccess,
            attempted_action: "HTTP request to external API".to_string(),
        };
        
        security_manager.record_violation(violation).unwrap();
        
        let violations = security_manager.get_violations("untrusted-plugin").unwrap();
        assert_eq!(violations.len(), 1);
        
        // Cleanup
        for (plugin_id, _) in [("system-plugin", TrustLevel::System), ("trusted-plugin", TrustLevel::Trusted), ("basic-plugin", TrustLevel::Basic), ("untrusted-plugin", TrustLevel::Untrusted)] {
            event_bus.unregister_plugin(plugin_id).unwrap();
            security_manager.remove_sandbox(plugin_id).unwrap();
            security_manager.stop_monitoring(plugin_id).unwrap();
        }
    }

    #[tokio::test]
    async fn test_security_under_load() {
        let event_bus = Arc::new(PluginEventBus::new());
        let security_manager = Arc::new(SecurityManager::new());
        
        // Create multiple plugins
        let plugin_count = 10;
        let mut handles = Vec::new();
        
        for i in 0..plugin_count {
            let plugin_id = format!("load-test-plugin-{}", i);
            let event_bus = event_bus.clone();
            let security_manager = security_manager.clone();
            
            let handle = tokio::spawn(async move {
                // Register plugin
                let mut receiver = event_bus.register_plugin(plugin_id.clone()).unwrap();
                event_bus.set_plugin_trust_level(&plugin_id, TrustLevel::Basic).unwrap();
                
                // Create sandbox
                let permissions = PermissionSet::basic();
                security_manager.create_sandbox(&plugin_id, permissions).unwrap();
                
                // Send many events rapidly
                for j in 0..100 {
                    let event = PluginEvent::PluginMessage {
                        from_plugin: plugin_id.clone(),
                        to_plugin: None,
                        message: PluginData::new("load_test".to_string(), serde_json::json!({"id": j})),
                    };
                    
                    // Some events may be rate limited, which is expected
                    let _ = event_bus.emit(event);
                    
                    // Small delay to avoid overwhelming the system
                    tokio::time::sleep(Duration::from_millis(1)).await;
                }
                
                // Update resource usage periodically
                for _ in 0..10 {
                    let usage = ResourceUsage {
                        memory_bytes: (i + 1) * 10 * 1024 * 1024, // Varying memory usage
                        cpu_percentage: (i as f64 + 1.0) * 2.0,
                        disk_bytes: 0,
                        network_bytes_per_sec: 0,
                        file_handles: 10,
                        thread_count: 3,
                        event_queue_size: 50,
                        last_updated: Some(Instant::now()),
                    };
                    
                    let _ = security_manager.update_resource_usage(&plugin_id, usage);
                    tokio::time::sleep(Duration::from_millis(10)).await;
                }
                
                plugin_id
            });
            
            handles.push(handle);
        }
        
        // Wait for all tasks to complete
        let mut plugin_ids = Vec::new();
        for handle in handles {
            plugin_ids.push(handle.await.unwrap());
        }
        
        // Verify system integrity
        let stats = event_bus.get_statistics().unwrap();
        assert!(stats.total_events > 0);
        
        // Some events should have been rate limited under load
        assert!(stats.rate_limited_events > 0);
        
        // Cleanup
        for plugin_id in plugin_ids {
            event_bus.unregister_plugin(&plugin_id).unwrap();
            security_manager.remove_sandbox(&plugin_id).unwrap();
        }
    }
}