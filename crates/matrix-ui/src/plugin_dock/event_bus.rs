//! Plugin Event Bus
//!
//! Implements secure message passing between plugins and the plugin dock system.

use super::{SlotId, PluginDockError, PluginDockResult};

use std::{
    collections::{HashMap, HashSet},
    sync::{Arc, RwLock},
};

use tokio::sync::{broadcast, mpsc};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use async_trait::async_trait;
use std::time::{Duration, Instant};

/// Plugin event types for communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginEvent {
    // Slot management events
    SlotAllocated {
        slot_id: SlotId,
        plugin_id: String,
    },
    SlotDeallocated {
        slot_id: SlotId,
        plugin_id: String,
    },
    SlotResized {
        slot_id: SlotId,
        width: f64,
        height: f64,
    },
    SlotRepositioned {
        slot_id: SlotId,
        x: f64,
        y: f64,
    },
    
    // Plugin lifecycle events
    PluginRegistered {
        plugin_id: String,
        slot_id: SlotId,
    },
    PluginUnregistered {
        plugin_id: String,
        slot_id: SlotId,
    },
    PluginActivated {
        plugin_id: String,
        slot_id: SlotId,
    },
    PluginDeactivated {
        plugin_id: String,
        slot_id: SlotId,
    },
    
    // Plugin communication events
    PluginMessage {
        from_plugin: String,
        to_plugin: Option<String>, // None for broadcast
        message: PluginData,
    },
    PluginCommand {
        plugin_id: String,
        command: PluginCommand,
    },
    PluginResponse {
        plugin_id: String,
        request_id: Uuid,
        response: PluginData,
    },
    
    // System events
    SystemShutdown,
    ResourceLimitWarning {
        plugin_id: String,
        resource_type: String,
        current_usage: f64,
        limit: f64,
    },
    SecurityViolation {
        plugin_id: String,
        violation_type: String,
        details: String,
    },
}

/// Plugin command types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginCommand {
    Initialize,
    Shutdown,
    Pause,
    Resume,
    Reload,
    UpdateConfig(serde_json::Value),
    ExecuteAction {
        action: String,
        parameters: serde_json::Value,
    },
    RequestData {
        request_id: Uuid,
        data_type: String,
        query: serde_json::Value,
    },
}

/// Plugin data container for messages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginData {
    pub data_type: String,
    pub payload: serde_json::Value,
    pub timestamp: u64,
    pub metadata: HashMap<String, String>,
}

impl PluginData {
    /// Create new plugin data
    pub fn new(data_type: String, payload: serde_json::Value) -> Self {
        Self {
            data_type,
            payload,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            metadata: HashMap::new(),
        }
    }
    
    /// Add metadata to the plugin data
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// Plugin event handler trait
#[async_trait]
pub trait PluginEventHandler: Send + Sync {
    /// Handle a plugin event
    async fn handle_event(&self, event: PluginEvent) -> PluginDockResult<()>;
    
    /// Get the plugin ID this handler belongs to
    fn plugin_id(&self) -> &str;
    
    /// Check if this handler should receive the given event
    fn should_handle(&self, event: &PluginEvent) -> bool {
        true // Default: handle all events
    }
}

/// Plugin event subscription
pub struct PluginEventSubscription {
    pub id: Uuid,
    pub plugin_id: String,
    pub event_types: Vec<String>,
    pub handler: Arc<dyn PluginEventHandler>,
}

/// Plugin event bus implementation
pub struct PluginEventBus {
    /// Broadcast channel for global events
    global_sender: broadcast::Sender<PluginEvent>,
    
    /// Individual plugin channels
    plugin_channels: RwLock<HashMap<String, mpsc::UnboundedSender<PluginEvent>>>,
    
    /// Event subscriptions
    subscriptions: RwLock<HashMap<Uuid, PluginEventSubscription>>,
    
    /// Event handlers by plugin ID
    handlers: RwLock<HashMap<String, Vec<Arc<dyn PluginEventHandler>>>>,
    
    /// Message filtering rules
    filters: RwLock<Vec<Box<dyn EventFilter>>>,
    
    /// Event statistics
    stats: RwLock<EventStatistics>,
    
    /// Rate limiting for plugins
    rate_limits: RwLock<HashMap<String, RateLimiter>>,
    
    /// Message queue size limits per plugin
    queue_limits: RwLock<HashMap<String, usize>>,
    
    /// Security context for event validation
    security_context: Arc<SecurityContext>,
}

/// Event filtering trait
pub trait EventFilter: Send + Sync {
    /// Check if an event should be allowed
    fn should_allow(&self, event: &PluginEvent, from_plugin: &str, to_plugin: Option<&str>) -> bool;
    
    /// Get filter name for debugging
    fn name(&self) -> &str;
}

/// Event statistics tracking
#[derive(Debug, Default)]
pub struct EventStatistics {
    pub total_events: u64,
    pub events_by_type: HashMap<String, u64>,
    pub events_by_plugin: HashMap<String, u64>,
    pub filtered_events: u64,
    pub failed_events: u64,
    pub rate_limited_events: u64,
    pub queue_overflow_events: u64,
}

/// Rate limiter for plugin events
#[derive(Debug)]
pub struct RateLimiter {
    max_events_per_second: u32,
    window_start: Instant,
    event_count: u32,
    burst_allowance: u32,
}

impl RateLimiter {
    pub fn new(max_events_per_second: u32, burst_allowance: u32) -> Self {
        Self {
            max_events_per_second,
            window_start: Instant::now(),
            event_count: 0,
            burst_allowance,
        }
    }
    
    pub fn check_rate_limit(&mut self) -> bool {
        let now = Instant::now();
        let elapsed = now.duration_since(self.window_start);
        
        // Reset window if more than 1 second has passed
        if elapsed >= Duration::from_secs(1) {
            self.window_start = now;
            self.event_count = 0;
        }
        
        // Check if within rate limit (including burst allowance)
        if self.event_count < self.max_events_per_second + self.burst_allowance {
            self.event_count += 1;
            true
        } else {
            false
        }
    }
}

/// Security context for event validation
pub struct SecurityContext {
    /// Allowed event types per plugin
    allowed_events: RwLock<HashMap<String, HashSet<String>>>,
    
    /// Blocked plugins (temporarily or permanently)
    blocked_plugins: RwLock<HashSet<String>>,
    
    /// Plugin trust levels
    trust_levels: RwLock<HashMap<String, TrustLevel>>,
}

/// Plugin trust levels for security decisions
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TrustLevel {
    Untrusted,  // Heavily restricted
    Basic,      // Standard restrictions
    Trusted,    // Fewer restrictions
    System,     // Minimal restrictions (for system plugins)
}

impl SecurityContext {
    pub fn new() -> Self {
        Self {
            allowed_events: RwLock::new(HashMap::new()),
            blocked_plugins: RwLock::new(HashSet::new()),
            trust_levels: RwLock::new(HashMap::new()),
        }
    }
    
    pub fn set_plugin_trust_level(&self, plugin_id: &str, level: TrustLevel) -> Result<(), String> {
        let mut trust_levels = self.trust_levels.write()
            .map_err(|_| "Failed to acquire trust levels lock")?;
        trust_levels.insert(plugin_id.to_string(), level);
        Ok(())
    }
    
    pub fn get_plugin_trust_level(&self, plugin_id: &str) -> TrustLevel {
        self.trust_levels.read()
            .ok()
            .and_then(|levels| levels.get(plugin_id).copied())
            .unwrap_or(TrustLevel::Untrusted)
    }
    
    pub fn is_plugin_blocked(&self, plugin_id: &str) -> bool {
        self.blocked_plugins.read()
            .map(|blocked| blocked.contains(plugin_id))
            .unwrap_or(false)
    }
    
    pub fn block_plugin(&self, plugin_id: &str) -> Result<(), String> {
        let mut blocked = self.blocked_plugins.write()
            .map_err(|_| "Failed to acquire blocked plugins lock")?;
        blocked.insert(plugin_id.to_string());
        Ok(())
    }
    
    pub fn unblock_plugin(&self, plugin_id: &str) -> Result<(), String> {
        let mut blocked = self.blocked_plugins.write()
            .map_err(|_| "Failed to acquire blocked plugins lock")?;
        blocked.remove(plugin_id);
        Ok(())
    }
}

impl PluginEventBus {
    /// Create a new plugin event bus
    pub fn new() -> Self {
        let (global_sender, _) = broadcast::channel(1000);
        
        Self {
            global_sender,
            plugin_channels: RwLock::new(HashMap::new()),
            subscriptions: RwLock::new(HashMap::new()),
            handlers: RwLock::new(HashMap::new()),
            filters: RwLock::new(Vec::new()),
            stats: RwLock::new(EventStatistics::default()),
            rate_limits: RwLock::new(HashMap::new()),
            queue_limits: RwLock::new(HashMap::new()),
            security_context: Arc::new(SecurityContext::new()),
        }
    }
    
    /// Create a new plugin event bus with security context
    pub fn with_security_context(security_context: Arc<SecurityContext>) -> Self {
        let (global_sender, _) = broadcast::channel(1000);
        
        Self {
            global_sender,
            plugin_channels: RwLock::new(HashMap::new()),
            subscriptions: RwLock::new(HashMap::new()),
            handlers: RwLock::new(HashMap::new()),
            filters: RwLock::new(Vec::new()),
            stats: RwLock::new(EventStatistics::default()),
            rate_limits: RwLock::new(HashMap::new()),
            queue_limits: RwLock::new(HashMap::new()),
            security_context,
        }
    }

    /// Register a plugin for event communication
    pub fn register_plugin(&self, plugin_id: String) -> PluginDockResult<mpsc::UnboundedReceiver<PluginEvent>> {
        // Check if plugin is blocked
        if self.security_context.is_plugin_blocked(&plugin_id) {
            return Err(PluginDockError::Security(format!("Plugin {} is blocked", plugin_id)));
        }
        
        let (sender, receiver) = mpsc::unbounded_channel();
        
        {
            let mut channels = self.plugin_channels.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
            channels.insert(plugin_id.clone(), sender);
        }
        
        // Initialize handler list for this plugin
        {
            let mut handlers = self.handlers.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire handlers lock".to_string()))?;
            handlers.insert(plugin_id.clone(), Vec::new());
        }
        
        // Initialize rate limiter based on trust level
        {
            let trust_level = self.security_context.get_plugin_trust_level(&plugin_id);
            let (max_events, burst) = match trust_level {
                TrustLevel::Untrusted => (10, 5),   // Very limited
                TrustLevel::Basic => (50, 20),      // Standard limits
                TrustLevel::Trusted => (200, 50),   // Higher limits
                TrustLevel::System => (1000, 200),  // Minimal limits
            };
            
            let mut rate_limits = self.rate_limits.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire rate limits lock".to_string()))?;
            rate_limits.insert(plugin_id.clone(), RateLimiter::new(max_events, burst));
        }
        
        // Set queue size limit
        {
            let trust_level = self.security_context.get_plugin_trust_level(&plugin_id);
            let queue_limit = match trust_level {
                TrustLevel::Untrusted => 100,
                TrustLevel::Basic => 500,
                TrustLevel::Trusted => 1000,
                TrustLevel::System => 5000,
            };
            
            let mut queue_limits = self.queue_limits.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire queue limits lock".to_string()))?;
            queue_limits.insert(plugin_id, queue_limit);
        }
        
        Ok(receiver)
    }

    /// Unregister a plugin from event communication
    pub fn unregister_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        // Remove plugin channel
        {
            let mut channels = self.plugin_channels.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
            channels.remove(plugin_id);
        }
        
        // Remove plugin handlers
        {
            let mut handlers = self.handlers.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire handlers lock".to_string()))?;
            handlers.remove(plugin_id);
        }
        
        // Remove subscriptions for this plugin
        {
            let mut subscriptions = self.subscriptions.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire subscriptions lock".to_string()))?;
            subscriptions.retain(|_, sub| sub.plugin_id != plugin_id);
        }
        
        Ok(())
    }

    /// Subscribe to events with a handler
    pub fn subscribe(
        &self,
        plugin_id: String,
        event_types: Vec<String>,
        handler: Arc<dyn PluginEventHandler>,
    ) -> PluginDockResult<Uuid> {
        let subscription_id = Uuid::new_v4();
        
        let subscription = PluginEventSubscription {
            id: subscription_id,
            plugin_id: plugin_id.clone(),
            event_types,
            handler: handler.clone(),
        };
        
        // Add subscription
        {
            let mut subscriptions = self.subscriptions.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire subscriptions lock".to_string()))?;
            subscriptions.insert(subscription_id, subscription);
        }
        
        // Add handler to plugin handlers
        {
            let mut handlers = self.handlers.write()
                .map_err(|_| PluginDockError::Security("Failed to acquire handlers lock".to_string()))?;
            if let Some(plugin_handlers) = handlers.get_mut(&plugin_id) {
                plugin_handlers.push(handler);
            }
        }
        
        Ok(subscription_id)
    }

    /// Unsubscribe from events
    pub fn unsubscribe(&self, subscription_id: Uuid) -> PluginDockResult<()> {
        let mut subscriptions = self.subscriptions.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire subscriptions lock".to_string()))?;
        subscriptions.remove(&subscription_id);
        Ok(())
    }

    /// Emit an event to the bus
    pub fn emit(&self, event: PluginEvent) -> PluginDockResult<()> {
        // Check rate limits for the source plugin
        if let Some(source_plugin) = self.extract_source_plugin(&event) {
            if !self.check_rate_limit(&source_plugin)? {
                self.increment_rate_limited_stats();
                return Err(PluginDockError::Security(format!("Rate limit exceeded for plugin {}", source_plugin)));
            }
        }
        
        // Update statistics
        self.update_stats(&event);
        
        // Apply filters
        if !self.should_allow_event(&event) {
            self.increment_filtered_stats();
            return Ok(());
        }
        
        // Send to global broadcast channel
        if let Err(_) = self.global_sender.send(event.clone()) {
            // Channel might be full or have no receivers, which is okay
        }
        
        // Send to specific plugin channels based on event type
        self.route_event_to_plugins(event)?;
        
        Ok(())
    }

    /// Send a direct message to a specific plugin
    pub fn send_to_plugin(&self, plugin_id: &str, event: PluginEvent) -> PluginDockResult<()> {
        let channels = self.plugin_channels.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
        
        if let Some(sender) = channels.get(plugin_id) {
            sender.send(event)
                .map_err(|_| PluginDockError::Security("Failed to send event to plugin".to_string()))?;
        }
        
        Ok(())
    }

    /// Broadcast an event to all plugins
    pub fn broadcast(&self, event: PluginEvent) -> PluginDockResult<()> {
        let channels = self.plugin_channels.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
        
        for (plugin_id, sender) in channels.iter() {
            if let Err(_) = sender.send(event.clone()) {
                log::warn!("Failed to send broadcast event to plugin: {}", plugin_id);
            }
        }
        
        Ok(())
    }

    /// Add an event filter
    pub fn add_filter(&self, filter: Box<dyn EventFilter>) -> PluginDockResult<()> {
        let mut filters = self.filters.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire filters lock".to_string()))?;
        filters.push(filter);
        Ok(())
    }

    /// Get event statistics
    pub fn get_statistics(&self) -> PluginDockResult<EventStatistics> {
        let stats = self.stats.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire stats lock".to_string()))?;
        Ok(EventStatistics {
            total_events: stats.total_events,
            events_by_type: stats.events_by_type.clone(),
            events_by_plugin: stats.events_by_plugin.clone(),
            filtered_events: stats.filtered_events,
            failed_events: stats.failed_events,
            rate_limited_events: stats.rate_limited_events,
            queue_overflow_events: stats.queue_overflow_events,
        })
    }

    /// Get global event receiver
    pub fn subscribe_global(&self) -> broadcast::Receiver<PluginEvent> {
        self.global_sender.subscribe()
    }

    /// Route event to appropriate plugins
    fn route_event_to_plugins(&self, event: PluginEvent) -> PluginDockResult<()> {
        match event.clone() {
            PluginEvent::PluginMessage { from_plugin, to_plugin, .. } => {
                if let Some(target) = &to_plugin {
                    // Direct message to specific plugin
                    self.send_to_plugin(target, event.clone())?;
                } else {
                    // Broadcast to all plugins except sender
                    let channels = self.plugin_channels.read()
                        .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
                    
                    for (plugin_id, sender) in channels.iter() {
                        if plugin_id != &from_plugin {
                            let _ = sender.send(event.clone());
                        }
                    }
                }
            },
            PluginEvent::PluginCommand { plugin_id, .. } |
            PluginEvent::PluginResponse { plugin_id, .. } => {
                // Send to specific plugin
                self.send_to_plugin(&plugin_id, event.clone())?;
            },
            _ => {
                // Broadcast system events to all plugins
                self.broadcast(event.clone())?;
            }
        }
        
        Ok(())
    }

    /// Check if event should be allowed through filters
    fn should_allow_event(&self, event: &PluginEvent) -> bool {
        let filters = match self.filters.read() {
            Ok(filters) => filters,
            Err(_) => return true, // Allow if we can't acquire lock
        };
        
        let (from_plugin, to_plugin) = match event {
            PluginEvent::PluginMessage { from_plugin, to_plugin, .. } => {
                (from_plugin.as_str(), to_plugin.as_deref())
            },
            PluginEvent::PluginCommand { plugin_id, .. } |
            PluginEvent::PluginResponse { plugin_id, .. } => {
                (plugin_id.as_str(), None)
            },
            _ => ("system", None),
        };
        
        for filter in filters.iter() {
            if !filter.should_allow(event, from_plugin, to_plugin) {
                return false;
            }
        }
        
        true
    }

    /// Update event statistics
    fn update_stats(&self, event: &PluginEvent) {
        if let Ok(mut stats) = self.stats.write() {
            stats.total_events += 1;
            
            let event_type = match event {
                PluginEvent::SlotAllocated { .. } => "SlotAllocated",
                PluginEvent::SlotDeallocated { .. } => "SlotDeallocated",
                PluginEvent::PluginMessage { .. } => "PluginMessage",
                PluginEvent::PluginCommand { .. } => "PluginCommand",
                PluginEvent::SystemShutdown => "SystemShutdown",
                _ => "Other",
            };
            
            *stats.events_by_type.entry(event_type.to_string()).or_insert(0) += 1;
            
            // Update plugin-specific stats
            let plugin_id = match event {
                PluginEvent::PluginMessage { from_plugin, .. } => Some(from_plugin),
                PluginEvent::PluginCommand { plugin_id, .. } => Some(plugin_id),
                PluginEvent::PluginResponse { plugin_id, .. } => Some(plugin_id),
                _ => None,
            };
            
            if let Some(plugin_id) = plugin_id {
                *stats.events_by_plugin.entry(plugin_id.clone()).or_insert(0) += 1;
            }
        }
    }

    /// Increment filtered events counter
    fn increment_filtered_stats(&self) {
        if let Ok(mut stats) = self.stats.write() {
            stats.filtered_events += 1;
        }
    }
    
    /// Increment rate limited events counter
    fn increment_rate_limited_stats(&self) {
        if let Ok(mut stats) = self.stats.write() {
            stats.rate_limited_events += 1;
        }
    }
    
    /// Increment queue overflow events counter
    fn increment_queue_overflow_stats(&self) {
        if let Ok(mut stats) = self.stats.write() {
            stats.queue_overflow_events += 1;
        }
    }
    
    /// Extract source plugin from event
    fn extract_source_plugin(&self, event: &PluginEvent) -> Option<String> {
        match event {
            PluginEvent::PluginMessage { from_plugin, .. } => Some(from_plugin.clone()),
            PluginEvent::PluginCommand { plugin_id, .. } => Some(plugin_id.clone()),
            PluginEvent::PluginResponse { plugin_id, .. } => Some(plugin_id.clone()),
            _ => None,
        }
    }
    
    /// Check rate limit for a plugin
    fn check_rate_limit(&self, plugin_id: &str) -> PluginDockResult<bool> {
        let mut rate_limits = self.rate_limits.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire rate limits lock".to_string()))?;
        
        if let Some(rate_limiter) = rate_limits.get_mut(plugin_id) {
            Ok(rate_limiter.check_rate_limit())
        } else {
            // No rate limiter found, allow by default but log warning
            log::warn!("No rate limiter found for plugin: {}", plugin_id);
            Ok(true)
        }
    }
    
    /// Check queue size limit for a plugin
    fn check_queue_limit(&self, plugin_id: &str) -> PluginDockResult<bool> {
        let queue_limits = self.queue_limits.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire queue limits lock".to_string()))?;
        
        let channels = self.plugin_channels.read()
            .map_err(|_| PluginDockError::Security("Failed to acquire plugin channels lock".to_string()))?;
        
        if let (Some(limit), Some(sender)) = (queue_limits.get(plugin_id), channels.get(plugin_id)) {
            // Check if the channel is approaching capacity
            // Note: UnboundedSender doesn't have a direct way to check queue size,
            // so we'll implement a simple heuristic based on send failures
            Ok(true) // For now, always allow - could be enhanced with custom queue implementation
        } else {
            Ok(true)
        }
    }
    
    /// Set plugin trust level
    pub fn set_plugin_trust_level(&self, plugin_id: &str, level: TrustLevel) -> PluginDockResult<()> {
        self.security_context.set_plugin_trust_level(plugin_id, level)
            .map_err(|e| PluginDockError::Security(e))?;
        
        // Update rate limiter based on new trust level
        let (max_events, burst) = match level {
            TrustLevel::Untrusted => (10, 5),
            TrustLevel::Basic => (50, 20),
            TrustLevel::Trusted => (200, 50),
            TrustLevel::System => (1000, 200),
        };
        
        let mut rate_limits = self.rate_limits.write()
            .map_err(|_| PluginDockError::Security("Failed to acquire rate limits lock".to_string()))?;
        rate_limits.insert(plugin_id.to_string(), RateLimiter::new(max_events, burst));
        
        Ok(())
    }
    
    /// Block a plugin from sending events
    pub fn block_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        self.security_context.block_plugin(plugin_id)
            .map_err(|e| PluginDockError::Security(e))
    }
    
    /// Unblock a plugin
    pub fn unblock_plugin(&self, plugin_id: &str) -> PluginDockResult<()> {
        self.security_context.unblock_plugin(plugin_id)
            .map_err(|e| PluginDockError::Security(e))
    }
    
    /// Get security context
    pub fn security_context(&self) -> Arc<SecurityContext> {
        self.security_context.clone()
    }
}

/// Default security filter implementation
pub struct SecurityFilter {
    allowed_commands: HashMap<String, Vec<String>>,
}

impl SecurityFilter {
    pub fn new() -> Self {
        Self {
            allowed_commands: HashMap::new(),
        }
    }
    
    pub fn allow_command(&mut self, plugin_id: String, command: String) {
        self.allowed_commands.entry(plugin_id).or_insert_with(Vec::new).push(command);
    }
}

impl EventFilter for SecurityFilter {
    fn should_allow(&self, event: &PluginEvent, from_plugin: &str, _to_plugin: Option<&str>) -> bool {
        match event {
            PluginEvent::PluginCommand { plugin_id, command } => {
                if let Some(allowed) = self.allowed_commands.get(plugin_id) {
                    let command_name = match command {
                        PluginCommand::Initialize => "Initialize",
                        PluginCommand::Shutdown => "Shutdown",
                        PluginCommand::ExecuteAction { action, .. } => action,
                        _ => "Unknown",
                    };
                    allowed.contains(&command_name.to_string())
                } else {
                    false // No permissions defined = deny
                }
            },
            PluginEvent::SecurityViolation { .. } => {
                // Always allow security violation events
                true
            },
            _ => true, // Allow other events by default
        }
    }
    
    fn name(&self) -> &str {
        "SecurityFilter"
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{timeout, Duration};

    struct TestEventHandler {
        plugin_id: String,
        received_events: Arc<RwLock<Vec<PluginEvent>>>,
    }

    impl TestEventHandler {
        fn new(plugin_id: String) -> Self {
            Self {
                plugin_id,
                received_events: Arc::new(RwLock::new(Vec::new())),
            }
        }
        
        fn get_received_events(&self) -> Vec<PluginEvent> {
            self.received_events.read().unwrap().clone()
        }
    }

    #[async_trait]
    impl PluginEventHandler for TestEventHandler {
        async fn handle_event(&self, event: PluginEvent) -> PluginDockResult<()> {
            self.received_events.write().unwrap().push(event);
            Ok(())
        }
        
        fn plugin_id(&self) -> &str {
            &self.plugin_id
        }
    }

    #[tokio::test]
    async fn test_plugin_registration() {
        let event_bus = PluginEventBus::new();
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Send a test event
        let test_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        
        event_bus.emit(test_event.clone()).unwrap();
        
        // Receive the event
        let received = timeout(Duration::from_millis(100), receiver.recv()).await;
        assert!(received.is_ok());
        
        if let Ok(Some(event)) = received {
            match event {
                PluginEvent::PluginCommand { plugin_id, .. } => {
                    assert_eq!(plugin_id, "test-plugin");
                },
                _ => panic!("Unexpected event type"),
            }
        }
    }

    #[tokio::test]
    async fn test_event_filtering() {
        let event_bus = PluginEventBus::new();
        let mut security_filter = SecurityFilter::new();
        security_filter.allow_command("test-plugin".to_string(), "Initialize".to_string());
        
        event_bus.add_filter(Box::new(security_filter)).unwrap();
        
        let mut receiver = event_bus.register_plugin("test-plugin".to_string()).unwrap();
        
        // Send allowed command
        let allowed_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Initialize,
        };
        event_bus.emit(allowed_event).unwrap();
        
        // Send disallowed command
        let disallowed_event = PluginEvent::PluginCommand {
            plugin_id: "test-plugin".to_string(),
            command: PluginCommand::Shutdown,
        };
        event_bus.emit(disallowed_event).unwrap();
        
        // Should only receive the allowed event
        let received = timeout(Duration::from_millis(100), receiver.recv()).await;
        assert!(received.is_ok());
        
        // Should not receive the disallowed event
        let not_received = timeout(Duration::from_millis(50), receiver.recv()).await;
        assert!(not_received.is_err()); // Timeout expected
    }

    #[test]
    fn test_plugin_data_creation() {
        let data = PluginData::new(
            "test-data".to_string(),
            serde_json::json!({"key": "value"})
        ).with_metadata("source".to_string(), "test".to_string());
        
        assert_eq!(data.data_type, "test-data");
        assert_eq!(data.metadata.get("source"), Some(&"test".to_string()));
        assert!(data.timestamp > 0);
    }
}