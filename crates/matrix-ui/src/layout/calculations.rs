//! Layout Calculations and Optimization
//!
//! This module provides algorithms for calculating optimal layout positions,
//! handling constraints, and optimizing layout performance.

use crate::error::UiError;
use super::{LayoutArea, LayoutDimensions, LayoutPosition, LayoutRect};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Layout constraints for calculations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutConstraints {
    /// Container dimensions
    pub container_width: f64,
    pub container_height: f64,
    
    /// Minimum dimensions for areas
    pub min_dimensions: HashMap<LayoutArea, LayoutDimensions>,
    
    /// Maximum dimensions for areas
    pub max_dimensions: HashMap<LayoutArea, LayoutDimensions>,
    
    /// Fixed dimensions (cannot be resized)
    pub fixed_dimensions: HashMap<LayoutArea, LayoutDimensions>,
    
    /// Area priorities (higher priority gets more space)
    pub priorities: HashMap<LayoutArea, u32>,
    
    /// Spacing constraints
    pub min_spacing: f64,
    pub preferred_spacing: f64,
    
    /// Aspect ratio constraints
    pub aspect_ratios: HashMap<LayoutArea, f64>,
    
    /// Alignment preferences
    pub alignments: HashMap<LayoutArea, Alignment>,
}

/// Alignment options for layout areas
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum Alignment {
    Start,
    Center,
    End,
    Stretch,
}

impl Default for LayoutConstraints {
    fn default() -> Self {
        let mut min_dimensions = HashMap::new();
        let mut priorities = HashMap::new();
        
        // Set default minimum dimensions
        min_dimensions.insert(LayoutArea::TitleBar, LayoutDimensions {
            width: 200.0,
            height: 32.0,
            min_width: 200.0,
            min_height: 32.0,
            max_width: None,
            max_height: Some(32.0),
        });
        
        min_dimensions.insert(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 200.0,
            height: 300.0,
            min_width: 150.0,
            min_height: 200.0,
            max_width: Some(500.0),
            max_height: None,
        });
        
        min_dimensions.insert(LayoutArea::MainEditor, LayoutDimensions {
            width: 400.0,
            height: 300.0,
            min_width: 300.0,
            min_height: 200.0,
            max_width: None,
            max_height: None,
        });
        
        // Set priorities (higher = more important)
        priorities.insert(LayoutArea::MainEditor, 10); // Highest priority
        priorities.insert(LayoutArea::LeftSidebar, 7);
        priorities.insert(LayoutArea::RightSidebar, 6);
        priorities.insert(LayoutArea::BottomPanel, 5);
        priorities.insert(LayoutArea::TitleBar, 8);
        priorities.insert(LayoutArea::MenuBar, 8);
        priorities.insert(LayoutArea::StatusBar, 4);
        
        Self {
            container_width: 1200.0,
            container_height: 800.0,
            min_dimensions,
            max_dimensions: HashMap::new(),
            fixed_dimensions: HashMap::new(),
            priorities,
            min_spacing: 4.0,
            preferred_spacing: 8.0,
            aspect_ratios: HashMap::new(),
            alignments: HashMap::new(),
        }
    }
}

/// Layout metrics for analysis and optimization
#[derive(Debug, Clone)]
pub struct LayoutMetrics {
    /// Current window dimensions
    pub window_width: f64,
    pub window_height: f64,
    
    /// Current area layouts
    pub areas: HashMap<LayoutArea, LayoutRect>,
    
    /// Calculated metrics
    pub total_used_space: f64,
    pub space_efficiency: f64,
    pub overlap_count: u32,
    pub constraint_violations: Vec<String>,
    
    /// Performance metrics
    pub calculation_time_ms: f64,
    pub memory_usage_bytes: usize,
}

impl LayoutMetrics {
    /// Create metrics from current layout
    pub fn from_layout(window_width: f64, window_height: f64, areas: HashMap<LayoutArea, LayoutRect>) -> Self {
        let total_window_space = window_width * window_height;
        let total_used_space: f64 = areas.values()
            .map(|rect| rect.dimensions.width * rect.dimensions.height)
            .sum();
        
        let space_efficiency = if total_window_space > 0.0 {
            total_used_space / total_window_space
        } else {
            0.0
        };
        
        let overlap_count = Self::count_overlaps(&areas);
        
        Self {
            window_width,
            window_height,
            areas,
            total_used_space,
            space_efficiency,
            overlap_count,
            constraint_violations: Vec::new(),
            calculation_time_ms: 0.0,
            memory_usage_bytes: 0,
        }
    }
    
    /// Count overlapping areas
    fn count_overlaps(areas: &HashMap<LayoutArea, LayoutRect>) -> u32 {
        let mut count = 0;
        let area_vec: Vec<_> = areas.values().collect();
        
        for i in 0..area_vec.len() {
            for j in (i + 1)..area_vec.len() {
                if area_vec[i].intersects(area_vec[j]) {
                    count += 1;
                }
            }
        }
        
        count
    }
    
    /// Check if layout is optimal
    pub fn is_optimal(&self) -> bool {
        self.overlap_count == 0 && 
        self.constraint_violations.is_empty() && 
        self.space_efficiency > 0.8
    }
    
    /// Get layout quality score (0.0 to 1.0)
    pub fn quality_score(&self) -> f64 {
        let overlap_penalty = self.overlap_count as f64 * 0.1;
        let violation_penalty = self.constraint_violations.len() as f64 * 0.05;
        
        (self.space_efficiency - overlap_penalty - violation_penalty).max(0.0).min(1.0)
    }
}

/// Layout calculation result
#[derive(Debug, Clone)]
pub struct LayoutResult {
    /// Calculated area positions and dimensions
    pub areas: HashMap<LayoutArea, LayoutRect>,
    
    /// Layout metrics
    pub metrics: LayoutMetrics,
    
    /// Warnings and suggestions
    pub warnings: Vec<String>,
    
    /// Whether the layout is valid
    pub is_valid: bool,
}

/// Layout calculator for optimal positioning
pub struct LayoutCalculator {
    /// Current constraints
    constraints: LayoutConstraints,
    
    /// Calculation history for optimization
    calculation_history: Vec<LayoutMetrics>,
    
    /// Maximum history size
    max_history_size: usize,
}

impl LayoutCalculator {
    /// Create a new layout calculator
    pub fn new() -> Self {
        Self {
            constraints: LayoutConstraints::default(),
            calculation_history: Vec::new(),
            max_history_size: 100,
        }
    }
    
    /// Create with custom constraints
    pub fn with_constraints(constraints: LayoutConstraints) -> Self {
        Self {
            constraints,
            calculation_history: Vec::new(),
            max_history_size: 100,
        }
    }
    
    /// Calculate optimal layout
    pub fn calculate_optimal_layout(&self, metrics: LayoutMetrics) -> Result<LayoutResult, UiError> {
        let start_time = std::time::Instant::now();
        
        // Start with current layout
        let mut areas = metrics.areas.clone();
        
        // Apply constraint-based optimization
        self.apply_constraints(&mut areas)?;
        
        // Resolve overlaps
        self.resolve_overlaps(&mut areas)?;
        
        // Optimize space usage
        self.optimize_space_usage(&mut areas)?;
        
        // Calculate final metrics
        let mut final_metrics = LayoutMetrics::from_layout(
            metrics.window_width,
            metrics.window_height,
            areas.clone(),
        );
        
        final_metrics.calculation_time_ms = start_time.elapsed().as_millis() as f64;
        
        // Validate result
        let (is_valid, warnings) = self.validate_layout(&areas, &final_metrics);
        
        Ok(LayoutResult {
            areas,
            metrics: final_metrics,
            warnings,
            is_valid,
        })
    }
    
    /// Apply layout constraints
    fn apply_constraints(&self, areas: &mut HashMap<LayoutArea, LayoutRect>) -> Result<(), UiError> {
        for (area, rect) in areas.iter_mut() {
            // Apply minimum dimensions
            if let Some(min_dims) = self.constraints.min_dimensions.get(area) {
                rect.dimensions.width = rect.dimensions.width.max(min_dims.width);
                rect.dimensions.height = rect.dimensions.height.max(min_dims.height);
            }
            
            // Apply maximum dimensions
            if let Some(max_dims) = self.constraints.max_dimensions.get(area) {
                if let Some(max_width) = max_dims.max_width {
                    rect.dimensions.width = rect.dimensions.width.min(max_width);
                }
                if let Some(max_height) = max_dims.max_height {
                    rect.dimensions.height = rect.dimensions.height.min(max_height);
                }
            }
            
            // Apply fixed dimensions
            if let Some(fixed_dims) = self.constraints.fixed_dimensions.get(area) {
                rect.dimensions = *fixed_dims;
            }
            
            // Apply aspect ratio constraints
            if let Some(aspect_ratio) = self.constraints.aspect_ratios.get(area) {
                let current_ratio = rect.dimensions.width / rect.dimensions.height;
                if (current_ratio - aspect_ratio).abs() > 0.1 {
                    // Adjust height to match aspect ratio
                    rect.dimensions.height = rect.dimensions.width / aspect_ratio;
                }
            }
        }
        
        Ok(())
    }
    
    /// Resolve overlapping areas
    fn resolve_overlaps(&self, areas: &mut HashMap<LayoutArea, LayoutRect>) -> Result<(), UiError> {
        let mut iterations = 0;
        const MAX_ITERATIONS: u32 = 10;
        
        while self.has_overlaps(areas) && iterations < MAX_ITERATIONS {
            let overlaps = self.find_overlaps(areas);
            
            for (area1, area2) in overlaps {
                self.resolve_overlap_pair(areas, area1, area2)?;
            }
            
            iterations += 1;
        }
        
        if iterations >= MAX_ITERATIONS {
            return Err(UiError::LayoutError("Could not resolve all overlaps within iteration limit".to_string()));
        }
        
        Ok(())
    }
    
    /// Check if layout has overlaps
    fn has_overlaps(&self, areas: &HashMap<LayoutArea, LayoutRect>) -> bool {
        let area_vec: Vec<_> = areas.iter().collect();
        
        for i in 0..area_vec.len() {
            for j in (i + 1)..area_vec.len() {
                if area_vec[i].1.intersects(area_vec[j].1) {
                    return true;
                }
            }
        }
        
        false
    }
    
    /// Find all overlapping area pairs
    fn find_overlaps(&self, areas: &HashMap<LayoutArea, LayoutRect>) -> Vec<(LayoutArea, LayoutArea)> {
        let mut overlaps = Vec::new();
        let area_vec: Vec<_> = areas.iter().collect();
        
        for i in 0..area_vec.len() {
            for j in (i + 1)..area_vec.len() {
                if area_vec[i].1.intersects(area_vec[j].1) {
                    overlaps.push((*area_vec[i].0, *area_vec[j].0));
                }
            }
        }
        
        overlaps
    }
    
    /// Resolve overlap between two specific areas
    fn resolve_overlap_pair(&self, areas: &mut HashMap<LayoutArea, LayoutRect>, area1: LayoutArea, area2: LayoutArea) -> Result<(), UiError> {
        let priority1 = self.constraints.priorities.get(&area1).copied().unwrap_or(5);
        let priority2 = self.constraints.priorities.get(&area2).copied().unwrap_or(5);
        
        // Higher priority area stays, lower priority area moves
        let (fixed_area, moving_area) = if priority1 >= priority2 {
            (area1, area2)
        } else {
            (area2, area1)
        };
        
        let fixed_rect = areas.get(&fixed_area).copied();
        if let Some(fixed_rect) = fixed_rect {
            if let Some(moving_rect) = areas.get_mut(&moving_area) {
                // Move the lower priority area to avoid overlap
                self.move_area_to_avoid_overlap(moving_rect, &fixed_rect)?;
            }
        }
        
        Ok(())
    }
    
    /// Move an area to avoid overlap with a fixed area
    fn move_area_to_avoid_overlap(&self, moving_rect: &mut LayoutRect, fixed_rect: &LayoutRect) -> Result<(), UiError> {
        // Try moving to the right first
        let right_position = fixed_rect.position.x + fixed_rect.dimensions.width + self.constraints.preferred_spacing;
        if right_position + moving_rect.dimensions.width <= self.constraints.container_width {
            moving_rect.position.x = right_position;
            return Ok(());
        }
        
        // Try moving down
        let down_position = fixed_rect.position.y + fixed_rect.dimensions.height + self.constraints.preferred_spacing;
        if down_position + moving_rect.dimensions.height <= self.constraints.container_height {
            moving_rect.position.y = down_position;
            return Ok(());
        }
        
        // Try moving left
        let left_position = fixed_rect.position.x - moving_rect.dimensions.width - self.constraints.preferred_spacing;
        if left_position >= 0.0 {
            moving_rect.position.x = left_position;
            return Ok(());
        }
        
        // Try moving up
        let up_position = fixed_rect.position.y - moving_rect.dimensions.height - self.constraints.preferred_spacing;
        if up_position >= 0.0 {
            moving_rect.position.y = up_position;
            return Ok(());
        }
        
        // If no good position found, shrink the moving area
        moving_rect.dimensions.width *= 0.8;
        moving_rect.dimensions.height *= 0.8;
        
        Ok(())
    }
    
    /// Optimize space usage
    fn optimize_space_usage(&self, areas: &mut HashMap<LayoutArea, LayoutRect>) -> Result<(), UiError> {
        // Find unused space and redistribute to high-priority areas
        let total_used_width: f64 = areas.values()
            .map(|rect| rect.dimensions.width)
            .sum();
        
        let total_used_height: f64 = areas.values()
            .map(|rect| rect.dimensions.height)
            .sum();
        
        let unused_width = self.constraints.container_width - total_used_width;
        let unused_height = self.constraints.container_height - total_used_height;
        
        if unused_width > 0.0 || unused_height > 0.0 {
            self.redistribute_space(areas, unused_width, unused_height)?;
        }
        
        Ok(())
    }
    
    /// Redistribute unused space to areas based on priority
    fn redistribute_space(&self, areas: &mut HashMap<LayoutArea, LayoutRect>, unused_width: f64, unused_height: f64) -> Result<(), UiError> {
        // Get areas sorted by priority
        let mut priority_areas: Vec<_> = areas.keys()
            .map(|area| (*area, self.constraints.priorities.get(area).copied().unwrap_or(5)))
            .collect();
        priority_areas.sort_by(|a, b| b.1.cmp(&a.1)); // Sort by priority descending
        
        // Distribute width
        if unused_width > 0.0 {
            let width_per_area = unused_width / priority_areas.len() as f64;
            for (area, _) in &priority_areas {
                if let Some(rect) = areas.get_mut(area) {
                    // Check if area can accept more width
                    if let Some(max_dims) = self.constraints.max_dimensions.get(area) {
                        if let Some(max_width) = max_dims.max_width {
                            let additional_width = (width_per_area).min(max_width - rect.dimensions.width);
                            rect.dimensions.width += additional_width.max(0.0);
                        } else {
                            rect.dimensions.width += width_per_area;
                        }
                    } else {
                        rect.dimensions.width += width_per_area;
                    }
                }
            }
        }
        
        // Distribute height
        if unused_height > 0.0 {
            let height_per_area = unused_height / priority_areas.len() as f64;
            for (area, _) in &priority_areas {
                if let Some(rect) = areas.get_mut(area) {
                    // Check if area can accept more height
                    if let Some(max_dims) = self.constraints.max_dimensions.get(area) {
                        if let Some(max_height) = max_dims.max_height {
                            let additional_height = (height_per_area).min(max_height - rect.dimensions.height);
                            rect.dimensions.height += additional_height.max(0.0);
                        } else {
                            rect.dimensions.height += height_per_area;
                        }
                    } else {
                        rect.dimensions.height += height_per_area;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Validate layout and generate warnings
    fn validate_layout(&self, areas: &HashMap<LayoutArea, LayoutRect>, metrics: &LayoutMetrics) -> (bool, Vec<String>) {
        let mut warnings = Vec::new();
        let mut is_valid = true;
        
        // Check for overlaps
        if metrics.overlap_count > 0 {
            warnings.push(format!("Layout has {} overlapping areas", metrics.overlap_count));
            is_valid = false;
        }
        
        // Check space efficiency
        if metrics.space_efficiency < 0.5 {
            warnings.push("Layout has low space efficiency (< 50%)".to_string());
        }
        
        // Check minimum dimensions
        for (area, rect) in areas {
            if let Some(min_dims) = self.constraints.min_dimensions.get(area) {
                if rect.dimensions.width < min_dims.min_width || rect.dimensions.height < min_dims.min_height {
                    warnings.push(format!("Area {:?} is below minimum dimensions", area));
                    is_valid = false;
                }
            }
        }
        
        // Check bounds
        for (area, rect) in areas {
            if rect.position.x < 0.0 || rect.position.y < 0.0 {
                warnings.push(format!("Area {:?} has negative position", area));
                is_valid = false;
            }
            
            if rect.position.x + rect.dimensions.width > self.constraints.container_width ||
               rect.position.y + rect.dimensions.height > self.constraints.container_height {
                warnings.push(format!("Area {:?} extends beyond container bounds", area));
                is_valid = false;
            }
        }
        
        (is_valid, warnings)
    }
    
    /// Update constraints
    pub fn update_constraints(&mut self, constraints: LayoutConstraints) {
        self.constraints = constraints;
    }
    
    /// Get current constraints
    pub fn get_constraints(&self) -> &LayoutConstraints {
        &self.constraints
    }
    
    /// Add calculation to history
    pub fn add_to_history(&mut self, metrics: LayoutMetrics) {
        self.calculation_history.push(metrics);
        
        // Limit history size
        if self.calculation_history.len() > self.max_history_size {
            self.calculation_history.remove(0);
        }
    }
    
    /// Get calculation history
    pub fn get_history(&self) -> &[LayoutMetrics] {
        &self.calculation_history
    }
    
    /// Get average calculation time
    pub fn get_average_calculation_time(&self) -> f64 {
        if self.calculation_history.is_empty() {
            0.0
        } else {
            let total: f64 = self.calculation_history.iter()
                .map(|m| m.calculation_time_ms)
                .sum();
            total / self.calculation_history.len() as f64
        }
    }
    
    /// Clear calculation history
    pub fn clear_history(&mut self) {
        self.calculation_history.clear();
    }
}

impl Default for LayoutCalculator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_layout_calculator_creation() {
        let calculator = LayoutCalculator::new();
        assert_eq!(calculator.calculation_history.len(), 0);
        assert_eq!(calculator.max_history_size, 100);
    }

    #[test]
    fn test_layout_metrics_creation() {
        let mut areas = HashMap::new();
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(0.0, 0.0, 400.0, 300.0));
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(0.0, 0.0, 200.0, 300.0)); // Overlapping
        
        let metrics = LayoutMetrics::from_layout(800.0, 600.0, areas);
        
        assert_eq!(metrics.window_width, 800.0);
        assert_eq!(metrics.window_height, 600.0);
        assert_eq!(metrics.overlap_count, 1); // Should detect overlap
        assert!(metrics.space_efficiency > 0.0);
    }

    #[test]
    fn test_constraint_application() {
        let calculator = LayoutCalculator::new();
        let mut areas = HashMap::new();
        
        // Create area with dimensions below minimum
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(0.0, 0.0, 100.0, 100.0));
        
        calculator.apply_constraints(&mut areas).unwrap();
        
        // Should be adjusted to minimum dimensions
        let rect = areas.get(&LayoutArea::LeftSidebar).unwrap();
        assert!(rect.dimensions.width >= 150.0); // Minimum width from default constraints
        assert!(rect.dimensions.height >= 200.0); // Minimum height from default constraints
    }

    #[test]
    fn test_overlap_detection() {
        let calculator = LayoutCalculator::new();
        let mut areas = HashMap::new();
        
        // Create overlapping areas
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(0.0, 0.0, 400.0, 300.0));
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(100.0, 100.0, 200.0, 200.0));
        
        assert!(calculator.has_overlaps(&areas));
        
        let overlaps = calculator.find_overlaps(&areas);
        assert_eq!(overlaps.len(), 1);
        assert!(overlaps.contains(&(LayoutArea::MainEditor, LayoutArea::LeftSidebar)) ||
                overlaps.contains(&(LayoutArea::LeftSidebar, LayoutArea::MainEditor)));
    }

    #[test]
    fn test_layout_quality_score() {
        let mut areas = HashMap::new();
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(0.0, 0.0, 400.0, 300.0));
        
        let metrics = LayoutMetrics::from_layout(800.0, 600.0, areas);
        let score = metrics.quality_score();
        
        assert!(score >= 0.0 && score <= 1.0);
        assert!(score > 0.0); // Should have some positive score
    }

    #[test]
    fn test_layout_validation() {
        let calculator = LayoutCalculator::new();
        let mut areas = HashMap::new();
        
        // Valid layout
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(0.0, 0.0, 400.0, 300.0));
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(400.0, 0.0, 200.0, 300.0));
        
        let metrics = LayoutMetrics::from_layout(800.0, 600.0, areas.clone());
        let (is_valid, warnings) = calculator.validate_layout(&areas, &metrics);
        
        assert!(is_valid);
        assert!(warnings.is_empty() || warnings.iter().all(|w| !w.contains("overlap")));
    }
}