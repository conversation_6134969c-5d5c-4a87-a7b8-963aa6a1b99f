//! Responsive System for Layout Management
//!
//! This module implements responsive breakpoints and adaptive layouts
//! that adjust based on window size and device characteristics.

use crate::error::UiError;
use super::{LayoutArea, LayoutDimensions};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Responsive breakpoints configuration
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ResponsiveBreakpoints {
    /// Mobile breakpoint (max width)
    pub mobile: f64,
    
    /// Tablet breakpoint (max width)
    pub tablet: f64,
    
    /// Desktop breakpoint (max width)
    pub desktop: f64,
    
    /// Large desktop breakpoint (max width)
    pub large_desktop: f64,
    
    /// Ultra-wide breakpoint (max width)
    pub ultra_wide: f64,
}

impl Default for ResponsiveBreakpoints {
    fn default() -> Self {
        Self {
            mobile: 768.0,
            tablet: 1024.0,
            desktop: 1440.0,
            large_desktop: 1920.0,
            ultra_wide: 2560.0,
        }
    }
}

/// Responsive layout configuration for a specific breakpoint
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ResponsiveLayout {
    /// Breakpoint name
    pub breakpoint: String,
    
    /// Area visibility for this breakpoint
    pub area_visibility: HashMap<LayoutArea, bool>,
    
    /// Area dimensions for this breakpoint
    pub area_dimensions: HashMap<LayoutArea, LayoutDimensions>,
    
    /// Layout-specific properties
    pub properties: HashMap<String, serde_json::Value>,
}

impl ResponsiveLayout {
    /// Create a mobile layout
    pub fn mobile() -> Self {
        let mut area_visibility = HashMap::new();
        let mut area_dimensions = HashMap::new();
        
        // Mobile layout: hide sidebars, stack vertically
        area_visibility.insert(LayoutArea::TitleBar, true);
        area_visibility.insert(LayoutArea::MenuBar, false); // Hide menu bar on mobile
        area_visibility.insert(LayoutArea::LeftSidebar, false); // Hide left sidebar
        area_visibility.insert(LayoutArea::MainEditor, true);
        area_visibility.insert(LayoutArea::RightSidebar, false); // Hide right sidebar
        area_visibility.insert(LayoutArea::BottomPanel, true);
        area_visibility.insert(LayoutArea::StatusBar, true);
        area_visibility.insert(LayoutArea::Modal, false);
        
        // Mobile dimensions - full width for visible areas
        area_dimensions.insert(LayoutArea::TitleBar, LayoutDimensions {
            width: 100.0, // Percentage-based
            height: 40.0,
            min_width: 320.0,
            min_height: 40.0,
            max_width: None,
            max_height: Some(40.0),
        });
        
        area_dimensions.insert(LayoutArea::MainEditor, LayoutDimensions {
            width: 100.0,
            height: 60.0, // Percentage of available height
            min_width: 320.0,
            min_height: 200.0,
            max_width: None,
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::BottomPanel, LayoutDimensions {
            width: 100.0,
            height: 30.0,
            min_width: 320.0,
            min_height: 100.0,
            max_width: None,
            max_height: Some(300.0),
        });
        
        area_dimensions.insert(LayoutArea::StatusBar, LayoutDimensions {
            width: 100.0,
            height: 24.0,
            min_width: 320.0,
            min_height: 24.0,
            max_width: None,
            max_height: Some(24.0),
        });
        
        Self {
            breakpoint: "mobile".to_string(),
            area_visibility,
            area_dimensions,
            properties: HashMap::new(),
        }
    }
    
    /// Create a tablet layout
    pub fn tablet() -> Self {
        let mut area_visibility = HashMap::new();
        let mut area_dimensions = HashMap::new();
        
        // Tablet layout: show left sidebar, hide right sidebar
        area_visibility.insert(LayoutArea::TitleBar, true);
        area_visibility.insert(LayoutArea::MenuBar, true);
        area_visibility.insert(LayoutArea::LeftSidebar, true);
        area_visibility.insert(LayoutArea::MainEditor, true);
        area_visibility.insert(LayoutArea::RightSidebar, false); // Hide right sidebar
        area_visibility.insert(LayoutArea::BottomPanel, true);
        area_visibility.insert(LayoutArea::StatusBar, true);
        area_visibility.insert(LayoutArea::Modal, false);
        
        // Tablet dimensions
        area_dimensions.insert(LayoutArea::TitleBar, LayoutDimensions {
            width: 100.0,
            height: 32.0,
            min_width: 768.0,
            min_height: 32.0,
            max_width: None,
            max_height: Some(32.0),
        });
        
        area_dimensions.insert(LayoutArea::MenuBar, LayoutDimensions {
            width: 100.0,
            height: 28.0,
            min_width: 768.0,
            min_height: 28.0,
            max_width: None,
            max_height: Some(28.0),
        });
        
        area_dimensions.insert(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 25.0, // 25% of width
            height: 100.0,
            min_width: 200.0,
            min_height: 400.0,
            max_width: Some(300.0),
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::MainEditor, LayoutDimensions {
            width: 75.0, // 75% of width
            height: 70.0,
            min_width: 400.0,
            min_height: 300.0,
            max_width: None,
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::BottomPanel, LayoutDimensions {
            width: 75.0,
            height: 30.0,
            min_width: 400.0,
            min_height: 100.0,
            max_width: None,
            max_height: Some(400.0),
        });
        
        area_dimensions.insert(LayoutArea::StatusBar, LayoutDimensions {
            width: 100.0,
            height: 24.0,
            min_width: 768.0,
            min_height: 24.0,
            max_width: None,
            max_height: Some(24.0),
        });
        
        Self {
            breakpoint: "tablet".to_string(),
            area_visibility,
            area_dimensions,
            properties: HashMap::new(),
        }
    }
    
    /// Create a desktop layout
    pub fn desktop() -> Self {
        let mut area_visibility = HashMap::new();
        let mut area_dimensions = HashMap::new();
        
        // Desktop layout: show all areas
        for area in [
            LayoutArea::TitleBar,
            LayoutArea::MenuBar,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
            LayoutArea::RightSidebar,
            LayoutArea::BottomPanel,
            LayoutArea::StatusBar,
        ] {
            area_visibility.insert(area, true);
        }
        area_visibility.insert(LayoutArea::Modal, false);
        
        // Desktop dimensions - balanced layout
        area_dimensions.insert(LayoutArea::TitleBar, LayoutDimensions {
            width: 100.0,
            height: 32.0,
            min_width: 1024.0,
            min_height: 32.0,
            max_width: None,
            max_height: Some(32.0),
        });
        
        area_dimensions.insert(LayoutArea::MenuBar, LayoutDimensions {
            width: 100.0,
            height: 28.0,
            min_width: 1024.0,
            min_height: 28.0,
            max_width: None,
            max_height: Some(28.0),
        });
        
        area_dimensions.insert(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 20.0, // 20% of width
            height: 100.0,
            min_width: 200.0,
            min_height: 400.0,
            max_width: Some(400.0),
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::MainEditor, LayoutDimensions {
            width: 55.0, // 55% of width
            height: 70.0,
            min_width: 400.0,
            min_height: 300.0,
            max_width: None,
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::RightSidebar, LayoutDimensions {
            width: 25.0, // 25% of width
            height: 100.0,
            min_width: 200.0,
            min_height: 400.0,
            max_width: Some(400.0),
            max_height: None,
        });
        
        area_dimensions.insert(LayoutArea::BottomPanel, LayoutDimensions {
            width: 80.0, // Spans main editor + right sidebar
            height: 30.0,
            min_width: 600.0,
            min_height: 100.0,
            max_width: None,
            max_height: Some(400.0),
        });
        
        area_dimensions.insert(LayoutArea::StatusBar, LayoutDimensions {
            width: 100.0,
            height: 24.0,
            min_width: 1024.0,
            min_height: 24.0,
            max_width: None,
            max_height: Some(24.0),
        });
        
        Self {
            breakpoint: "desktop".to_string(),
            area_visibility,
            area_dimensions,
            properties: HashMap::new(),
        }
    }
    
    /// Create a large desktop layout
    pub fn large_desktop() -> Self {
        let mut layout = Self::desktop();
        layout.breakpoint = "large_desktop".to_string();
        
        // Adjust dimensions for larger screens
        if let Some(left_sidebar) = layout.area_dimensions.get_mut(&LayoutArea::LeftSidebar) {
            left_sidebar.width = 18.0; // Slightly smaller percentage
            left_sidebar.max_width = Some(350.0);
        }
        
        if let Some(main_editor) = layout.area_dimensions.get_mut(&LayoutArea::MainEditor) {
            main_editor.width = 57.0; // Slightly larger
        }
        
        if let Some(right_sidebar) = layout.area_dimensions.get_mut(&LayoutArea::RightSidebar) {
            right_sidebar.width = 25.0;
            right_sidebar.max_width = Some(450.0);
        }
        
        layout
    }
    
    /// Create an ultra-wide layout
    pub fn ultra_wide() -> Self {
        let mut layout = Self::large_desktop();
        layout.breakpoint = "ultra_wide".to_string();
        
        // Optimize for ultra-wide screens
        if let Some(left_sidebar) = layout.area_dimensions.get_mut(&LayoutArea::LeftSidebar) {
            left_sidebar.width = 15.0; // Smaller percentage for ultra-wide
            left_sidebar.max_width = Some(400.0);
        }
        
        if let Some(main_editor) = layout.area_dimensions.get_mut(&LayoutArea::MainEditor) {
            main_editor.width = 60.0; // More space for editor
        }
        
        if let Some(right_sidebar) = layout.area_dimensions.get_mut(&LayoutArea::RightSidebar) {
            right_sidebar.width = 25.0;
            right_sidebar.max_width = Some(500.0);
        }
        
        layout
    }
}

/// Responsive system for managing breakpoints and layouts
pub struct ResponsiveSystem {
    /// Breakpoint configuration
    breakpoints: ResponsiveBreakpoints,
    
    /// Layout configurations for each breakpoint
    layouts: HashMap<String, ResponsiveLayout>,
    
    /// Current breakpoint
    current_breakpoint: Option<String>,
}

impl ResponsiveSystem {
    /// Create a new responsive system
    pub fn new(breakpoints: ResponsiveBreakpoints) -> Result<Self, UiError> {
        let mut layouts = HashMap::new();
        
        // Initialize default layouts for each breakpoint
        layouts.insert("mobile".to_string(), ResponsiveLayout::mobile());
        layouts.insert("tablet".to_string(), ResponsiveLayout::tablet());
        layouts.insert("desktop".to_string(), ResponsiveLayout::desktop());
        layouts.insert("large_desktop".to_string(), ResponsiveLayout::large_desktop());
        layouts.insert("ultra_wide".to_string(), ResponsiveLayout::ultra_wide());
        
        Ok(Self {
            breakpoints,
            layouts,
            current_breakpoint: None,
        })
    }
    
    /// Initialize the responsive system
    pub async fn initialize(&self) -> Result<(), UiError> {
        // ResponsiveSystem is initialized with default values, no mutable state needed
        Ok(())
    }
    
    /// Get current breakpoint based on window width
    pub fn get_current_breakpoint(&self, window_width: f64) -> Option<String> {
        if window_width <= self.breakpoints.mobile {
            Some("mobile".to_string())
        } else if window_width <= self.breakpoints.tablet {
            Some("tablet".to_string())
        } else if window_width <= self.breakpoints.desktop {
            Some("desktop".to_string())
        } else if window_width <= self.breakpoints.large_desktop {
            Some("large_desktop".to_string())
        } else if window_width <= self.breakpoints.ultra_wide {
            Some("ultra_wide".to_string())
        } else {
            Some("ultra_wide".to_string()) // Default to ultra_wide for very large screens
        }
    }
    
    /// Get layout configuration for a breakpoint
    pub fn get_layout_for_breakpoint(&self, breakpoint: &str) -> Result<ResponsiveLayout, UiError> {
        self.layouts.get(breakpoint)
            .cloned()
            .ok_or_else(|| UiError::LayoutError(format!("Layout for breakpoint '{}' not found", breakpoint)))
    }
    
    /// Update layout for a specific breakpoint
    pub fn set_layout_for_breakpoint(&mut self, breakpoint: String, layout: ResponsiveLayout) {
        self.layouts.insert(breakpoint, layout);
    }
    
    /// Get all available breakpoints
    pub fn get_breakpoints(&self) -> &ResponsiveBreakpoints {
        &self.breakpoints
    }
    
    /// Update breakpoint configuration
    pub fn update_breakpoints(&mut self, breakpoints: ResponsiveBreakpoints) {
        self.breakpoints = breakpoints;
    }
    
    /// Get current breakpoint
    pub fn get_current_breakpoint_name(&self) -> Option<&String> {
        self.current_breakpoint.as_ref()
    }
    
    /// Check if current breakpoint is mobile
    pub fn is_mobile(&self, window_width: f64) -> bool {
        window_width <= self.breakpoints.mobile
    }
    
    /// Check if current breakpoint is tablet
    pub fn is_tablet(&self, window_width: f64) -> bool {
        window_width > self.breakpoints.mobile && window_width <= self.breakpoints.tablet
    }
    
    /// Check if current breakpoint is desktop
    pub fn is_desktop(&self, window_width: f64) -> bool {
        window_width > self.breakpoints.tablet && window_width <= self.breakpoints.desktop
    }
    
    /// Check if current breakpoint is large desktop
    pub fn is_large_desktop(&self, window_width: f64) -> bool {
        window_width > self.breakpoints.desktop && window_width <= self.breakpoints.large_desktop
    }
    
    /// Check if current breakpoint is ultra-wide
    pub fn is_ultra_wide(&self, window_width: f64) -> bool {
        window_width > self.breakpoints.large_desktop
    }
    
    /// Get recommended layout properties for current breakpoint
    pub fn get_layout_properties(&self, window_width: f64) -> HashMap<String, serde_json::Value> {
        let mut properties = HashMap::new();
        
        if self.is_mobile(window_width) {
            properties.insert("layout_type".to_string(), serde_json::json!("mobile"));
            properties.insert("sidebar_collapsed".to_string(), serde_json::json!(true));
            properties.insert("panel_stacked".to_string(), serde_json::json!(true));
        } else if self.is_tablet(window_width) {
            properties.insert("layout_type".to_string(), serde_json::json!("tablet"));
            properties.insert("sidebar_collapsed".to_string(), serde_json::json!(false));
            properties.insert("right_sidebar_hidden".to_string(), serde_json::json!(true));
        } else if self.is_desktop(window_width) {
            properties.insert("layout_type".to_string(), serde_json::json!("desktop"));
            properties.insert("sidebar_collapsed".to_string(), serde_json::json!(false));
            properties.insert("all_panels_visible".to_string(), serde_json::json!(true));
        } else if self.is_large_desktop(window_width) {
            properties.insert("layout_type".to_string(), serde_json::json!("large_desktop"));
            properties.insert("optimized_spacing".to_string(), serde_json::json!(true));
        } else {
            properties.insert("layout_type".to_string(), serde_json::json!("ultra_wide"));
            properties.insert("multi_column_layout".to_string(), serde_json::json!(true));
        }
        
        properties
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_responsive_breakpoints() {
        let breakpoints = ResponsiveBreakpoints::default();
        let system = ResponsiveSystem::new(breakpoints).unwrap();
        
        // Test mobile breakpoint
        assert_eq!(system.get_current_breakpoint(600.0), Some("mobile".to_string()));
        
        // Test tablet breakpoint
        assert_eq!(system.get_current_breakpoint(900.0), Some("tablet".to_string()));
        
        // Test desktop breakpoint
        assert_eq!(system.get_current_breakpoint(1200.0), Some("desktop".to_string()));
        
        // Test large desktop breakpoint
        assert_eq!(system.get_current_breakpoint(1600.0), Some("large_desktop".to_string()));
        
        // Test ultra-wide breakpoint
        assert_eq!(system.get_current_breakpoint(2000.0), Some("ultra_wide".to_string()));
    }

    #[test]
    fn test_responsive_layout_creation() {
        let mobile_layout = ResponsiveLayout::mobile();
        assert_eq!(mobile_layout.breakpoint, "mobile");
        assert!(!mobile_layout.area_visibility[&LayoutArea::LeftSidebar]);
        assert!(!mobile_layout.area_visibility[&LayoutArea::RightSidebar]);
        
        let desktop_layout = ResponsiveLayout::desktop();
        assert_eq!(desktop_layout.breakpoint, "desktop");
        assert!(desktop_layout.area_visibility[&LayoutArea::LeftSidebar]);
        assert!(desktop_layout.area_visibility[&LayoutArea::RightSidebar]);
    }

    #[test]
    fn test_layout_properties() {
        let system = ResponsiveSystem::new(ResponsiveBreakpoints::default()).unwrap();
        
        let mobile_props = system.get_layout_properties(600.0);
        assert_eq!(mobile_props["layout_type"], serde_json::json!("mobile"));
        assert_eq!(mobile_props["sidebar_collapsed"], serde_json::json!(true));
        
        let desktop_props = system.get_layout_properties(1200.0);
        assert_eq!(desktop_props["layout_type"], serde_json::json!("desktop"));
        assert_eq!(desktop_props["all_panels_visible"], serde_json::json!(true));
    }
}