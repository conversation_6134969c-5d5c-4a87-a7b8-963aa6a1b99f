//! Main Layout Manager with Responsive Grid System
//!
//! This module implements the core layout management system that coordinates
//! all layout areas, handles responsive behavior, and manages layout state.

use crate::error::UiError;
use crate::core::{UIEventBus, UIEvent};
use super::{
    ResponsiveSystem, ResponsiveBreakpoints, ResponsiveLayout,
    LayoutPersistence, LayoutPreferences,
    ResponsiveGrid, GridConfig,
    LayoutCalculator, LayoutMetrics,
    LayoutArea, LayoutDimensions, LayoutPosition, LayoutRect, LayoutEvent,
};
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Layout configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutConfig {
    /// Minimum panel dimensions
    pub min_panel_width: f64,
    pub min_panel_height: f64,
    
    /// Default margins and padding
    pub default_margin: f64,
    pub default_padding: f64,
    
    /// Splitter configuration
    pub splitter_width: f64,
    pub splitter_hover_width: f64,
    
    /// Animation settings
    pub animation_duration_ms: u32,
    pub enable_animations: bool,
    
    /// Responsive breakpoints
    pub breakpoints: ResponsiveBreakpoints,
    
    /// Grid configuration
    pub grid_config: GridConfig,
    
    /// Auto-save preferences
    pub auto_save_layout: bool,
    pub save_interval_ms: u32,
}

impl Default for LayoutConfig {
    fn default() -> Self {
        Self {
            min_panel_width: 200.0,
            min_panel_height: 100.0,
            default_margin: 8.0,
            default_padding: 12.0,
            splitter_width: 4.0,
            splitter_hover_width: 8.0,
            animation_duration_ms: 250,
            enable_animations: true,
            breakpoints: ResponsiveBreakpoints::default(),
            grid_config: GridConfig::default(),
            auto_save_layout: true,
            save_interval_ms: 5000,
        }
    }
}

/// Layout state for reactive updates
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct LayoutState {
    /// Current window dimensions
    pub window_dimensions: LayoutDimensions,
    
    /// Area layouts
    pub areas: HashMap<LayoutArea, LayoutRect>,
    
    /// Splitter positions
    pub splitter_positions: HashMap<String, f64>,
    
    /// Current responsive breakpoint
    pub current_breakpoint: String,
    
    /// Layout visibility states
    pub area_visibility: HashMap<LayoutArea, bool>,
    
    /// Layout lock states (prevent resizing)
    pub area_locks: HashMap<LayoutArea, bool>,
    
    /// Last update timestamp
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

impl Default for LayoutState {
    fn default() -> Self {
        let mut areas = HashMap::new();
        let mut area_visibility = HashMap::new();
        let mut area_locks = HashMap::new();
        
        // Initialize default areas
        areas.insert(LayoutArea::TitleBar, LayoutRect::new(0.0, 0.0, 1200.0, 32.0));
        areas.insert(LayoutArea::MenuBar, LayoutRect::new(0.0, 32.0, 1200.0, 28.0));
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(0.0, 60.0, 280.0, 640.0));
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(284.0, 60.0, 636.0, 440.0));
        areas.insert(LayoutArea::RightSidebar, LayoutRect::new(924.0, 60.0, 276.0, 640.0));
        areas.insert(LayoutArea::BottomPanel, LayoutRect::new(284.0, 504.0, 636.0, 196.0));
        areas.insert(LayoutArea::StatusBar, LayoutRect::new(0.0, 700.0, 1200.0, 24.0));
        
        // Initialize visibility
        for area in [
            LayoutArea::TitleBar,
            LayoutArea::MenuBar,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
            LayoutArea::RightSidebar,
            LayoutArea::BottomPanel,
            LayoutArea::StatusBar,
        ] {
            area_visibility.insert(area, true);
            area_locks.insert(area, false);
        }
        
        area_visibility.insert(LayoutArea::Modal, false);
        area_locks.insert(LayoutArea::Modal, false);
        
        Self {
            window_dimensions: LayoutDimensions {
                width: 1200.0,
                height: 800.0,
                min_width: 800.0,
                min_height: 600.0,
                max_width: None,
                max_height: None,
            },
            areas,
            splitter_positions: HashMap::new(),
            current_breakpoint: "desktop".to_string(),
            area_visibility,
            area_locks,
            last_updated: chrono::Utc::now(),
        }
    }
}

/// Main Layout Manager
pub struct LayoutManager {
    /// Layout configuration
    config: Arc<RwLock<LayoutConfig>>,
    
    /// Current layout state (reactive)
    state: RwSignal<LayoutState>,
    
    /// Responsive system
    responsive_system: Arc<ResponsiveSystem>,
    
    /// Layout persistence
    persistence: Arc<LayoutPersistence>,
    
    /// Responsive grid
    grid: Arc<ResponsiveGrid>,
    
    /// Layout calculator
    calculator: Arc<LayoutCalculator>,
    
    /// Event bus for layout events
    event_bus: Option<Arc<UIEventBus>>,
    
    /// Manager ID
    id: Uuid,
}

impl LayoutManager {
    /// Create a new layout manager
    pub fn new() -> Result<Self, UiError> {
        let config = Arc::new(RwLock::new(LayoutConfig::default()));
        let state = create_rw_signal(LayoutState::default());
        
        let responsive_system = Arc::new(ResponsiveSystem::new(
            config.read().unwrap().breakpoints.clone()
        )?);
        
        let persistence = Arc::new(LayoutPersistence::new()?);
        
        let grid = Arc::new(ResponsiveGrid::new(
            config.read().unwrap().grid_config.clone()
        )?);
        
        let calculator = Arc::new(LayoutCalculator::new());
        
        Ok(Self {
            config,
            state,
            responsive_system,
            persistence,
            grid,
            calculator,
            event_bus: None,
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the layout manager
    pub async fn initialize(&mut self, event_bus: Arc<UIEventBus>) -> Result<(), UiError> {
        self.event_bus = Some(event_bus.clone());
        
        // Initialize responsive system
        self.responsive_system.initialize().await?;
        
        // Load saved layout preferences
        if let Ok(preferences) = self.persistence.load_preferences().await {
            self.apply_preferences(preferences).await?;
        }
        
        // Setup responsive breakpoint monitoring
        self.setup_responsive_monitoring().await?;
        
        // Setup auto-save if enabled
        if self.config.read().unwrap().auto_save_layout {
            self.setup_auto_save().await?;
        }
        
        Ok(())
    }
    
    /// Setup responsive monitoring
    async fn setup_responsive_monitoring(&self) -> Result<(), UiError> {
        let state = self.state;
        let responsive_system = self.responsive_system.clone();
        let event_bus = self.event_bus.clone();
        
        tokio::spawn(async move {
            let mut last_breakpoint = String::new();
            
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                
                let current_state = state.get();
                let current_breakpoint = responsive_system
                    .get_current_breakpoint(current_state.window_dimensions.width)
                    .unwrap_or_else(|| "desktop".to_string());
                
                if current_breakpoint != last_breakpoint {
                    // Update state
                    state.update(|s| {
                        s.current_breakpoint = current_breakpoint.clone();
                        s.last_updated = chrono::Utc::now();
                    });
                    
                    // Emit event
                    if let Some(bus) = &event_bus {
                        let event = UIEvent::LayoutChanged {
                            layout_id: "responsive_breakpoint_change".to_string(),
                            changes: vec![format!("breakpoint_changed_to_{}", current_breakpoint)],
                            timestamp: chrono::Utc::now(),
                        };
                        let _ = bus.emit(event);
                    }
                    
                    last_breakpoint = current_breakpoint;
                }
            }
        });
        
        Ok(())
    }
    
    /// Setup auto-save functionality
    async fn setup_auto_save(&self) -> Result<(), UiError> {
        let state = self.state;
        let persistence = self.persistence.clone();
        let save_interval = self.config.read().unwrap().save_interval_ms;
        
        tokio::spawn(async move {
            let mut last_saved_state: Option<LayoutState> = None;
            
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(save_interval as u64)).await;
                
                let current_state = state.get();
                
                // Only save if state has changed
                if last_saved_state.as_ref() != Some(&current_state) {
                    let preferences = LayoutPreferences::from_state(&current_state);
                    if let Err(e) = persistence.save_preferences(&preferences).await {
                        eprintln!("Failed to auto-save layout preferences: {}", e);
                    } else {
                        last_saved_state = Some(current_state);
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Apply layout preferences
    async fn apply_preferences(&self, preferences: LayoutPreferences) -> Result<(), UiError> {
        self.state.update(|state| {
            // Apply area dimensions and positions
            for (area, rect) in preferences.area_layouts {
                state.areas.insert(area, rect);
            }
            
            // Apply splitter positions
            state.splitter_positions = preferences.splitter_positions;
            
            // Apply visibility states
            state.area_visibility = preferences.area_visibility;
            
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Update window dimensions and trigger responsive layout
    pub fn update_window_dimensions(&self, width: f64, height: f64) -> Result<(), UiError> {
        let old_state = self.state.get();
        let old_breakpoint = old_state.current_breakpoint.clone();
        
        // Calculate new breakpoint
        let new_breakpoint = self.responsive_system
            .get_current_breakpoint(width)
            .unwrap_or_else(|| "desktop".to_string());
        
        // Update state
        self.state.update(|state| {
            state.window_dimensions.width = width;
            state.window_dimensions.height = height;
            state.current_breakpoint = new_breakpoint.clone();
            state.last_updated = chrono::Utc::now();
        });
        
        // Recalculate layout if breakpoint changed
        if new_breakpoint != old_breakpoint {
            self.recalculate_responsive_layout()?;
        } else {
            // Just resize existing layout proportionally
            self.resize_layout_proportionally(width, height)?;
        }
        
        // Emit layout changed event
        if let Some(event_bus) = &self.event_bus {
            let event = UIEvent::LayoutChanged {
                layout_id: "window_resize".to_string(),
                changes: vec![
                    format!("window_width_{}", width),
                    format!("window_height_{}", height),
                    format!("breakpoint_{}", new_breakpoint),
                ],
                timestamp: chrono::Utc::now(),
            };
            event_bus.emit(event)?;
        }
        
        Ok(())
    }
    
    /// Recalculate layout for responsive breakpoint
    fn recalculate_responsive_layout(&self) -> Result<(), UiError> {
        let current_state = self.state.get();
        let responsive_layout = self.responsive_system
            .get_layout_for_breakpoint(&current_state.current_breakpoint)?;
        
        self.apply_responsive_layout(responsive_layout)?;
        
        Ok(())
    }
    
    /// Apply responsive layout configuration
    fn apply_responsive_layout(&self, layout: ResponsiveLayout) -> Result<(), UiError> {
        self.state.update(|state| {
            // Update area visibility based on responsive layout
            for (area, visible) in layout.area_visibility {
                state.area_visibility.insert(area, visible);
            }
            
            // Update area dimensions based on responsive layout
            for (area, dimensions) in layout.area_dimensions {
                if let Some(rect) = state.areas.get_mut(&area) {
                    rect.dimensions = dimensions;
                }
            }
            
            state.last_updated = chrono::Utc::now();
        });
        
        // Recalculate positions to fit new dimensions
        self.recalculate_layout_positions()?;
        
        Ok(())
    }
    
    /// Resize layout proportionally
    fn resize_layout_proportionally(&self, new_width: f64, new_height: f64) -> Result<(), UiError> {
        let current_state = self.state.get();
        let old_width = current_state.window_dimensions.width;
        let old_height = current_state.window_dimensions.height;
        
        if old_width <= 0.0 || old_height <= 0.0 {
            return Ok(());
        }
        
        let width_ratio = new_width / old_width;
        let height_ratio = new_height / old_height;
        
        self.state.update(|state| {
            for (_, rect) in state.areas.iter_mut() {
                // Scale position and dimensions
                rect.position.x *= width_ratio;
                rect.position.y *= height_ratio;
                rect.dimensions.width *= width_ratio;
                rect.dimensions.height *= height_ratio;
                
                // Ensure minimum dimensions
                let config = self.config.read().unwrap();
                rect.dimensions.width = rect.dimensions.width.max(config.min_panel_width);
                rect.dimensions.height = rect.dimensions.height.max(config.min_panel_height);
            }
            
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Recalculate layout positions to avoid overlaps
    fn recalculate_layout_positions(&self) -> Result<(), UiError> {
        let current_state = self.state.get();
        let metrics = LayoutMetrics::from_layout(
            current_state.window_dimensions.width,
            current_state.window_dimensions.height,
            current_state.areas.clone(),
        );
        
        let new_layout = self.calculator.calculate_optimal_layout(metrics)?;
        
        self.state.update(|state| {
            state.areas = new_layout.areas;
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Resize a specific area
    pub fn resize_area(&self, area: LayoutArea, new_dimensions: LayoutDimensions) -> Result<(), UiError> {
        // Check if area is locked
        let current_state = self.state.get();
        if current_state.area_locks.get(&area).copied().unwrap_or(false) {
            return Err(UiError::LayoutError("Area is locked and cannot be resized".to_string()));
        }
        
        let old_dimensions = current_state.areas.get(&area)
            .map(|rect| rect.dimensions)
            .unwrap_or_default();
        
        // Validate dimensions
        let config = self.config.read().unwrap();
        let validated_dimensions = LayoutDimensions {
            width: new_dimensions.width.max(config.min_panel_width),
            height: new_dimensions.height.max(config.min_panel_height),
            ..new_dimensions
        };
        
        // Update area dimensions
        self.state.update(|state| {
            if let Some(rect) = state.areas.get_mut(&area) {
                rect.dimensions = validated_dimensions;
            }
            state.last_updated = chrono::Utc::now();
        });
        
        // Recalculate layout to handle overlaps
        self.recalculate_layout_positions()?;
        
        // Emit resize event
        if let Some(event_bus) = &self.event_bus {
            let layout_event = LayoutEvent::AreaResized {
                area,
                old_dimensions,
                new_dimensions: validated_dimensions,
            };
            
            let ui_event = UIEvent::LayoutChanged {
                layout_id: format!("area_resize_{:?}", area),
                changes: vec![format!("resized_{}x{}", validated_dimensions.width, validated_dimensions.height)],
                timestamp: chrono::Utc::now(),
            };
            
            event_bus.emit(ui_event)?;
        }
        
        Ok(())
    }
    
    /// Move a specific area
    pub fn move_area(&self, area: LayoutArea, new_position: LayoutPosition) -> Result<(), UiError> {
        // Check if area is locked
        let current_state = self.state.get();
        if current_state.area_locks.get(&area).copied().unwrap_or(false) {
            return Err(UiError::LayoutError("Area is locked and cannot be moved".to_string()));
        }
        
        let old_position = current_state.areas.get(&area)
            .map(|rect| rect.position)
            .unwrap_or_default();
        
        // Update area position
        self.state.update(|state| {
            if let Some(rect) = state.areas.get_mut(&area) {
                rect.position = new_position;
            }
            state.last_updated = chrono::Utc::now();
        });
        
        // Emit move event
        if let Some(event_bus) = &self.event_bus {
            let layout_event = LayoutEvent::AreaMoved {
                area,
                old_position,
                new_position,
            };
            
            let ui_event = UIEvent::LayoutChanged {
                layout_id: format!("area_move_{:?}", area),
                changes: vec![format!("moved_to_{}x{}", new_position.x, new_position.y)],
                timestamp: chrono::Utc::now(),
            };
            
            event_bus.emit(ui_event)?;
        }
        
        Ok(())
    }
    
    /// Toggle area visibility
    pub fn toggle_area_visibility(&self, area: LayoutArea) -> Result<(), UiError> {
        let current_visibility = self.state.get().area_visibility.get(&area).copied().unwrap_or(true);
        
        self.state.update(|state| {
            state.area_visibility.insert(area, !current_visibility);
            state.last_updated = chrono::Utc::now();
        });
        
        // Recalculate layout to handle space redistribution
        self.recalculate_layout_positions()?;
        
        Ok(())
    }
    
    /// Lock/unlock area from resizing
    pub fn set_area_lock(&self, area: LayoutArea, locked: bool) -> Result<(), UiError> {
        self.state.update(|state| {
            state.area_locks.insert(area, locked);
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Get current layout state
    pub fn get_state(&self) -> LayoutState {
        self.state.get()
    }
    
    /// Get layout state signal for reactive updates
    pub fn get_state_signal(&self) -> RwSignal<LayoutState> {
        self.state
    }
    
    /// Get area rectangle
    pub fn get_area_rect(&self, area: LayoutArea) -> Option<LayoutRect> {
        self.state.get().areas.get(&area).copied()
    }
    
    /// Check if area is visible
    pub fn is_area_visible(&self, area: LayoutArea) -> bool {
        self.state.get().area_visibility.get(&area).copied().unwrap_or(true)
    }
    
    /// Check if area is locked
    pub fn is_area_locked(&self, area: LayoutArea) -> bool {
        self.state.get().area_locks.get(&area).copied().unwrap_or(false)
    }
    
    /// Save current layout as preferences
    pub async fn save_layout(&self) -> Result<(), UiError> {
        let current_state = self.state.get();
        let preferences = LayoutPreferences::from_state(&current_state);
        self.persistence.save_preferences(&preferences).await?;
        Ok(())
    }
    
    /// Reset layout to defaults
    pub fn reset_layout(&self) -> Result<(), UiError> {
        self.state.update(|state| {
            *state = LayoutState::default();
        });
        
        // Emit reset event
        if let Some(event_bus) = &self.event_bus {
            let ui_event = UIEvent::LayoutChanged {
                layout_id: "layout_reset".to_string(),
                changes: vec!["reset_to_defaults".to_string()],
                timestamp: chrono::Utc::now(),
            };
            event_bus.emit(ui_event)?;
        }
        
        Ok(())
    }
    
    /// Get manager ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Update configuration
    pub fn update_config(&self, new_config: LayoutConfig) -> Result<(), UiError> {
        {
            let mut config = self.config.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on config".to_string())
            })?;
            *config = new_config;
        }
        
        // Recalculate layout with new configuration
        self.recalculate_layout_positions()?;
        
        Ok(())
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> Result<LayoutConfig, UiError> {
        let config = self.config.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on config".to_string())
        })?;
        Ok(config.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_layout_manager_creation() {
        let manager = LayoutManager::new().unwrap();
        assert_ne!(manager.id(), Uuid::nil());
    }

    #[test]
    fn test_window_dimensions_update() {
        let manager = LayoutManager::new().unwrap();
        
        // Test window resize
        manager.update_window_dimensions(1600.0, 900.0).unwrap();
        
        let state = manager.get_state();
        assert_eq!(state.window_dimensions.width, 1600.0);
        assert_eq!(state.window_dimensions.height, 900.0);
    }

    #[test]
    fn test_area_resize() {
        let manager = LayoutManager::new().unwrap();
        
        let new_dimensions = LayoutDimensions {
            width: 400.0,
            height: 300.0,
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
        };
        
        manager.resize_area(LayoutArea::LeftSidebar, new_dimensions).unwrap();
        
        let rect = manager.get_area_rect(LayoutArea::LeftSidebar).unwrap();
        assert_eq!(rect.dimensions.width, 400.0);
        assert_eq!(rect.dimensions.height, 300.0);
    }

    #[test]
    fn test_area_visibility_toggle() {
        let manager = LayoutManager::new().unwrap();
        
        // Initially visible
        assert!(manager.is_area_visible(LayoutArea::LeftSidebar));
        
        // Toggle visibility
        manager.toggle_area_visibility(LayoutArea::LeftSidebar).unwrap();
        assert!(!manager.is_area_visible(LayoutArea::LeftSidebar));
        
        // Toggle back
        manager.toggle_area_visibility(LayoutArea::LeftSidebar).unwrap();
        assert!(manager.is_area_visible(LayoutArea::LeftSidebar));
    }

    #[test]
    fn test_area_lock() {
        let manager = LayoutManager::new().unwrap();
        
        // Initially unlocked
        assert!(!manager.is_area_locked(LayoutArea::LeftSidebar));
        
        // Lock area
        manager.set_area_lock(LayoutArea::LeftSidebar, true).unwrap();
        assert!(manager.is_area_locked(LayoutArea::LeftSidebar));
        
        // Try to resize locked area (should fail)
        let new_dimensions = LayoutDimensions::default();
        let result = manager.resize_area(LayoutArea::LeftSidebar, new_dimensions);
        assert!(result.is_err());
    }
}