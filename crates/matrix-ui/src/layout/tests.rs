//! Comprehensive tests for the layout system
//!
//! This module contains integration tests that verify the entire layout system
//! works correctly with all components working together.

#[cfg(test)]
mod integration_tests {
    use super::super::*;
    use crate::core::UIEventBus;
    use std::sync::Arc;
    use tokio;

    #[tokio::test]
    async fn test_complete_layout_system_integration() {
        // Create event bus
        let event_bus = Arc::new(UIEventBus::new().unwrap());
        event_bus.initialize().await.unwrap();
        
        // Create layout manager
        let mut layout_manager = LayoutManager::new().unwrap();
        layout_manager.initialize(event_bus.clone()).await.unwrap();
        
        // Test window resize
        layout_manager.update_window_dimensions(1600.0, 900.0).unwrap();
        let state = layout_manager.get_state();
        assert_eq!(state.window_dimensions.width, 1600.0);
        assert_eq!(state.window_dimensions.height, 900.0);
        
        // Test area resize
        let new_dimensions = LayoutDimensions {
            width: 350.0,
            height: 400.0,
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
        };
        
        layout_manager.resize_area(LayoutArea::LeftSidebar, new_dimensions).unwrap();
        let rect = layout_manager.get_area_rect(LayoutArea::LeftSidebar).unwrap();
        assert_eq!(rect.dimensions.width, 350.0);
        assert_eq!(rect.dimensions.height, 400.0);
        
        // Test visibility toggle
        assert!(layout_manager.is_area_visible(LayoutArea::RightSidebar));
        layout_manager.toggle_area_visibility(LayoutArea::RightSidebar).unwrap();
        assert!(!layout_manager.is_area_visible(LayoutArea::RightSidebar));
        
        // Test layout save and reset
        layout_manager.save_layout().await.unwrap();
        layout_manager.reset_layout().unwrap();
        
        // Verify reset worked
        let reset_state = layout_manager.get_state();
        assert_eq!(reset_state.window_dimensions.width, 1200.0); // Default
    }

    #[tokio::test]
    async fn test_responsive_system_integration() {
        let breakpoints = ResponsiveBreakpoints::default();
        let mut responsive_system = ResponsiveSystem::new(breakpoints).unwrap();
        responsive_system.initialize().await.unwrap();
        
        // Test breakpoint detection
        assert_eq!(responsive_system.get_current_breakpoint(600.0), Some("mobile".to_string()));
        assert_eq!(responsive_system.get_current_breakpoint(900.0), Some("tablet".to_string()));
        assert_eq!(responsive_system.get_current_breakpoint(1300.0), Some("desktop".to_string()));
        
        // Test layout retrieval
        let mobile_layout = responsive_system.get_layout_for_breakpoint("mobile").unwrap();
        assert_eq!(mobile_layout.breakpoint, "mobile");
        assert!(!mobile_layout.area_visibility[&LayoutArea::LeftSidebar]);
        
        let desktop_layout = responsive_system.get_layout_for_breakpoint("desktop").unwrap();
        assert_eq!(desktop_layout.breakpoint, "desktop");
        assert!(desktop_layout.area_visibility[&LayoutArea::LeftSidebar]);
    }

    #[tokio::test]
    async fn test_splitter_system_integration() {
        let event_bus = Arc::new(UIEventBus::new().unwrap());
        event_bus.initialize().await.unwrap();
        
        let mut splitter_manager = SplitterManager::new();
        splitter_manager.set_event_bus(event_bus.clone());
        
        // Create splitters
        let left_splitter = Splitter::new(
            SplitterDirection::Horizontal,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
        );
        let bottom_splitter = Splitter::new(
            SplitterDirection::Vertical,
            LayoutArea::MainEditor,
            LayoutArea::BottomPanel,
        );
        
        let left_id = left_splitter.id();
        let bottom_id = bottom_splitter.id();
        
        splitter_manager.add_splitter(left_splitter);
        splitter_manager.add_splitter(bottom_splitter);
        
        // Test splitter operations
        assert_eq!(splitter_manager.get_splitters().len(), 2);
        
        let left_splitter = splitter_manager.get_splitter(left_id).unwrap();
        left_splitter.set_position(320.0).unwrap();
        assert_eq!(left_splitter.get_position(), 320.0);
        
        // Test splitter filtering
        let horizontal_splitters = splitter_manager.get_splitters_by_direction(SplitterDirection::Horizontal);
        assert_eq!(horizontal_splitters.len(), 1);
        
        let vertical_splitters = splitter_manager.get_splitters_by_direction(SplitterDirection::Vertical);
        assert_eq!(vertical_splitters.len(), 1);
        
        // Test reset
        splitter_manager.reset_all_splitters().unwrap();
    }

    #[tokio::test]
    async fn test_persistence_system_integration() {
        use tempfile::TempDir;
        
        let temp_dir = TempDir::new().unwrap();
        let config = PersistenceConfig {
            storage_dir: temp_dir.path().to_path_buf(),
            max_saved_layouts: 10,
            auto_save_interval: 1,
            enable_compression: false,
            create_backups: true,
            max_backups: 3,
        };
        
        let mut persistence = LayoutPersistence::with_config(config).unwrap();
        persistence.initialize().await.unwrap();
        
        // Create test layout state
        let mut state = LayoutState::default();
        state.window_dimensions.width = 1600.0;
        state.window_dimensions.height = 900.0;
        
        // Create and save preferences
        let preferences_id = persistence.create_preference_set(
            "Test Layout".to_string(),
            "Integration test layout".to_string(),
            &state,
        ).await.unwrap();
        
        // Load preferences
        let loaded_preferences = persistence.load_preferences_by_id(preferences_id).unwrap();
        assert_eq!(loaded_preferences.name, "Test Layout");
        assert_eq!(loaded_preferences.window_dimensions.width, 1600.0);
        
        // Test export/import
        let export_path = temp_dir.path().join("exported_layout.json");
        persistence.export_preferences(preferences_id, &export_path).await.unwrap();
        
        let imported_id = persistence.import_preferences(&export_path).await.unwrap();
        let imported_preferences = persistence.load_preferences_by_id(imported_id).unwrap();
        assert_eq!(imported_preferences.name, "Test Layout");
        
        // Test deletion
        persistence.delete_preferences(preferences_id).await.unwrap();
        assert!(persistence.load_preferences_by_id(preferences_id).is_err());
    }

    #[test]
    fn test_grid_system_integration() {
        let mut grid = ResponsiveGrid::new(GridConfig::default()).unwrap();
        
        // Create default IDE layout
        grid.create_default_ide_layout().unwrap();
        
        // Validate layout
        grid.validate_layout().unwrap();
        
        // Calculate layout for different screen sizes
        let desktop_layout = grid.calculate_layout(1200.0, 800.0, "desktop").unwrap();
        assert_eq!(desktop_layout.len(), 7); // All IDE areas
        
        // Test mobile layout
        grid.create_mobile_layout().unwrap();
        let mobile_layout = grid.calculate_layout(400.0, 600.0, "mobile").unwrap();
        assert_eq!(mobile_layout.len(), 4); // Reduced areas for mobile
        
        // Verify mobile layout positions
        let title_bar = mobile_layout.get(&LayoutArea::TitleBar).unwrap();
        let main_editor = mobile_layout.get(&LayoutArea::MainEditor).unwrap();
        let status_bar = mobile_layout.get(&LayoutArea::StatusBar).unwrap();
        
        assert!(title_bar.position.y < main_editor.position.y);
        assert!(main_editor.position.y < status_bar.position.y);
    }

    #[test]
    fn test_layout_calculator_integration() {
        let mut calculator = LayoutCalculator::new();
        
        // Create test layout with overlaps
        let mut areas = HashMap::new();
        areas.insert(LayoutArea::MainEditor, LayoutRect::new(0.0, 0.0, 400.0, 300.0));
        areas.insert(LayoutArea::LeftSidebar, LayoutRect::new(100.0, 100.0, 200.0, 200.0)); // Overlapping
        areas.insert(LayoutArea::RightSidebar, LayoutRect::new(500.0, 0.0, 200.0, 300.0));
        
        let metrics = LayoutMetrics::from_layout(800.0, 600.0, areas);
        assert_eq!(metrics.overlap_count, 1);
        
        // Calculate optimal layout
        let result = calculator.calculate_optimal_layout(metrics).unwrap();
        
        // Should have resolved overlaps
        assert_eq!(result.metrics.overlap_count, 0);
        assert!(result.is_valid);
        assert!(result.metrics.space_efficiency > 0.0);
        
        // Add to history
        calculator.add_to_history(result.metrics);
        assert_eq!(calculator.get_history().len(), 1);
    }

    #[tokio::test]
    async fn test_layout_manager_with_all_systems() {
        let event_bus = Arc::new(UIEventBus::new().unwrap());
        event_bus.initialize().await.unwrap();
        
        // Create layout manager with all systems
        let mut layout_manager = LayoutManager::new().unwrap();
        layout_manager.initialize(event_bus.clone()).await.unwrap();
        
        // Test responsive behavior
        layout_manager.update_window_dimensions(600.0, 800.0).unwrap(); // Mobile size
        let state = layout_manager.get_state();
        assert_eq!(state.current_breakpoint, "mobile");
        
        layout_manager.update_window_dimensions(1200.0, 800.0).unwrap(); // Desktop size
        let state = layout_manager.get_state();
        assert_eq!(state.current_breakpoint, "desktop");
        
        // Test area operations
        layout_manager.resize_area(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 300.0,
            height: 400.0,
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
        }).unwrap();
        
        layout_manager.move_area(LayoutArea::BottomPanel, LayoutPosition { x: 300.0, y: 500.0 }).unwrap();
        
        // Test locking
        layout_manager.set_area_lock(LayoutArea::TitleBar, true).unwrap();
        assert!(layout_manager.is_area_locked(LayoutArea::TitleBar));
        
        // Try to resize locked area (should fail)
        let result = layout_manager.resize_area(LayoutArea::TitleBar, LayoutDimensions::default());
        assert!(result.is_err());
        
        // Test save and load
        layout_manager.save_layout().await.unwrap();
        
        // Modify layout
        layout_manager.toggle_area_visibility(LayoutArea::RightSidebar).unwrap();
        assert!(!layout_manager.is_area_visible(LayoutArea::RightSidebar));
        
        // Reset should restore to defaults
        layout_manager.reset_layout().unwrap();
        assert!(layout_manager.is_area_visible(LayoutArea::RightSidebar));
    }

    #[test]
    fn test_layout_performance() {
        use std::time::Instant;
        
        let mut layout_manager = LayoutManager::new().unwrap();
        
        // Measure layout calculation performance
        let start = Instant::now();
        
        for _ in 0..100 {
            layout_manager.update_window_dimensions(
                1200.0 + (rand::random::<f64>() * 400.0),
                800.0 + (rand::random::<f64>() * 200.0),
            ).unwrap();
        }
        
        let duration = start.elapsed();
        println!("100 layout calculations took: {:?}", duration);
        
        // Should complete within reasonable time (adjust threshold as needed)
        assert!(duration.as_millis() < 1000); // Less than 1 second for 100 calculations
    }

    #[test]
    fn test_layout_edge_cases() {
        let mut layout_manager = LayoutManager::new().unwrap();
        
        // Test very small window
        layout_manager.update_window_dimensions(100.0, 100.0).unwrap();
        let state = layout_manager.get_state();
        
        // Areas should be clamped to minimum sizes
        for rect in state.areas.values() {
            assert!(rect.dimensions.width >= 0.0);
            assert!(rect.dimensions.height >= 0.0);
        }
        
        // Test very large window
        layout_manager.update_window_dimensions(5000.0, 3000.0).unwrap();
        let state = layout_manager.get_state();
        assert_eq!(state.window_dimensions.width, 5000.0);
        
        // Test zero dimensions (should handle gracefully)
        let result = layout_manager.resize_area(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 0.0,
            height: 0.0,
            min_width: 0.0,
            min_height: 0.0,
            max_width: None,
            max_height: None,
        });
        
        // Should either succeed with minimum dimensions or fail gracefully
        if let Ok(_) = result {
            let rect = layout_manager.get_area_rect(LayoutArea::LeftSidebar).unwrap();
            // Should be adjusted to at least minimum from config
            assert!(rect.dimensions.width > 0.0);
            assert!(rect.dimensions.height > 0.0);
        }
    }

    #[test]
    fn test_layout_consistency() {
        let mut layout_manager = LayoutManager::new().unwrap();
        
        // Perform multiple operations and verify consistency
        let initial_state = layout_manager.get_state();
        
        // Resize multiple areas
        layout_manager.resize_area(LayoutArea::LeftSidebar, LayoutDimensions {
            width: 250.0,
            height: 350.0,
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
        }).unwrap();
        
        layout_manager.resize_area(LayoutArea::RightSidebar, LayoutDimensions {
            width: 300.0,
            height: 400.0,
            min_width: 200.0,
            min_height: 100.0,
            max_width: None,
            max_height: None,
        }).unwrap();
        
        let modified_state = layout_manager.get_state();
        
        // Verify that modifications were applied
        assert_ne!(initial_state.areas[&LayoutArea::LeftSidebar].dimensions.width,
                  modified_state.areas[&LayoutArea::LeftSidebar].dimensions.width);
        
        // Verify that unmodified areas remain unchanged
        assert_eq!(initial_state.areas[&LayoutArea::TitleBar].dimensions.height,
                  modified_state.areas[&LayoutArea::TitleBar].dimensions.height);
        
        // Verify timestamp was updated
        assert!(modified_state.last_updated > initial_state.last_updated);
    }
}

// Helper function for generating random layouts for stress testing
#[cfg(test)]
fn generate_random_layout() -> HashMap<LayoutArea, LayoutRect> {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let mut areas = HashMap::new();
    
    let areas_to_test = vec![
        LayoutArea::TitleBar,
        LayoutArea::LeftSidebar,
        LayoutArea::MainEditor,
        LayoutArea::RightSidebar,
        LayoutArea::BottomPanel,
        LayoutArea::StatusBar,
    ];
    
    for area in areas_to_test {
        let rect = LayoutRect::new(
            rng.gen_range(0.0..800.0),
            rng.gen_range(0.0..600.0),
            rng.gen_range(100.0..400.0),
            rng.gen_range(50.0..300.0),
        );
        areas.insert(area, rect);
    }
    
    areas
}

#[cfg(test)]
mod stress_tests {
    use super::*;
    
    #[test]
    fn test_layout_stress() {
        let mut layout_manager = LayoutManager::new().unwrap();
        
        // Perform many rapid layout changes
        for i in 0..1000 {
            let width = 800.0 + (i as f64 % 800.0);
            let height = 600.0 + (i as f64 % 400.0);
            
            layout_manager.update_window_dimensions(width, height).unwrap();
            
            if i % 10 == 0 {
                layout_manager.toggle_area_visibility(LayoutArea::LeftSidebar).unwrap();
            }
            
            if i % 15 == 0 {
                layout_manager.resize_area(LayoutArea::MainEditor, LayoutDimensions {
                    width: 400.0 + (i as f64 % 200.0),
                    height: 300.0 + (i as f64 % 100.0),
                    min_width: 300.0,
                    min_height: 200.0,
                    max_width: None,
                    max_height: None,
                }).unwrap();
            }
        }
        
        // Verify layout is still valid after stress test
        let final_state = layout_manager.get_state();
        assert!(final_state.areas.len() > 0);
        
        // All areas should have positive dimensions
        for rect in final_state.areas.values() {
            assert!(rect.dimensions.width > 0.0);
            assert!(rect.dimensions.height > 0.0);
        }
    }
}