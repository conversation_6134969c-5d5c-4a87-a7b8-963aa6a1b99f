//! Splitter Components with Drag-and-Drop Resizing
//!
//! This module implements interactive splitter components that allow users
//! to resize layout areas by dragging dividers between panels.

use crate::error::UiError;
use crate::core::{UIEventBus, UIEvent};
use super::{LayoutArea, LayoutEvent};
use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, empty, Decorators},
    style::{Position, CursorStyle},
    event::{Event, EventListener, EventPropagation},
    View,
};
use serde::{Serialize, Deserialize};
use std::sync::Arc;
use uuid::Uuid;

/// Splitter direction
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SplitterDirection {
    Horizontal, // Divides areas horizontally (vertical line)
    Vertical,   // Divides areas vertically (horizontal line)
}

/// Splitter configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SplitterConfig {
    /// Splitter thickness in pixels
    pub thickness: f64,
    
    /// Hover thickness (when mouse is over splitter)
    pub hover_thickness: f64,
    
    /// Minimum distance from edges
    pub min_distance: f64,
    
    /// Maximum distance from edges
    pub max_distance: f64,
    
    /// Snap threshold for automatic positioning
    pub snap_threshold: f64,
    
    /// Enable smooth animations
    pub enable_animations: bool,
    
    /// Animation duration in milliseconds
    pub animation_duration: u32,
}

impl Default for SplitterConfig {
    fn default() -> Self {
        Self {
            thickness: 4.0,
            hover_thickness: 8.0,
            min_distance: 100.0,
            max_distance: 500.0,
            snap_threshold: 10.0,
            enable_animations: true,
            animation_duration: 150,
        }
    }
}

/// Splitter state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SplitterState {
    /// Current position (offset from start)
    pub position: f64,
    
    /// Is currently being dragged
    pub is_dragging: bool,
    
    /// Is mouse hovering over splitter
    pub is_hovering: bool,
    
    /// Drag start position
    pub drag_start_position: Option<f64>,
    
    /// Initial position when drag started
    pub initial_position: f64,
    
    /// Last update timestamp
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

impl Default for SplitterState {
    fn default() -> Self {
        Self {
            position: 280.0, // Default sidebar width
            is_dragging: false,
            is_hovering: false,
            drag_start_position: None,
            initial_position: 280.0,
            last_updated: chrono::Utc::now(),
        }
    }
}

/// Splitter component
pub struct Splitter {
    /// Unique identifier
    id: Uuid,
    
    /// Splitter direction
    direction: SplitterDirection,
    
    /// Configuration
    config: SplitterConfig,
    
    /// Current state (reactive)
    state: RwSignal<SplitterState>,
    
    /// Areas that this splitter divides
    left_area: LayoutArea,
    right_area: LayoutArea,
    
    /// Event bus for notifications
    event_bus: Option<Arc<UIEventBus>>,
}

impl Splitter {
    /// Create a new splitter
    pub fn new(
        direction: SplitterDirection,
        left_area: LayoutArea,
        right_area: LayoutArea,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            direction,
            config: SplitterConfig::default(),
            state: create_rw_signal(SplitterState::default()),
            left_area,
            right_area,
            event_bus: None,
        }
    }
    
    /// Create a new splitter with custom configuration
    pub fn with_config(
        direction: SplitterDirection,
        left_area: LayoutArea,
        right_area: LayoutArea,
        config: SplitterConfig,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            direction,
            config,
            state: create_rw_signal(SplitterState::default()),
            left_area,
            right_area,
            event_bus: None,
        }
    }
    
    /// Set event bus for notifications
    pub fn set_event_bus(&mut self, event_bus: Arc<UIEventBus>) {
        self.event_bus = Some(event_bus);
    }
    
    /// Get splitter ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Get current position
    pub fn get_position(&self) -> f64 {
        self.state.get().position
    }
    
    /// Set position programmatically
    pub fn set_position(&self, position: f64) -> Result<(), UiError> {
        let clamped_position = self.clamp_position(position);
        
        self.state.update(|state| {
            state.position = clamped_position;
            state.last_updated = chrono::Utc::now();
        });
        
        self.emit_position_changed(clamped_position)?;
        
        Ok(())
    }
    
    /// Clamp position to valid range
    fn clamp_position(&self, position: f64) -> f64 {
        position.max(self.config.min_distance).min(self.config.max_distance)
    }
    
    /// Handle drag start
    fn handle_drag_start(&self, mouse_position: f64) -> Result<(), UiError> {
        self.state.update(|state| {
            state.is_dragging = true;
            state.drag_start_position = Some(mouse_position);
            state.initial_position = state.position;
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Handle drag move
    fn handle_drag_move(&self, mouse_position: f64) -> Result<(), UiError> {
        let current_state = self.state.get();
        
        if !current_state.is_dragging {
            return Ok(());
        }
        
        if let Some(drag_start) = current_state.drag_start_position {
            let delta = mouse_position - drag_start;
            let new_position = self.clamp_position(current_state.initial_position + delta);
            
            // Apply snap threshold
            let snapped_position = self.apply_snap(new_position);
            
            self.state.update(|state| {
                state.position = snapped_position;
                state.last_updated = chrono::Utc::now();
            });
            
            self.emit_position_changed(snapped_position)?;
        }
        
        Ok(())
    }
    
    /// Handle drag end
    fn handle_drag_end(&self) -> Result<(), UiError> {
        self.state.update(|state| {
            state.is_dragging = false;
            state.drag_start_position = None;
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Apply snap to common positions
    fn apply_snap(&self, position: f64) -> f64 {
        let snap_positions = vec![
            self.config.min_distance,
            self.config.min_distance + 100.0,
            (self.config.min_distance + self.config.max_distance) / 2.0, // Middle
            self.config.max_distance - 100.0,
            self.config.max_distance,
        ];
        
        for snap_pos in snap_positions {
            if (position - snap_pos).abs() < self.config.snap_threshold {
                return snap_pos;
            }
        }
        
        position
    }
    
    /// Handle mouse enter
    fn handle_mouse_enter(&self) -> Result<(), UiError> {
        self.state.update(|state| {
            state.is_hovering = true;
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Handle mouse leave
    fn handle_mouse_leave(&self) -> Result<(), UiError> {
        self.state.update(|state| {
            state.is_hovering = false;
            state.last_updated = chrono::Utc::now();
        });
        
        Ok(())
    }
    
    /// Emit position changed event
    fn emit_position_changed(&self, new_position: f64) -> Result<(), UiError> {
        if let Some(event_bus) = &self.event_bus {
            // Emit layout event
            let layout_event = LayoutEvent::SplitterDragged {
                splitter_id: self.id,
                delta: new_position,
            };
            
            // Emit UI event
            let ui_event = UIEvent::LayoutChanged {
                layout_id: format!("splitter_{}", self.id),
                changes: vec![format!("position_changed_to_{}", new_position)],
                timestamp: chrono::Utc::now(),
            };
            
            event_bus.emit(ui_event)?;
        }
        
        Ok(())
    }
    
    /// Build the splitter view
    pub fn build_view(&self) -> impl View {
        let state = self.state;
        let direction = self.direction;
        let config = self.config.clone();
        let id = self.id;
        
        // Create splitter handle
        container(empty())
            .style(move |s| {
                let current_state = state.get();
                let thickness = if current_state.is_hovering || current_state.is_dragging {
                    config.hover_thickness
                } else {
                    config.thickness
                };
                
                match direction {
                    SplitterDirection::Horizontal => {
                        s.position(Position::Absolute)
                            .width(thickness)
                            .height_full()
                            .inset_left(current_state.position - thickness / 2.0)
                            .cursor(CursorStyle::ColResize)
                            .background(if current_state.is_dragging {
                                floem::peniko::Color::rgba8(59, 130, 246, 128) // Blue with transparency
                            } else if current_state.is_hovering {
                                floem::peniko::Color::rgba8(156, 163, 175, 128) // Gray with transparency
                            } else {
                                floem::peniko::Color::rgba8(75, 85, 99, 64) // Subtle gray
                            })
                            .border_radius(2.0)
                            .z_index(10)
                    }
                    SplitterDirection::Vertical => {
                        s.position(Position::Absolute)
                            .width_full()
                            .height(thickness)
                            .inset_top(current_state.position - thickness / 2.0)
                            .cursor(CursorStyle::RowResize)
                            .background(if current_state.is_dragging {
                                floem::peniko::Color::rgba8(59, 130, 246, 128) // Blue with transparency
                            } else if current_state.is_hovering {
                                floem::peniko::Color::rgba8(156, 163, 175, 128) // Gray with transparency
                            } else {
                                floem::peniko::Color::rgba8(75, 85, 99, 64) // Subtle gray
                            })
                            .border_radius(2.0)
                            .z_index(10)
                    }
                }
            })
            .on_event(EventListener::PointerEnter, {
                let splitter_id = id;
                move |_| {
                    // Handle mouse enter
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::PointerLeave, {
                let splitter_id = id;
                move |_| {
                    // Handle mouse leave
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::PointerDown, {
                let splitter_id = id;
                move |event| {
                    if let Event::PointerDown(pointer_event) = event {
                        let mouse_pos = match direction {
                            SplitterDirection::Horizontal => pointer_event.pos.x,
                            SplitterDirection::Vertical => pointer_event.pos.y,
                        };
                        // Handle drag start
                    }
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::PointerMove, {
                let splitter_id = id;
                move |event| {
                    if let Event::PointerMove(pointer_event) = event {
                        let mouse_pos = match direction {
                            SplitterDirection::Horizontal => pointer_event.pos.x,
                            SplitterDirection::Vertical => pointer_event.pos.y,
                        };
                        // Handle drag move
                    }
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::PointerUp, {
                let splitter_id = id;
                move |_| {
                    // Handle drag end
                    EventPropagation::Continue
                }
            })
    }
    
    /// Get current state
    pub fn get_state(&self) -> SplitterState {
        self.state.get()
    }
    
    /// Get state signal for reactive updates
    pub fn get_state_signal(&self) -> RwSignal<SplitterState> {
        self.state
    }
    
    /// Check if splitter is currently being dragged
    pub fn is_dragging(&self) -> bool {
        self.state.get().is_dragging
    }
    
    /// Check if mouse is hovering over splitter
    pub fn is_hovering(&self) -> bool {
        self.state.get().is_hovering
    }
    
    /// Get the areas this splitter divides
    pub fn get_areas(&self) -> (LayoutArea, LayoutArea) {
        (self.left_area, self.right_area)
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: SplitterConfig) {
        self.config = config;
        
        // Reclamp position with new constraints
        let current_position = self.get_position();
        let new_position = self.clamp_position(current_position);
        
        if (new_position - current_position).abs() > 0.1 {
            let _ = self.set_position(new_position);
        }
    }
    
    /// Reset to default position
    pub fn reset_position(&self) -> Result<(), UiError> {
        let default_position = (self.config.min_distance + self.config.max_distance) / 2.0;
        self.set_position(default_position)
    }
}

/// Splitter manager for coordinating multiple splitters
pub struct SplitterManager {
    /// All managed splitters
    splitters: Vec<Splitter>,
    
    /// Event bus for coordination
    event_bus: Option<Arc<UIEventBus>>,
}

impl SplitterManager {
    /// Create a new splitter manager
    pub fn new() -> Self {
        Self {
            splitters: Vec::new(),
            event_bus: None,
        }
    }
    
    /// Set event bus
    pub fn set_event_bus(&mut self, event_bus: Arc<UIEventBus>) {
        self.event_bus = Some(event_bus.clone());
        
        // Update all splitters with event bus
        for splitter in &mut self.splitters {
            splitter.set_event_bus(event_bus.clone());
        }
    }
    
    /// Add a splitter
    pub fn add_splitter(&mut self, mut splitter: Splitter) {
        if let Some(event_bus) = &self.event_bus {
            splitter.set_event_bus(event_bus.clone());
        }
        self.splitters.push(splitter);
    }
    
    /// Remove a splitter by ID
    pub fn remove_splitter(&mut self, id: Uuid) -> Option<Splitter> {
        if let Some(index) = self.splitters.iter().position(|s| s.id() == id) {
            Some(self.splitters.remove(index))
        } else {
            None
        }
    }
    
    /// Get splitter by ID
    pub fn get_splitter(&self, id: Uuid) -> Option<&Splitter> {
        self.splitters.iter().find(|s| s.id() == id)
    }
    
    /// Get mutable splitter by ID
    pub fn get_splitter_mut(&mut self, id: Uuid) -> Option<&mut Splitter> {
        self.splitters.iter_mut().find(|s| s.id() == id)
    }
    
    /// Get all splitters
    pub fn get_splitters(&self) -> &[Splitter] {
        &self.splitters
    }
    
    /// Reset all splitters to default positions
    pub fn reset_all_splitters(&self) -> Result<(), UiError> {
        for splitter in &self.splitters {
            splitter.reset_position()?;
        }
        Ok(())
    }
    
    /// Get splitters by direction
    pub fn get_splitters_by_direction(&self, direction: SplitterDirection) -> Vec<&Splitter> {
        self.splitters.iter().filter(|s| s.direction == direction).collect()
    }
    
    /// Check if any splitter is currently being dragged
    pub fn is_any_dragging(&self) -> bool {
        self.splitters.iter().any(|s| s.is_dragging())
    }
    
    /// Get all splitter positions
    pub fn get_all_positions(&self) -> Vec<(Uuid, f64)> {
        self.splitters.iter().map(|s| (s.id(), s.get_position())).collect()
    }
}

impl Default for SplitterManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_splitter_creation() {
        let splitter = Splitter::new(
            SplitterDirection::Horizontal,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
        );
        
        assert_ne!(splitter.id(), Uuid::nil());
        assert_eq!(splitter.direction, SplitterDirection::Horizontal);
        assert_eq!(splitter.get_areas(), (LayoutArea::LeftSidebar, LayoutArea::MainEditor));
    }

    #[test]
    fn test_splitter_position() {
        let splitter = Splitter::new(
            SplitterDirection::Horizontal,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
        );
        
        // Test setting position
        splitter.set_position(350.0).unwrap();
        assert_eq!(splitter.get_position(), 350.0);
        
        // Test clamping
        splitter.set_position(50.0).unwrap(); // Below minimum
        assert_eq!(splitter.get_position(), 100.0); // Should be clamped to minimum
        
        splitter.set_position(600.0).unwrap(); // Above maximum
        assert_eq!(splitter.get_position(), 500.0); // Should be clamped to maximum
    }

    #[test]
    fn test_splitter_manager() {
        let mut manager = SplitterManager::new();
        
        let splitter1 = Splitter::new(
            SplitterDirection::Horizontal,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
        );
        let id1 = splitter1.id();
        
        let splitter2 = Splitter::new(
            SplitterDirection::Vertical,
            LayoutArea::MainEditor,
            LayoutArea::BottomPanel,
        );
        let id2 = splitter2.id();
        
        manager.add_splitter(splitter1);
        manager.add_splitter(splitter2);
        
        assert_eq!(manager.get_splitters().len(), 2);
        assert!(manager.get_splitter(id1).is_some());
        assert!(manager.get_splitter(id2).is_some());
        
        // Test removal
        let removed = manager.remove_splitter(id1);
        assert!(removed.is_some());
        assert_eq!(manager.get_splitters().len(), 1);
    }

    #[test]
    fn test_splitter_snap() {
        let splitter = Splitter::new(
            SplitterDirection::Horizontal,
            LayoutArea::LeftSidebar,
            LayoutArea::MainEditor,
        );
        
        // Test snap to minimum
        let snapped = splitter.apply_snap(105.0); // Close to minimum (100.0)
        assert_eq!(snapped, 100.0);
        
        // Test snap to middle
        let middle = (100.0 + 500.0) / 2.0; // 300.0
        let snapped = splitter.apply_snap(295.0); // Close to middle
        assert_eq!(snapped, middle);
        
        // Test no snap
        let snapped = splitter.apply_snap(250.0); // Not close to any snap position
        assert_eq!(snapped, 250.0);
    }
}