//! Layout Persistence System for Saving User Preferences
//!
//! This module handles saving and loading layout configurations,
//! allowing users to maintain their preferred layout across sessions.

use crate::error::UiError;
use super::{LayoutArea, LayoutDimensions, LayoutPosition, LayoutRect, LayoutState};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, RwLock};
use tokio::fs;
use uuid::Uuid;

/// Layout preferences that can be persisted
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutPreferences {
    /// Preference set ID
    pub id: Uuid,
    
    /// Name of the preference set
    pub name: String,
    
    /// Description
    pub description: String,
    
    /// Area layouts (positions and dimensions)
    pub area_layouts: HashMap<LayoutArea, LayoutRect>,
    
    /// Splitter positions
    pub splitter_positions: HashMap<String, f64>,
    
    /// Area visibility states
    pub area_visibility: HashMap<LayoutArea, bool>,
    
    /// Area lock states
    pub area_locks: HashMap<LayoutArea, bool>,
    
    /// Window dimensions when saved
    pub window_dimensions: LayoutDimensions,
    
    /// Responsive breakpoint when saved
    pub breakpoint: String,
    
    /// Custom properties
    pub properties: HashMap<String, serde_json::Value>,
    
    /// Creation timestamp
    pub created_at: chrono::DateTime<chrono::Utc>,
    
    /// Last modified timestamp
    pub modified_at: chrono::DateTime<chrono::Utc>,
    
    /// Version for compatibility
    pub version: String,
}

impl LayoutPreferences {
    /// Create preferences from current layout state
    pub fn from_state(state: &LayoutState) -> Self {
        Self {
            id: Uuid::new_v4(),
            name: "Current Layout".to_string(),
            description: "Current layout configuration".to_string(),
            area_layouts: state.areas.clone(),
            splitter_positions: state.splitter_positions.clone(),
            area_visibility: state.area_visibility.clone(),
            area_locks: state.area_locks.clone(),
            window_dimensions: state.window_dimensions,
            breakpoint: state.current_breakpoint.clone(),
            properties: HashMap::new(),
            created_at: chrono::Utc::now(),
            modified_at: chrono::Utc::now(),
            version: "1.0.0".to_string(),
        }
    }
    
    /// Create a named preference set
    pub fn named(name: String, description: String, state: &LayoutState) -> Self {
        let mut prefs = Self::from_state(state);
        prefs.name = name;
        prefs.description = description;
        prefs
    }
    
    /// Update modification timestamp
    pub fn touch(&mut self) {
        self.modified_at = chrono::Utc::now();
    }
    
    /// Add custom property
    pub fn set_property(&mut self, key: String, value: serde_json::Value) {
        self.properties.insert(key, value);
        self.touch();
    }
    
    /// Get custom property
    pub fn get_property(&self, key: &str) -> Option<&serde_json::Value> {
        self.properties.get(key)
    }
    
    /// Check if preferences are compatible with current version
    pub fn is_compatible(&self) -> bool {
        // Simple version check - in a real implementation, you'd have more sophisticated logic
        self.version.starts_with("1.")
    }
}

impl Default for LayoutPreferences {
    fn default() -> Self {
        let default_state = LayoutState::default();
        Self::from_state(&default_state)
    }
}

/// Persistence configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PersistenceConfig {
    /// Directory to store layout files
    pub storage_dir: PathBuf,
    
    /// Maximum number of saved layouts
    pub max_saved_layouts: usize,
    
    /// Auto-save interval in seconds
    pub auto_save_interval: u64,
    
    /// Enable compression for saved files
    pub enable_compression: bool,
    
    /// Backup old layouts before overwriting
    pub create_backups: bool,
    
    /// Maximum number of backups to keep
    pub max_backups: usize,
}

impl Default for PersistenceConfig {
    fn default() -> Self {
        let storage_dir = dirs::config_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("matrix-ide")
            .join("layouts");
        
        Self {
            storage_dir,
            max_saved_layouts: 50,
            auto_save_interval: 30, // 30 seconds
            enable_compression: false, // Keep simple for now
            create_backups: true,
            max_backups: 5,
        }
    }
}

/// Layout persistence manager
pub struct LayoutPersistence {
    /// Configuration
    config: PersistenceConfig,
    
    /// Currently loaded preferences
    current_preferences: Arc<RwLock<Option<LayoutPreferences>>>,
    
    /// Available preference sets
    available_preferences: Arc<RwLock<HashMap<Uuid, LayoutPreferences>>>,
}

impl LayoutPersistence {
    /// Create a new persistence manager
    pub fn new() -> Result<Self, UiError> {
        let config = PersistenceConfig::default();
        
        Ok(Self {
            config,
            current_preferences: Arc::new(RwLock::new(None)),
            available_preferences: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Create with custom configuration
    pub fn with_config(config: PersistenceConfig) -> Result<Self, UiError> {
        Ok(Self {
            config,
            current_preferences: Arc::new(RwLock::new(None)),
            available_preferences: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Initialize the persistence system
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Create storage directory if it doesn't exist
        fs::create_dir_all(&self.config.storage_dir).await?;
        
        // Load all available preferences
        self.load_all_preferences().await?;
        
        // Load default preferences if available
        if let Some(default_prefs) = self.find_default_preferences() {
            let mut current = self.current_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on current preferences".to_string())
            })?;
            *current = Some(default_prefs);
        }
        
        Ok(())
    }
    
    /// Load all available preferences from storage
    async fn load_all_preferences(&self) -> Result<(), UiError> {
        let mut entries = fs::read_dir(&self.config.storage_dir).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
                match self.load_preferences_from_file(&path).await {
                    Ok(preferences) => {
                        let mut available = self.available_preferences.write().map_err(|_| {
                            UiError::LockError("Failed to acquire write lock on available preferences".to_string())
                        })?;
                        available.insert(preferences.id, preferences);
                    }
                    Err(e) => {
                        eprintln!("Failed to load preferences from {}: {}", path.display(), e);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Load preferences from a specific file
    async fn load_preferences_from_file(&self, path: &PathBuf) -> Result<LayoutPreferences, UiError> {
        let content = fs::read_to_string(path).await?;
        let preferences: LayoutPreferences = serde_json::from_str(&content)?;
        
        // Check compatibility
        if !preferences.is_compatible() {
            return Err(UiError::LayoutError(
                format!("Layout preferences version {} is not compatible", preferences.version)
            ));
        }
        
        Ok(preferences)
    }
    
    /// Find default preferences (most recently used or named "default")
    fn find_default_preferences(&self) -> Option<LayoutPreferences> {
        let available = self.available_preferences.read().ok()?;
        
        // First, look for a preference set named "default"
        for prefs in available.values() {
            if prefs.name.to_lowercase() == "default" {
                return Some(prefs.clone());
            }
        }
        
        // If no default found, return the most recently modified
        available
            .values()
            .max_by_key(|prefs| prefs.modified_at)
            .cloned()
    }
    
    /// Save preferences to storage
    pub async fn save_preferences(&self, preferences: &LayoutPreferences) -> Result<(), UiError> {
        // Create backup if enabled
        if self.config.create_backups {
            self.create_backup(preferences).await?;
        }
        
        // Generate filename
        let filename = format!("{}.json", preferences.id);
        let file_path = self.config.storage_dir.join(filename);
        
        // Serialize preferences
        let content = serde_json::to_string_pretty(preferences)?;
        
        // Write to file
        fs::write(&file_path, content).await?;
        
        // Update in-memory storage
        {
            let mut available = self.available_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on available preferences".to_string())
            })?;
            available.insert(preferences.id, preferences.clone());
        }
        
        {
            let mut current = self.current_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on current preferences".to_string())
            })?;
            *current = Some(preferences.clone());
        }
        
        // Clean up old preferences if we exceed the limit
        self.cleanup_old_preferences().await?;
        
        Ok(())
    }
    
    /// Create a backup of existing preferences
    async fn create_backup(&self, preferences: &LayoutPreferences) -> Result<(), UiError> {
        let existing_prefs = {
            let available = self.available_preferences.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on available preferences".to_string())
            })?;
            available.get(&preferences.id).cloned()
        };
        
        if let Some(existing) = existing_prefs {
            let backup_dir = self.config.storage_dir.join("backups");
            fs::create_dir_all(&backup_dir).await?;
            
            let timestamp = existing.modified_at.format("%Y%m%d_%H%M%S");
            let backup_filename = format!("{}_{}.json", preferences.id, timestamp);
            let backup_path = backup_dir.join(backup_filename);
            
            let content = serde_json::to_string_pretty(&existing)?;
            fs::write(&backup_path, content).await?;
            
            // Clean up old backups
            self.cleanup_old_backups(&preferences.id).await?;
        }
        
        Ok(())
    }
    
    /// Clean up old backups for a specific preference ID
    async fn cleanup_old_backups(&self, preference_id: &Uuid) -> Result<(), UiError> {
        let backup_dir = self.config.storage_dir.join("backups");
        
        if !backup_dir.exists() {
            return Ok(());
        }
        
        let mut entries = fs::read_dir(&backup_dir).await?;
        let mut backup_files = Vec::new();
        
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                if filename.starts_with(&preference_id.to_string()) {
                    if let Ok(metadata) = entry.metadata().await {
                        backup_files.push((path, metadata.modified().unwrap_or(std::time::UNIX_EPOCH)));
                    }
                }
            }
        }
        
        // Sort by modification time (newest first)
        backup_files.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Remove excess backups
        for (path, _) in backup_files.into_iter().skip(self.config.max_backups) {
            let _ = fs::remove_file(path).await;
        }
        
        Ok(())
    }
    
    /// Clean up old preferences if we exceed the limit
    async fn cleanup_old_preferences(&self) -> Result<(), UiError> {
        let prefs_to_remove = {
            let available = self.available_preferences.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on available preferences".to_string())
            })?;
            
            if available.len() <= self.config.max_saved_layouts {
                return Ok(());
            }
            
            // Sort by modification time (oldest first)
            let mut prefs_by_time: Vec<_> = available.values().collect();
            prefs_by_time.sort_by_key(|prefs| prefs.modified_at);
            
            // Get IDs to remove
            let to_remove = available.len() - self.config.max_saved_layouts;
            prefs_by_time.into_iter().take(to_remove).map(|p| p.id).collect::<Vec<_>>()
        };
        
        // Remove from storage and memory
        for id in prefs_to_remove {
            // Remove from storage
            let filename = format!("{}.json", id);
            let file_path = self.config.storage_dir.join(filename);
            let _ = fs::remove_file(file_path).await;
            
            // Remove from memory
            let mut available = self.available_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on available preferences".to_string())
            })?;
            available.remove(&id);
        }
        
        Ok(())
    }
    
    /// Load preferences by ID
    pub async fn load_preferences(&self) -> Result<LayoutPreferences, UiError> {
        let current = self.current_preferences.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on current preferences".to_string())
        })?;
        
        if let Some(prefs) = current.as_ref() {
            Ok(prefs.clone())
        } else if let Some(default_prefs) = self.find_default_preferences() {
            Ok(default_prefs)
        } else {
            // Return default preferences
            Ok(LayoutPreferences::default())
        }
    }
    
    /// Load specific preferences by ID
    pub fn load_preferences_by_id(&self, id: Uuid) -> Result<LayoutPreferences, UiError> {
        let available = self.available_preferences.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on available preferences".to_string())
        })?;
        
        available
            .get(&id)
            .cloned()
            .ok_or_else(|| UiError::LayoutError(format!("Preferences with ID {} not found", id)))
    }
    
    /// Get all available preferences
    pub fn get_available_preferences(&self) -> Vec<LayoutPreferences> {
        match self.available_preferences.read() {
            Ok(available) => available.values().cloned().collect(),
            Err(_) => Vec::new(), // If lock is poisoned, return empty vec
        }
    }
    
    /// Delete preferences by ID
    pub async fn delete_preferences(&self, id: Uuid) -> Result<(), UiError> {
        // Remove from storage
        let filename = format!("{}.json", id);
        let file_path = self.config.storage_dir.join(filename);
        
        if file_path.exists() {
            fs::remove_file(file_path).await?;
        }
        
        // Remove from memory
        {
            let mut available = self.available_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on available preferences".to_string())
            })?;
            available.remove(&id);
        }
        
        // Clear current preferences if it was the deleted one
        {
            let mut current = self.current_preferences.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on current preferences".to_string())
            })?;
            
            if let Some(ref current_prefs) = *current {
                if current_prefs.id == id {
                    *current = None;
                }
            }
        }
        
        Ok(())
    }
    
    /// Export preferences to a specific file
    pub async fn export_preferences(&self, id: Uuid, export_path: &PathBuf) -> Result<(), UiError> {
        let preferences = self.load_preferences_by_id(id)?;
        let content = serde_json::to_string_pretty(&preferences)?;
        fs::write(export_path, content).await?;
        Ok(())
    }
    
    /// Import preferences from a file
    pub async fn import_preferences(&self, import_path: &PathBuf) -> Result<Uuid, UiError> {
        let mut preferences = self.load_preferences_from_file(import_path).await?;
        
        // Generate new ID to avoid conflicts
        preferences.id = Uuid::new_v4();
        preferences.modified_at = chrono::Utc::now();
        
        // Save the imported preferences
        self.save_preferences(&preferences).await?;
        
        Ok(preferences.id)
    }
    
    /// Create a new preference set from current state
    pub async fn create_preference_set(
        &self,
        name: String,
        description: String,
        state: &LayoutState,
    ) -> Result<Uuid, UiError> {
        let preferences = LayoutPreferences::named(name, description, state);
        let id = preferences.id;
        
        self.save_preferences(&preferences).await?;
        
        Ok(id)
    }
    
    /// Update existing preferences
    pub async fn update_preferences(&self, mut preferences: LayoutPreferences) -> Result<(), UiError> {
        preferences.touch();
        self.save_preferences(&preferences).await
    }
    
    /// Get current preferences
    pub fn get_current_preferences(&self) -> Option<LayoutPreferences> {
        let current = self.current_preferences.read().ok()?;
        current.clone()
    }
    
    /// Set current preferences
    pub fn set_current_preferences(&self, preferences: LayoutPreferences) {
        if let Ok(mut current) = self.current_preferences.write() {
            *current = Some(preferences);
        }
    }
    
    /// Get storage statistics
    pub fn get_storage_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();
        
        if let Ok(available) = self.available_preferences.read() {
            stats.insert("total_preferences".to_string(), serde_json::json!(available.len()));
        }
        
        stats.insert("storage_dir".to_string(), serde_json::json!(self.config.storage_dir.display().to_string()));
        stats.insert("max_saved_layouts".to_string(), serde_json::json!(self.config.max_saved_layouts));
        
        if let Ok(current) = self.current_preferences.read() {
            if let Some(ref current_prefs) = *current {
                stats.insert("current_preference_id".to_string(), serde_json::json!(current_prefs.id));
                stats.insert("current_preference_name".to_string(), serde_json::json!(current_prefs.name));
            }
        }
        
        stats
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_persistence_creation() {
        let persistence = LayoutPersistence::new().unwrap();
        assert!(persistence.available_preferences.read().unwrap().is_empty());
        assert!(persistence.current_preferences.read().unwrap().is_none());
    }

    #[tokio::test]
    async fn test_save_and_load_preferences() {
        let temp_dir = TempDir::new().unwrap();
        let config = PersistenceConfig {
            storage_dir: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        
        let mut persistence = LayoutPersistence::with_config(config).unwrap();
        persistence.initialize().await.unwrap();
        
        // Create test preferences
        let state = LayoutState::default();
        let preferences = LayoutPreferences::named(
            "Test Layout".to_string(),
            "Test description".to_string(),
            &state,
        );
        let id = preferences.id;
        
        // Save preferences
        persistence.save_preferences(&preferences).await.unwrap();
        
        // Load preferences
        let loaded = persistence.load_preferences_by_id(id).unwrap();
        assert_eq!(loaded.name, "Test Layout");
        assert_eq!(loaded.description, "Test description");
    }

    #[tokio::test]
    async fn test_preference_cleanup() {
        let temp_dir = TempDir::new().unwrap();
        let config = PersistenceConfig {
            storage_dir: temp_dir.path().to_path_buf(),
            max_saved_layouts: 2, // Very low limit for testing
            ..Default::default()
        };
        
        let mut persistence = LayoutPersistence::with_config(config).unwrap();
        persistence.initialize().await.unwrap();
        
        let state = LayoutState::default();
        
        // Save 3 preferences (exceeds limit)
        for i in 0..3 {
            let preferences = LayoutPreferences::named(
                format!("Layout {}", i),
                format!("Description {}", i),
                &state,
            );
            persistence.save_preferences(&preferences).await.unwrap();
            
            // Add small delay to ensure different timestamps
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
        
        // Should only have 2 preferences (oldest should be cleaned up)
        assert_eq!(persistence.available_preferences.read().unwrap().len(), 2);
    }

    #[test]
    fn test_layout_preferences_from_state() {
        let state = LayoutState::default();
        let preferences = LayoutPreferences::from_state(&state);
        
        assert_eq!(preferences.area_layouts.len(), state.areas.len());
        assert_eq!(preferences.area_visibility.len(), state.area_visibility.len());
        assert_eq!(preferences.window_dimensions.width, state.window_dimensions.width);
    }

    #[test]
    fn test_preferences_properties() {
        let state = LayoutState::default();
        let mut preferences = LayoutPreferences::from_state(&state);
        
        // Test setting and getting properties
        preferences.set_property("theme".to_string(), serde_json::json!("dark"));
        preferences.set_property("font_size".to_string(), serde_json::json!(14));
        
        assert_eq!(preferences.get_property("theme"), Some(&serde_json::json!("dark")));
        assert_eq!(preferences.get_property("font_size"), Some(&serde_json::json!(14)));
        assert_eq!(preferences.get_property("nonexistent"), None);
    }
}