//! Responsive Grid System for Layout Management
//!
//! This module implements a flexible grid system that adapts to different
//! screen sizes and provides consistent spacing and alignment.

use crate::error::UiError;
use super::{LayoutArea, LayoutDimensions, LayoutPosition, LayoutRect};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Grid configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GridConfig {
    /// Number of columns in the grid
    pub columns: u32,
    
    /// Number of rows in the grid
    pub rows: u32,
    
    /// Gap between grid items (horizontal)
    pub column_gap: f64,
    
    /// Gap between grid items (vertical)
    pub row_gap: f64,
    
    /// Padding around the entire grid
    pub padding: GridPadding,
    
    /// Minimum column width
    pub min_column_width: f64,
    
    /// Minimum row height
    pub min_row_height: f64,
    
    /// Enable responsive behavior
    pub responsive: bool,
    
    /// Responsive breakpoints for column count
    pub responsive_columns: HashMap<String, u32>,
}

impl Default for GridConfig {
    fn default() -> Self {
        let mut responsive_columns = HashMap::new();
        responsive_columns.insert("mobile".to_string(), 1);
        responsive_columns.insert("tablet".to_string(), 2);
        responsive_columns.insert("desktop".to_string(), 12);
        responsive_columns.insert("large_desktop".to_string(), 12);
        responsive_columns.insert("ultra_wide".to_string(), 16);
        
        Self {
            columns: 12,
            rows: 8,
            column_gap: 16.0,
            row_gap: 16.0,
            padding: GridPadding::default(),
            min_column_width: 60.0,
            min_row_height: 40.0,
            responsive: true,
            responsive_columns,
        }
    }
}

/// Grid padding configuration
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct GridPadding {
    pub top: f64,
    pub right: f64,
    pub bottom: f64,
    pub left: f64,
}

impl Default for GridPadding {
    fn default() -> Self {
        Self {
            top: 16.0,
            right: 16.0,
            bottom: 16.0,
            left: 16.0,
        }
    }
}

impl GridPadding {
    pub fn uniform(padding: f64) -> Self {
        Self {
            top: padding,
            right: padding,
            bottom: padding,
            left: padding,
        }
    }
    
    pub fn horizontal_vertical(horizontal: f64, vertical: f64) -> Self {
        Self {
            top: vertical,
            right: horizontal,
            bottom: vertical,
            left: horizontal,
        }
    }
}

/// Grid item configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridItem {
    /// Area this grid item represents
    pub area: LayoutArea,
    
    /// Grid area definition
    pub grid_area: GridArea,
    
    /// Item-specific properties
    pub properties: HashMap<String, serde_json::Value>,
}

/// Grid area definition (CSS Grid-like)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridArea {
    /// Starting column (1-based)
    pub column_start: u32,
    
    /// Ending column (1-based, exclusive)
    pub column_end: u32,
    
    /// Starting row (1-based)
    pub row_start: u32,
    
    /// Ending row (1-based, exclusive)
    pub row_end: u32,
}

impl GridArea {
    /// Create a new grid area
    pub fn new(column_start: u32, column_end: u32, row_start: u32, row_end: u32) -> Self {
        Self {
            column_start,
            column_end,
            row_start,
            row_end,
        }
    }
    
    /// Create a grid area spanning a single cell
    pub fn single_cell(column: u32, row: u32) -> Self {
        Self {
            column_start: column,
            column_end: column + 1,
            row_start: row,
            row_end: row + 1,
        }
    }
    
    /// Create a grid area spanning multiple columns in a single row
    pub fn span_columns(column_start: u32, column_span: u32, row: u32) -> Self {
        Self {
            column_start,
            column_end: column_start + column_span,
            row_start: row,
            row_end: row + 1,
        }
    }
    
    /// Create a grid area spanning multiple rows in a single column
    pub fn span_rows(column: u32, row_start: u32, row_span: u32) -> Self {
        Self {
            column_start: column,
            column_end: column + 1,
            row_start,
            row_end: row_start + row_span,
        }
    }
    
    /// Get column span
    pub fn column_span(&self) -> u32 {
        self.column_end - self.column_start
    }
    
    /// Get row span
    pub fn row_span(&self) -> u32 {
        self.row_end - self.row_start
    }
    
    /// Check if this area overlaps with another
    pub fn overlaps(&self, other: &GridArea) -> bool {
        !(self.column_end <= other.column_start
            || other.column_end <= self.column_start
            || self.row_end <= other.row_start
            || other.row_end <= self.row_start)
    }
}

/// Responsive grid system
pub struct ResponsiveGrid {
    /// Grid configuration
    config: GridConfig,
    
    /// Grid items
    items: Vec<GridItem>,
    
    /// Current breakpoint
    current_breakpoint: String,
    
    /// Calculated grid dimensions
    calculated_dimensions: Option<GridDimensions>,
}

/// Calculated grid dimensions
#[derive(Debug, Clone)]
pub struct GridDimensions {
    /// Total grid width
    pub total_width: f64,
    
    /// Total grid height
    pub total_height: f64,
    
    /// Column width
    pub column_width: f64,
    
    /// Row height
    pub row_height: f64,
    
    /// Number of columns
    pub columns: u32,
    
    /// Number of rows
    pub rows: u32,
    
    /// Available content width (excluding padding)
    pub content_width: f64,
    
    /// Available content height (excluding padding)
    pub content_height: f64,
}

impl ResponsiveGrid {
    /// Create a new responsive grid
    pub fn new(config: GridConfig) -> Result<Self, UiError> {
        Ok(Self {
            config,
            items: Vec::new(),
            current_breakpoint: "desktop".to_string(),
            calculated_dimensions: None,
        })
    }
    
    /// Add a grid item
    pub fn add_item(&mut self, item: GridItem) -> Result<(), UiError> {
        // Check for overlaps with existing items
        for existing_item in &self.items {
            if item.grid_area.overlaps(&existing_item.grid_area) {
                return Err(UiError::LayoutError(
                    format!("Grid item {:?} overlaps with existing item {:?}", 
                           item.area, existing_item.area)
                ));
            }
        }
        
        self.items.push(item);
        Ok(())
    }
    
    /// Remove a grid item by area
    pub fn remove_item(&mut self, area: LayoutArea) -> Option<GridItem> {
        if let Some(index) = self.items.iter().position(|item| item.area == area) {
            Some(self.items.remove(index))
        } else {
            None
        }
    }
    
    /// Get grid item by area
    pub fn get_item(&self, area: LayoutArea) -> Option<&GridItem> {
        self.items.iter().find(|item| item.area == area)
    }
    
    /// Get mutable grid item by area
    pub fn get_item_mut(&mut self, area: LayoutArea) -> Option<&mut GridItem> {
        self.items.iter_mut().find(|item| item.area == area)
    }
    
    /// Calculate grid layout for given container dimensions
    pub fn calculate_layout(&mut self, container_width: f64, container_height: f64, breakpoint: &str) -> Result<HashMap<LayoutArea, LayoutRect>, UiError> {
        self.current_breakpoint = breakpoint.to_string();
        
        // Get responsive column count
        let columns = if self.config.responsive {
            self.config.responsive_columns.get(breakpoint)
                .copied()
                .unwrap_or(self.config.columns)
        } else {
            self.config.columns
        };
        
        // Calculate dimensions
        let content_width = container_width - self.config.padding.left - self.config.padding.right;
        let content_height = container_height - self.config.padding.top - self.config.padding.bottom;
        
        let column_width = (content_width - (columns - 1) as f64 * self.config.column_gap) / columns as f64;
        let row_height = (content_height - (self.config.rows - 1) as f64 * self.config.row_gap) / self.config.rows as f64;
        
        // Ensure minimum dimensions
        let column_width = column_width.max(self.config.min_column_width);
        let row_height = row_height.max(self.config.min_row_height);
        
        // Store calculated dimensions
        self.calculated_dimensions = Some(GridDimensions {
            total_width: container_width,
            total_height: container_height,
            column_width,
            row_height,
            columns,
            rows: self.config.rows,
            content_width,
            content_height,
        });
        
        // Calculate item positions
        let mut layout = HashMap::new();
        
        for item in &self.items {
            let rect = self.calculate_item_rect(&item.grid_area, column_width, row_height)?;
            layout.insert(item.area, rect);
        }
        
        Ok(layout)
    }
    
    /// Calculate rectangle for a grid area
    fn calculate_item_rect(&self, grid_area: &GridArea, column_width: f64, row_height: f64) -> Result<LayoutRect, UiError> {
        // Calculate position
        let x = self.config.padding.left + 
                (grid_area.column_start - 1) as f64 * (column_width + self.config.column_gap);
        let y = self.config.padding.top + 
                (grid_area.row_start - 1) as f64 * (row_height + self.config.row_gap);
        
        // Calculate dimensions
        let width = grid_area.column_span() as f64 * column_width + 
                   (grid_area.column_span() - 1) as f64 * self.config.column_gap;
        let height = grid_area.row_span() as f64 * row_height + 
                    (grid_area.row_span() - 1) as f64 * self.config.row_gap;
        
        Ok(LayoutRect {
            position: LayoutPosition { x, y },
            dimensions: LayoutDimensions {
                width,
                height,
                min_width: self.config.min_column_width,
                min_height: self.config.min_row_height,
                max_width: None,
                max_height: None,
            },
        })
    }
    
    /// Get calculated grid dimensions
    pub fn get_dimensions(&self) -> Option<&GridDimensions> {
        self.calculated_dimensions.as_ref()
    }
    
    /// Update grid configuration
    pub fn update_config(&mut self, config: GridConfig) {
        self.config = config;
        self.calculated_dimensions = None; // Force recalculation
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> &GridConfig {
        &self.config
    }
    
    /// Get all grid items
    pub fn get_items(&self) -> &[GridItem] {
        &self.items
    }
    
    /// Clear all grid items
    pub fn clear_items(&mut self) {
        self.items.clear();
    }
    
    /// Create default IDE layout
    pub fn create_default_ide_layout(&mut self) -> Result<(), UiError> {
        self.clear_items();
        
        // Title bar - spans full width at top
        self.add_item(GridItem {
            area: LayoutArea::TitleBar,
            grid_area: GridArea::span_columns(1, self.config.columns, 1),
            properties: HashMap::new(),
        })?;
        
        // Menu bar - spans full width below title bar
        self.add_item(GridItem {
            area: LayoutArea::MenuBar,
            grid_area: GridArea::span_columns(1, self.config.columns, 2),
            properties: HashMap::new(),
        })?;
        
        // Left sidebar - spans left side
        self.add_item(GridItem {
            area: LayoutArea::LeftSidebar,
            grid_area: GridArea::span_rows(1, 3, self.config.rows - 4),
            properties: HashMap::new(),
        })?;
        
        // Main editor - center area
        let editor_columns = self.config.columns - 4; // Leave space for sidebars
        self.add_item(GridItem {
            area: LayoutArea::MainEditor,
            grid_area: GridArea::new(2, 2 + editor_columns, 3, self.config.rows - 2),
            properties: HashMap::new(),
        })?;
        
        // Right sidebar - spans right side
        self.add_item(GridItem {
            area: LayoutArea::RightSidebar,
            grid_area: GridArea::span_rows(self.config.columns, 3, self.config.rows - 4),
            properties: HashMap::new(),
        })?;
        
        // Bottom panel - spans editor and right sidebar width
        self.add_item(GridItem {
            area: LayoutArea::BottomPanel,
            grid_area: GridArea::span_columns(2, editor_columns + 1, self.config.rows - 1),
            properties: HashMap::new(),
        })?;
        
        // Status bar - spans full width at bottom
        self.add_item(GridItem {
            area: LayoutArea::StatusBar,
            grid_area: GridArea::span_columns(1, self.config.columns, self.config.rows),
            properties: HashMap::new(),
        })?;
        
        Ok(())
    }
    
    /// Create mobile layout
    pub fn create_mobile_layout(&mut self) -> Result<(), UiError> {
        self.clear_items();
        
        // Mobile layout - single column, stacked vertically
        self.add_item(GridItem {
            area: LayoutArea::TitleBar,
            grid_area: GridArea::single_cell(1, 1),
            properties: HashMap::new(),
        })?;
        
        self.add_item(GridItem {
            area: LayoutArea::MainEditor,
            grid_area: GridArea::span_rows(1, 2, 4), // Takes most space
            properties: HashMap::new(),
        })?;
        
        self.add_item(GridItem {
            area: LayoutArea::BottomPanel,
            grid_area: GridArea::span_rows(1, 6, 2),
            properties: HashMap::new(),
        })?;
        
        self.add_item(GridItem {
            area: LayoutArea::StatusBar,
            grid_area: GridArea::single_cell(1, 8),
            properties: HashMap::new(),
        })?;
        
        Ok(())
    }
    
    /// Validate grid layout
    pub fn validate_layout(&self) -> Result<(), UiError> {
        // Check for overlaps
        for (i, item1) in self.items.iter().enumerate() {
            for (j, item2) in self.items.iter().enumerate() {
                if i != j && item1.grid_area.overlaps(&item2.grid_area) {
                    return Err(UiError::LayoutError(
                        format!("Grid items {:?} and {:?} overlap", item1.area, item2.area)
                    ));
                }
            }
        }
        
        // Check bounds
        for item in &self.items {
            if item.grid_area.column_start < 1 || item.grid_area.column_end > self.config.columns + 1 {
                return Err(UiError::LayoutError(
                    format!("Grid item {:?} exceeds column bounds", item.area)
                ));
            }
            
            if item.grid_area.row_start < 1 || item.grid_area.row_end > self.config.rows + 1 {
                return Err(UiError::LayoutError(
                    format!("Grid item {:?} exceeds row bounds", item.area)
                ));
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_grid_area_creation() {
        let area = GridArea::new(1, 3, 2, 4);
        assert_eq!(area.column_span(), 2);
        assert_eq!(area.row_span(), 2);
        
        let single = GridArea::single_cell(2, 3);
        assert_eq!(single.column_span(), 1);
        assert_eq!(single.row_span(), 1);
        
        let span_cols = GridArea::span_columns(1, 4, 2);
        assert_eq!(span_cols.column_span(), 4);
        assert_eq!(span_cols.row_span(), 1);
    }

    #[test]
    fn test_grid_area_overlap() {
        let area1 = GridArea::new(1, 3, 1, 3);
        let area2 = GridArea::new(2, 4, 2, 4);
        let area3 = GridArea::new(5, 7, 1, 3);
        
        assert!(area1.overlaps(&area2));
        assert!(!area1.overlaps(&area3));
    }

    #[test]
    fn test_responsive_grid_creation() {
        let config = GridConfig::default();
        let grid = ResponsiveGrid::new(config).unwrap();
        
        assert_eq!(grid.items.len(), 0);
        assert_eq!(grid.current_breakpoint, "desktop");
    }

    #[test]
    fn test_grid_item_management() {
        let mut grid = ResponsiveGrid::new(GridConfig::default()).unwrap();
        
        let item = GridItem {
            area: LayoutArea::LeftSidebar,
            grid_area: GridArea::single_cell(1, 1),
            properties: HashMap::new(),
        };
        
        grid.add_item(item).unwrap();
        assert_eq!(grid.items.len(), 1);
        
        let retrieved = grid.get_item(LayoutArea::LeftSidebar);
        assert!(retrieved.is_some());
        
        let removed = grid.remove_item(LayoutArea::LeftSidebar);
        assert!(removed.is_some());
        assert_eq!(grid.items.len(), 0);
    }

    #[test]
    fn test_grid_overlap_detection() {
        let mut grid = ResponsiveGrid::new(GridConfig::default()).unwrap();
        
        let item1 = GridItem {
            area: LayoutArea::LeftSidebar,
            grid_area: GridArea::new(1, 3, 1, 3),
            properties: HashMap::new(),
        };
        
        let item2 = GridItem {
            area: LayoutArea::MainEditor,
            grid_area: GridArea::new(2, 4, 2, 4), // Overlaps with item1
            properties: HashMap::new(),
        };
        
        grid.add_item(item1).unwrap();
        let result = grid.add_item(item2);
        assert!(result.is_err());
    }

    #[test]
    fn test_default_ide_layout() {
        let mut grid = ResponsiveGrid::new(GridConfig::default()).unwrap();
        grid.create_default_ide_layout().unwrap();
        
        // Should have all main IDE areas
        assert!(grid.get_item(LayoutArea::TitleBar).is_some());
        assert!(grid.get_item(LayoutArea::MenuBar).is_some());
        assert!(grid.get_item(LayoutArea::LeftSidebar).is_some());
        assert!(grid.get_item(LayoutArea::MainEditor).is_some());
        assert!(grid.get_item(LayoutArea::RightSidebar).is_some());
        assert!(grid.get_item(LayoutArea::BottomPanel).is_some());
        assert!(grid.get_item(LayoutArea::StatusBar).is_some());
        
        // Validate layout
        grid.validate_layout().unwrap();
    }

    #[test]
    fn test_layout_calculation() {
        let mut grid = ResponsiveGrid::new(GridConfig::default()).unwrap();
        grid.create_default_ide_layout().unwrap();
        
        let layout = grid.calculate_layout(1200.0, 800.0, "desktop").unwrap();
        
        // Should have calculated positions for all areas
        assert_eq!(layout.len(), 7);
        
        // Title bar should be at top
        let title_bar = layout.get(&LayoutArea::TitleBar).unwrap();
        assert_eq!(title_bar.position.y, 16.0); // Top padding
        
        // Status bar should be at bottom
        let status_bar = layout.get(&LayoutArea::StatusBar).unwrap();
        assert!(status_bar.position.y > title_bar.position.y);
    }
}