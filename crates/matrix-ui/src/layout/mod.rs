//! Layout Manager and Responsive System for MATRIX IDE
//!
//! This module implements a comprehensive layout management system with:
//! - Responsive grid system
//! - Splitter components with drag-and-drop resizing
//! - Layout persistence for user preferences
//! - Performance-optimized layout calculations

pub mod manager;
pub mod responsive;
pub mod splitter;
pub mod persistence;
pub mod grid;
pub mod calculations;

#[cfg(test)]
mod tests;

pub use manager::{LayoutManager, LayoutConfig, LayoutState};
pub use responsive::{ResponsiveSystem, ResponsiveBreakpoints, ResponsiveLayout};
pub use splitter::{Splitter, SplitterConfig, SplitterState, SplitterDirection, SplitterManager};
pub use persistence::{LayoutPersistence, LayoutPreferences, PersistenceConfig};
pub use grid::{ResponsiveGrid, GridConfig, GridItem, GridArea};
pub use calculations::{LayoutCalculator, LayoutMetrics, LayoutConstraints};

use crate::error::UiError;
use floem::reactive::RwSignal;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Layout system initialization
pub fn initialize_layout_system() -> Result<LayoutManager, UiError> {
    LayoutManager::new()
}

/// Layout area types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum LayoutArea {
    TitleBar,
    MenuBar,
    LeftSidebar,
    RightSidebar,
    MainEditor,
    BottomPanel,
    StatusBar,
    Modal,
}

/// Layout dimensions
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct LayoutDimensions {
    pub width: f64,
    pub height: f64,
    pub min_width: f64,
    pub min_height: f64,
    pub max_width: Option<f64>,
    pub max_height: Option<f64>,
}

impl Default for LayoutDimensions {
    fn default() -> Self {
        Self {
            width: 300.0,
            height: 200.0,
            min_width: 100.0,
            min_height: 50.0,
            max_width: None,
            max_height: None,
        }
    }
}

/// Layout position
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct LayoutPosition {
    pub x: f64,
    pub y: f64,
}

impl Default for LayoutPosition {
    fn default() -> Self {
        Self { x: 0.0, y: 0.0 }
    }
}

/// Layout rectangle
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct LayoutRect {
    pub position: LayoutPosition,
    pub dimensions: LayoutDimensions,
}

impl LayoutRect {
    pub fn new(x: f64, y: f64, width: f64, height: f64) -> Self {
        Self {
            position: LayoutPosition { x, y },
            dimensions: LayoutDimensions {
                width,
                height,
                min_width: 0.0,
                min_height: 0.0,
                max_width: None,
                max_height: None,
            },
        }
    }
    
    pub fn contains_point(&self, x: f64, y: f64) -> bool {
        x >= self.position.x
            && x <= self.position.x + self.dimensions.width
            && y >= self.position.y
            && y <= self.position.y + self.dimensions.height
    }
    
    pub fn intersects(&self, other: &LayoutRect) -> bool {
        !(self.position.x + self.dimensions.width < other.position.x
            || other.position.x + other.dimensions.width < self.position.x
            || self.position.y + self.dimensions.height < other.position.y
            || other.position.y + other.dimensions.height < self.position.y)
    }
}

/// Layout event types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutEvent {
    AreaResized {
        area: LayoutArea,
        old_dimensions: LayoutDimensions,
        new_dimensions: LayoutDimensions,
    },
    AreaMoved {
        area: LayoutArea,
        old_position: LayoutPosition,
        new_position: LayoutPosition,
    },
    SplitterDragged {
        splitter_id: Uuid,
        delta: f64,
    },
    LayoutChanged {
        layout_id: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    ResponsiveBreakpointChanged {
        old_breakpoint: String,
        new_breakpoint: String,
    },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_layout_rect_creation() {
        let rect = LayoutRect::new(10.0, 20.0, 100.0, 200.0);
        assert_eq!(rect.position.x, 10.0);
        assert_eq!(rect.position.y, 20.0);
        assert_eq!(rect.dimensions.width, 100.0);
        assert_eq!(rect.dimensions.height, 200.0);
    }

    #[test]
    fn test_layout_rect_contains_point() {
        let rect = LayoutRect::new(10.0, 20.0, 100.0, 200.0);
        assert!(rect.contains_point(50.0, 100.0));
        assert!(!rect.contains_point(5.0, 100.0));
        assert!(!rect.contains_point(50.0, 15.0));
    }

    #[test]
    fn test_layout_rect_intersects() {
        let rect1 = LayoutRect::new(10.0, 20.0, 100.0, 200.0);
        let rect2 = LayoutRect::new(50.0, 100.0, 100.0, 200.0);
        let rect3 = LayoutRect::new(200.0, 300.0, 100.0, 200.0);
        
        assert!(rect1.intersects(&rect2));
        assert!(!rect1.intersects(&rect3));
    }
}