//! Theme Integration System for Floem 0.2
//!
//! This module provides seamless integration between MATRIX IDE theme system
//! and Floem 0.2 styling APIs, enabling reactive theme updates across all components.

use crate::error::UiError;
use crate::theme::{Theme, ThemeManager};
use crate::core::reactive::ReactiveSystem;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use floem::style::{Style, Background, TextColor, BorderColor, Transition};
use floem::peniko::Color;
use floem::unit::DurationUnitExt;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use uuid::Uuid;
use serde::{Serialize, Deserialize};

/// Style provider for component styling
pub trait StyleProvider: Send + Sync {
    /// Get style for a component
    fn get_style(&self, component_type: &str, theme: &Theme) -> Style;
    
    /// Get style variants for different states
    fn get_style_variants(&self, component_type: &str, theme: &Theme) -> HashMap<String, Style>;
    
    /// Check if provider supports component type
    fn supports_component(&self, component_type: &str) -> bool;
}

/// Theme-aware style configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StyleConfig {
    pub component_type: String,
    pub base_style: StyleDefinition,
    pub variants: HashMap<String, StyleDefinition>,
    pub responsive_breakpoints: Vec<ResponsiveBreakpoint>,
}

/// Style definition that can be converted to Floem Style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StyleDefinition {
    pub background_color: Option<String>,
    pub text_color: Option<String>,
    pub border_color: Option<String>,
    pub border_width: Option<f32>,
    pub border_radius: Option<f32>,
    pub padding: Option<f32>,
    pub margin: Option<f32>,
    pub font_size: Option<f32>,
    pub width: Option<f32>,
    pub height: Option<f32>,
    pub opacity: Option<f32>,
}

/// Responsive breakpoint configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponsiveBreakpoint {
    pub min_width: f32,
    pub max_width: Option<f32>,
    pub style_overrides: StyleDefinition,
}

/// Default style provider implementation
pub struct DefaultStyleProvider {
    /// Style configurations for different components
    style_configs: HashMap<String, StyleConfig>,
}

impl DefaultStyleProvider {
    /// Create a new default style provider
    pub fn new() -> Self {
        let mut provider = Self {
            style_configs: HashMap::new(),
        };
        
        provider.setup_default_styles();
        provider
    }
    
    /// Setup default style configurations
    fn setup_default_styles(&mut self) {
        // Button styles
        self.style_configs.insert("button".to_string(), StyleConfig {
            component_type: "button".to_string(),
            base_style: StyleDefinition {
                background_color: Some("background_secondary".to_string()),
                text_color: Some("text".to_string()),
                border_color: Some("border".to_string()),
                border_width: Some(1.0),
                border_radius: Some(4.0),
                padding: Some(8.0),
                margin: None,
                font_size: Some(13.0),
                width: None,
                height: None,
                opacity: None,
            },
            variants: {
                let mut variants = HashMap::new();
                variants.insert("primary".to_string(), StyleDefinition {
                    background_color: Some("primary".to_string()),
                    text_color: Some("button_fg".to_string()),
                    border_color: Some("primary".to_string()),
                    border_width: Some(1.0),
                    border_radius: Some(4.0),
                    padding: Some(8.0),
                    margin: None,
                    font_size: Some(13.0),
                    width: None,
                    height: None,
                    opacity: None,
                });
                variants.insert("danger".to_string(), StyleDefinition {
                    background_color: Some("danger".to_string()),
                    text_color: Some("button_fg".to_string()),
                    border_color: Some("danger".to_string()),
                    border_width: Some(1.0),
                    border_radius: Some(4.0),
                    padding: Some(8.0),
                    margin: None,
                    font_size: Some(13.0),
                    width: None,
                    height: None,
                    opacity: None,
                });
                variants
            },
            responsive_breakpoints: Vec::new(),
        });
        
        // Panel styles
        self.style_configs.insert("panel".to_string(), StyleConfig {
            component_type: "panel".to_string(),
            base_style: StyleDefinition {
                background_color: Some("background_secondary".to_string()),
                text_color: Some("text".to_string()),
                border_color: Some("border".to_string()),
                border_width: Some(1.0),
                border_radius: Some(4.0),
                padding: Some(16.0),
                margin: None,
                font_size: Some(13.0),
                width: None,
                height: None,
                opacity: None,
            },
            variants: HashMap::new(),
            responsive_breakpoints: Vec::new(),
        });
        
        // Input styles
        self.style_configs.insert("input".to_string(), StyleConfig {
            component_type: "input".to_string(),
            base_style: StyleDefinition {
                background_color: Some("background".to_string()),
                text_color: Some("text".to_string()),
                border_color: Some("border".to_string()),
                border_width: Some(1.0),
                border_radius: Some(2.0),
                padding: Some(4.0),
                margin: None,
                font_size: Some(13.0),
                width: None,
                height: None,
                opacity: None,
            },
            variants: HashMap::new(),
            responsive_breakpoints: Vec::new(),
        });
    }
    
    /// Convert style definition to Floem style
    fn convert_style_definition(&self, definition: &StyleDefinition, theme: &Theme) -> Style {
        let mut style = Style::new();
        
        // Background color
        if let Some(bg_color) = &definition.background_color {
            if let Some(color) = self.resolve_theme_color(bg_color, theme) {
                style = style.background(color);
            }
        }
        
        // Text color
        if let Some(text_color) = &definition.text_color {
            if let Some(color) = self.resolve_theme_color(text_color, theme) {
                style = style.color(color);
            }
        }
        
        // Border
        if let Some(border_color) = &definition.border_color {
            if let Some(color) = self.resolve_theme_color(border_color, theme) {
                style = style.border_color(color);
            }
        }
        
        if let Some(border_width) = definition.border_width {
            style = style.border(border_width);
        }
        
        if let Some(border_radius) = definition.border_radius {
            style = style.border_radius(border_radius);
        }
        
        // Spacing
        if let Some(padding) = definition.padding {
            style = style.padding(padding);
        }
        
        if let Some(margin) = definition.margin {
            style = style.margin(margin);
        }
        
        // Typography
        if let Some(font_size) = definition.font_size {
            style = style.font_size(font_size);
        }
        
        // Dimensions
        if let Some(width) = definition.width {
            style = style.width(width);
        }
        
        if let Some(height) = definition.height {
            style = style.height(height);
        }
        
        // Add transitions for smooth theme changes
        let transition_duration = 200.millis();
        style = style
            .transition(Background, Transition::ease_in_out(transition_duration))
            .transition(TextColor, Transition::ease_in_out(transition_duration))
            .transition(BorderColor, Transition::ease_in_out(transition_duration));
        
        style
    }
    
    /// Resolve theme color by name
    fn resolve_theme_color(&self, color_name: &str, theme: &Theme) -> Option<Color> {
        match color_name {
            "background" => Some(theme.colors.background),
            "background_secondary" => Some(theme.colors.background_secondary),
            "background_tertiary" => Some(theme.colors.background_tertiary),
            "text" => Some(theme.colors.text),
            "text_secondary" => Some(theme.colors.text_secondary),
            "text_tertiary" => Some(theme.colors.text_tertiary),
            "accent" => Some(theme.colors.accent),
            "accent_secondary" => Some(theme.colors.accent_secondary),
            "primary" => Some(theme.colors.primary),
            "surface" => Some(theme.colors.surface),
            "border" => Some(theme.colors.border),
            "hover" => Some(theme.colors.hover),
            "active" => Some(theme.colors.active),
            "success" => Some(theme.colors.success),
            "warning" => Some(theme.colors.warning),
            "error" => Some(theme.colors.error),
            "info" => Some(theme.colors.info),
            "danger" => Some(theme.colors.danger),
            "danger_active" => Some(theme.colors.danger_active),
            "button_bg" => Some(theme.colors.button_bg),
            "button_fg" => Some(theme.colors.button_fg),
            "hover_bg" => Some(theme.colors.hover_bg),
            "title_bar_bg" => Some(theme.colors.title_bar_bg),
            "transparent" => Some(theme.colors.transparent),
            _ => None,
        }
    }
}

impl StyleProvider for DefaultStyleProvider {
    fn get_style(&self, component_type: &str, theme: &Theme) -> Style {
        if let Some(config) = self.style_configs.get(component_type) {
            self.convert_style_definition(&config.base_style, theme)
        } else {
            // Return default style
            Style::new()
        }
    }
    
    fn get_style_variants(&self, component_type: &str, theme: &Theme) -> HashMap<String, Style> {
        let mut variants = HashMap::new();
        
        if let Some(config) = self.style_configs.get(component_type) {
            for (variant_name, variant_def) in &config.variants {
                let style = self.convert_style_definition(variant_def, theme);
                variants.insert(variant_name.clone(), style);
            }
        }
        
        variants
    }
    
    fn supports_component(&self, component_type: &str) -> bool {
        self.style_configs.contains_key(component_type)
    }
}

/// Theme integration system
pub struct ThemeIntegration {
    /// Current theme signal
    current_theme: RwSignal<Theme>,
    
    /// Style providers registry
    style_providers: Arc<RwLock<HashMap<String, Arc<dyn StyleProvider>>>>,
    
    /// Cached styles for performance
    style_cache: Arc<RwLock<HashMap<String, Style>>>,
    
    /// Theme manager reference
    theme_manager: Option<Arc<ThemeManager>>,
    
    /// Integration ID
    id: Uuid,
}

impl ThemeIntegration {
    /// Create a new theme integration system
    pub fn new() -> Result<Self, UiError> {
        let default_theme = Theme::default();
        
        Ok(Self {
            current_theme: create_rw_signal(default_theme),
            style_providers: Arc::new(RwLock::new(HashMap::new())),
            style_cache: Arc::new(RwLock::new(HashMap::new())),
            theme_manager: None,
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the theme integration system
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Register default style provider
        self.register_style_provider("default", Arc::new(DefaultStyleProvider::new()))?;
        
        Ok(())
    }
    
    /// Set theme manager
    pub fn set_theme_manager(&mut self, theme_manager: Arc<ThemeManager>) -> Result<(), UiError> {
        self.theme_manager = Some(theme_manager);
        
        // Update current theme from theme manager
        if let Some(ref manager) = self.theme_manager {
            let theme = manager.get_active_theme()?;
            self.update_theme(theme)?;
        }
        
        Ok(())
    }
    
    /// Update current theme
    pub fn update_theme(&self, theme: Theme) -> Result<(), UiError> {
        self.current_theme.update(|current| *current = theme);
        
        // Clear style cache to force regeneration
        {
            let mut cache = self.style_cache.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on style cache".to_string())
            })?;
            cache.clear();
        }
        
        Ok(())
    }
    
    /// Get current theme
    pub fn get_current_theme(&self) -> Theme {
        self.current_theme.get()
    }
    
    /// Register a style provider
    pub fn register_style_provider(&self, name: &str, provider: Arc<dyn StyleProvider>) -> Result<(), UiError> {
        let mut providers = self.style_providers.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on style providers".to_string())
        })?;
        
        providers.insert(name.to_string(), provider);
        
        Ok(())
    }
    
    /// Get style for component
    pub fn get_component_style(&self, component_type: &str) -> Result<Style, UiError> {
        let cache_key = format!("{}:base", component_type);
        
        // Check cache first
        {
            let cache = self.style_cache.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on style cache".to_string())
            })?;
            
            if let Some(cached_style) = cache.get(&cache_key) {
                return Ok(cached_style.clone());
            }
        }
        
        // Generate style
        let theme = self.current_theme.get();
        let style = self.generate_component_style(component_type, &theme)?;
        
        // Cache the style
        {
            let mut cache = self.style_cache.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on style cache".to_string())
            })?;
            cache.insert(cache_key, style.clone());
        }
        
        Ok(style)
    }
    
    /// Get style variant for component
    pub fn get_component_style_variant(&self, component_type: &str, variant: &str) -> Result<Style, UiError> {
        let cache_key = format!("{}:{}", component_type, variant);
        
        // Check cache first
        {
            let cache = self.style_cache.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on style cache".to_string())
            })?;
            
            if let Some(cached_style) = cache.get(&cache_key) {
                return Ok(cached_style.clone());
            }
        }
        
        // Generate style variant
        let theme = self.current_theme.get();
        let style = self.generate_component_style_variant(component_type, variant, &theme)?;
        
        // Cache the style
        {
            let mut cache = self.style_cache.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on style cache".to_string())
            })?;
            cache.insert(cache_key, style.clone());
        }
        
        Ok(style)
    }
    
    /// Generate component style using providers
    fn generate_component_style(&self, component_type: &str, theme: &Theme) -> Result<Style, UiError> {
        let providers = self.style_providers.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on style providers".to_string())
        })?;
        
        // Try to find a provider that supports this component type
        for provider in providers.values() {
            if provider.supports_component(component_type) {
                return Ok(provider.get_style(component_type, theme));
            }
        }
        
        // Return default style if no provider found
        Ok(Style::new())
    }
    
    /// Generate component style variant using providers
    fn generate_component_style_variant(&self, component_type: &str, variant: &str, theme: &Theme) -> Result<Style, UiError> {
        let providers = self.style_providers.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on style providers".to_string())
        })?;
        
        // Try to find a provider that supports this component type
        for provider in providers.values() {
            if provider.supports_component(component_type) {
                let variants = provider.get_style_variants(component_type, theme);
                if let Some(style) = variants.get(variant) {
                    return Ok(style.clone());
                }
            }
        }
        
        // Fallback to base style
        self.generate_component_style(component_type, theme)
    }
    
    /// Connect to reactive system for theme updates
    pub async fn connect_to_reactive_system(&self, reactive_system: Arc<ReactiveSystem>) -> Result<(), UiError> {
        // Create a signal for theme updates in the reactive system
        reactive_system.create_signal("theme.current", serde_json::to_value(&self.current_theme.get())?)?;
        
        // Setup theme change propagation
        let theme_signal = self.current_theme;
        let reactive_system_clone = reactive_system.clone();
        
        // Note: In a real implementation, we'd set up a proper reactive connection
        // For now, this is a placeholder for the connection logic
        
        Ok(())
    }
    
    /// Get integration ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Shutdown the theme integration system
    pub async fn shutdown(&self) -> Result<(), UiError> {
        // Clear all providers and cache
        {
            let mut providers = self.style_providers.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on style providers".to_string())
            })?;
            providers.clear();
        }
        
        {
            let mut cache = self.style_cache.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on style cache".to_string())
            })?;
            cache.clear();
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_style_provider() {
        let provider = DefaultStyleProvider::new();
        let theme = Theme::default();
        
        assert!(provider.supports_component("button"));
        assert!(provider.supports_component("panel"));
        assert!(provider.supports_component("input"));
        assert!(!provider.supports_component("unknown"));
        
        let button_style = provider.get_style("button", &theme);
        // Style should be created without errors
        
        let variants = provider.get_style_variants("button", &theme);
        assert!(variants.contains_key("primary"));
        assert!(variants.contains_key("danger"));
    }
    
    #[tokio::test]
    async fn test_theme_integration() {
        let integration = ThemeIntegration::new().unwrap();
        integration.initialize().await.unwrap();
        
        let button_style = integration.get_component_style("button").unwrap();
        let primary_style = integration.get_component_style_variant("button", "primary").unwrap();
        
        // Styles should be generated without errors
        
        // Test theme update
        let new_theme = Theme::professional_light();
        integration.update_theme(new_theme.clone()).unwrap();
        
        let updated_theme = integration.get_current_theme();
        assert_eq!(updated_theme.name, new_theme.name);
    }
}