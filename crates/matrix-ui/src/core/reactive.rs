//! Reactive System for UI State Management
//!
//! This module implements a reactive state management system using Floem 0.2
//! reactive primitives for efficient UI updates and state synchronization.

use crate::error::UiError;
use floem::reactive::{RwSignal, Memo, create_rw_signal, create_memo, SignalGet, SignalUpdate};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Reactive state container
pub trait ReactiveState: Send + Sync {
    /// Get state as JSON value
    fn as_json(&self) -> serde_json::Value;
    
    /// Update state from JSON value
    fn from_json(&mut self, value: serde_json::Value) -> Result<(), UiError>;
    
    /// Get state type name
    fn type_name(&self) -> &'static str;
    
    /// Get state ID
    fn state_id(&self) -> Uuid;
}

/// State change notification
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StateChange {
    pub state_id: Uuid,
    pub state_type: String,
    pub old_value: serde_json::Value,
    pub new_value: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// State manager for reactive state coordination
pub struct StateManager {
    /// Registered reactive states
    states: Arc<RwLock<HashMap<Uuid, Box<dyn ReactiveState>>>>,
    
    /// State change history
    change_history: RwSignal<Vec<StateChange>>,
    
    /// State dependencies (computed states)
    dependencies: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    
    /// Manager ID
    id: Uuid,
}

impl StateManager {
    /// Create a new state manager
    pub fn new() -> Self {
        Self {
            states: Arc::new(RwLock::new(HashMap::new())),
            change_history: create_rw_signal(Vec::new()),
            dependencies: Arc::new(RwLock::new(HashMap::new())),
            id: Uuid::new_v4(),
        }
    }
    
    /// Register a reactive state
    pub fn register_state<S>(&self, state: S) -> Result<Uuid, UiError>
    where
        S: ReactiveState + 'static,
    {
        let state_id = state.state_id();
        
        let mut states = self.states.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on states".to_string())
        })?;
        
        states.insert(state_id, Box::new(state));
        
        Ok(state_id)
    }
    
    /// Unregister a reactive state
    pub fn unregister_state(&self, state_id: Uuid) -> Result<(), UiError> {
        let mut states = self.states.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on states".to_string())
        })?;
        
        states.remove(&state_id);
        
        // Remove dependencies
        let mut dependencies = self.dependencies.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on dependencies".to_string())
        })?;
        
        dependencies.remove(&state_id);
        
        Ok(())
    }
    
    /// Update a state
    pub fn update_state(&self, state_id: Uuid, new_value: serde_json::Value) -> Result<(), UiError> {
        let old_value = {
            let mut states = self.states.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on states".to_string())
            })?;
            
            if let Some(state) = states.get_mut(&state_id) {
                let old_value = state.as_json();
                state.from_json(new_value.clone())?;
                old_value
            } else {
                return Err(UiError::StateError(format!("State {} not found", state_id)));
            }
        };
        
        // Record state change
        let change = StateChange {
            state_id,
            state_type: self.get_state_type(state_id)?,
            old_value,
            new_value,
            timestamp: chrono::Utc::now(),
        };
        
        self.change_history.update(|history| {
            history.push(change);
            // Keep only last 1000 changes
            if history.len() > 1000 {
                history.remove(0);
            }
        });
        
        Ok(())
    }
    
    /// Get state value
    pub fn get_state(&self, state_id: Uuid) -> Result<serde_json::Value, UiError> {
        let states = self.states.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on states".to_string())
        })?;
        
        if let Some(state) = states.get(&state_id) {
            Ok(state.as_json())
        } else {
            Err(UiError::StateError(format!("State {} not found", state_id)))
        }
    }
    
    /// Get state type
    fn get_state_type(&self, state_id: Uuid) -> Result<String, UiError> {
        let states = self.states.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on states".to_string())
        })?;
        
        if let Some(state) = states.get(&state_id) {
            Ok(state.type_name().to_string())
        } else {
            Err(UiError::StateError(format!("State {} not found", state_id)))
        }
    }
    
    /// Add state dependency
    pub fn add_dependency(&self, dependent_id: Uuid, dependency_id: Uuid) -> Result<(), UiError> {
        let mut dependencies = self.dependencies.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on dependencies".to_string())
        })?;
        
        dependencies.entry(dependent_id).or_insert_with(Vec::new).push(dependency_id);
        
        Ok(())
    }
    
    /// Get change history
    pub fn get_change_history(&self) -> Vec<StateChange> {
        self.change_history.get()
    }
    
    /// Clear change history
    pub fn clear_history(&self) {
        self.change_history.update(|history| history.clear());
    }
    
    /// Get manager ID
    pub fn id(&self) -> Uuid {
        self.id
    }
}

/// Reactive system for UI state management
pub struct ReactiveSystem {
    /// State manager
    state_manager: Arc<StateManager>,
    
    /// Reactive signals registry
    signals: Arc<RwLock<HashMap<String, RwSignal<serde_json::Value>>>>,
    
    /// Computed values (memos) registry
    memos: Arc<RwLock<HashMap<String, Memo<serde_json::Value>>>>,
    
    /// System ID
    id: Uuid,
}

impl ReactiveSystem {
    /// Create a new reactive system
    pub fn new() -> Result<Self, UiError> {
        Ok(Self {
            state_manager: Arc::new(StateManager::new()),
            signals: Arc::new(RwLock::new(HashMap::new())),
            memos: Arc::new(RwLock::new(HashMap::new())),
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the reactive system
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Setup default reactive signals
        self.setup_default_signals().await?;
        
        Ok(())
    }
    
    /// Setup default reactive signals
    async fn setup_default_signals(&self) -> Result<(), UiError> {
        // Create default UI state signals
        self.create_signal("ui.theme", serde_json::json!("dark"))?;
        self.create_signal("ui.layout", serde_json::json!({"type": "default"}))?;
        self.create_signal("ui.focus", serde_json::json!(null))?;
        self.create_signal("ui.loading", serde_json::json!(false))?;
        
        Ok(())
    }
    
    /// Create a reactive signal
    pub fn create_signal(&self, key: &str, initial_value: serde_json::Value) -> Result<(), UiError> {
        let signal = create_rw_signal(initial_value);
        
        let mut signals = self.signals.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on signals".to_string())
        })?;
        
        signals.insert(key.to_string(), signal);
        
        Ok(())
    }
    
    /// Get a reactive signal
    pub fn get_signal(&self, key: &str) -> Result<RwSignal<serde_json::Value>, UiError> {
        let signals = self.signals.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on signals".to_string())
        })?;
        
        signals.get(key).copied().ok_or_else(|| {
            UiError::StateError(format!("Signal '{}' not found", key))
        })
    }
    
    /// Update a reactive signal
    pub fn update_signal(&self, key: &str, value: serde_json::Value) -> Result<(), UiError> {
        let signal = self.get_signal(key)?;
        signal.update(|current| *current = value);
        Ok(())
    }
    
    /// Get signal value
    pub fn get_signal_value(&self, key: &str) -> Result<serde_json::Value, UiError> {
        let signal = self.get_signal(key)?;
        Ok(signal.get())
    }
    
    /// Create a computed value (memo)
    pub fn create_memo<F>(&self, key: &str, compute_fn: F) -> Result<(), UiError>
    where
        F: Fn(Option<&serde_json::Value>) -> serde_json::Value + 'static,
    {
        let memo = create_memo(compute_fn);
        
        let mut memos = self.memos.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on memos".to_string())
        })?;
        
        memos.insert(key.to_string(), memo);
        
        Ok(())
    }
    
    /// Get a computed value
    pub fn get_memo(&self, key: &str) -> Result<Memo<serde_json::Value>, UiError> {
        let memos = self.memos.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on memos".to_string())
        })?;
        
        memos.get(key).copied().ok_or_else(|| {
            UiError::StateError(format!("Memo '{}' not found", key))
        })
    }
    
    /// Get memo value
    pub fn get_memo_value(&self, key: &str) -> Result<serde_json::Value, UiError> {
        let memo = self.get_memo(key)?;
        Ok(memo.get())
    }
    
    /// Get state manager
    pub fn state_manager(&self) -> Arc<StateManager> {
        self.state_manager.clone()
    }
    
    /// Get system ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Shutdown the reactive system
    pub async fn shutdown(&self) -> Result<(), UiError> {
        // Clear all signals and memos
        {
            let mut signals = self.signals.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on signals".to_string())
            })?;
            signals.clear();
        }
        
        {
            let mut memos = self.memos.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on memos".to_string())
            })?;
            memos.clear();
        }
        
        Ok(())
    }
}

/// Example reactive state implementation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UIComponentState {
    pub id: Uuid,
    pub visible: bool,
    pub enabled: bool,
    pub position: (f32, f32),
    pub size: (f32, f32),
    pub properties: HashMap<String, serde_json::Value>,
}

impl UIComponentState {
    pub fn new() -> Self {
        Self {
            id: Uuid::new_v4(),
            visible: true,
            enabled: true,
            position: (0.0, 0.0),
            size: (100.0, 100.0),
            properties: HashMap::new(),
        }
    }
}

impl ReactiveState for UIComponentState {
    fn as_json(&self) -> serde_json::Value {
        serde_json::to_value(self).unwrap_or(serde_json::Value::Null)
    }
    
    fn from_json(&mut self, value: serde_json::Value) -> Result<(), UiError> {
        *self = serde_json::from_value(value).map_err(|e| {
            UiError::StateError(format!("Failed to deserialize state: {}", e))
        })?;
        Ok(())
    }
    
    fn type_name(&self) -> &'static str {
        "UIComponentState"
    }
    
    fn state_id(&self) -> Uuid {
        self.id
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_reactive_system_creation() {
        let system = ReactiveSystem::new().unwrap();
        assert_ne!(system.id(), Uuid::nil());
    }
    
    #[tokio::test]
    async fn test_signal_operations() {
        let system = ReactiveSystem::new().unwrap();
        system.initialize().await.unwrap();
        
        // Test signal creation and retrieval
        system.create_signal("test", serde_json::json!("initial")).unwrap();
        let value = system.get_signal_value("test").unwrap();
        assert_eq!(value, serde_json::json!("initial"));
        
        // Test signal update
        system.update_signal("test", serde_json::json!("updated")).unwrap();
        let updated_value = system.get_signal_value("test").unwrap();
        assert_eq!(updated_value, serde_json::json!("updated"));
    }
    
    #[test]
    fn test_state_manager() {
        let manager = StateManager::new();
        let state = UIComponentState::new();
        let state_id = state.state_id();
        
        // Register state
        manager.register_state(state).unwrap();
        
        // Get state value
        let value = manager.get_state(state_id).unwrap();
        assert!(value.is_object());
        
        // Update state
        let new_value = serde_json::json!({
            "id": state_id,
            "visible": false,
            "enabled": true,
            "position": [10.0, 20.0],
            "size": [200.0, 300.0],
            "properties": {}
        });
        
        manager.update_state(state_id, new_value).unwrap();
        
        // Verify update
        let updated_value = manager.get_state(state_id).unwrap();
        assert_eq!(updated_value["visible"], serde_json::json!(false));
    }
}