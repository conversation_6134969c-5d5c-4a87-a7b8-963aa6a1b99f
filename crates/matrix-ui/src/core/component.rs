//! Base Component System for Modular UI Architecture
//!
//! This module provides base component traits and interfaces for building
//! modular UI components with consistent behavior and communication.

use crate::error::UiError;
use crate::core::event_bus::{UIEventBus, UIEvent, EventHandler};
use crate::core::reactive::{ReactiveState, UIComponentState};
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use floem::View;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use uuid::Uuid;
use serde::{Serialize, Deserialize};

/// Component lifecycle states
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ComponentLifecycle {
    Created,
    Initializing,
    Ready,
    Active,
    Inactive,
    Destroying,
    Destroyed,
}

/// Component metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentMetadata {
    pub id: Uuid,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub category: String,
    pub tags: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl ComponentMetadata {
    pub fn new(name: &str) -> Self {
        Self {
            id: Uuid::new_v4(),
            name: name.to_string(),
            version: "1.0.0".to_string(),
            description: String::new(),
            author: String::new(),
            category: "general".to_string(),
            tags: Vec::new(),
            created_at: chrono::Utc::now(),
        }
    }
}

/// Base component trait for all UI components
pub trait ComponentTrait: Send + Sync {
    /// Get component metadata
    fn metadata(&self) -> &ComponentMetadata;
    
    /// Get component state
    fn state(&self) -> Box<dyn ReactiveState>;
    
    /// Initialize the component
    fn initialize(&mut self) -> Result<(), UiError>;
    
    /// Update component state
    fn update(&mut self, delta_time: f32) -> Result<(), UiError>;
    
    /// Render the component
    fn render(&self) -> Result<Box<dyn View>, UiError>;
    
    /// Handle component events
    fn handle_event(&mut self, event: &UIEvent) -> Result<(), UiError>;
    
    /// Get component lifecycle state
    fn lifecycle(&self) -> ComponentLifecycle;
    
    /// Set component lifecycle state
    fn set_lifecycle(&mut self, lifecycle: ComponentLifecycle);
    
    /// Cleanup component resources
    fn cleanup(&mut self) -> Result<(), UiError>;
    
    /// Get component dependencies
    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }
    
    /// Check if component is ready
    fn is_ready(&self) -> bool {
        matches!(self.lifecycle(), ComponentLifecycle::Ready | ComponentLifecycle::Active)
    }
    
    /// Check if component is active
    fn is_active(&self) -> bool {
        self.lifecycle() == ComponentLifecycle::Active
    }
}

/// Base component implementation
pub struct BaseComponent {
    /// Component metadata
    metadata: ComponentMetadata,
    
    /// Component state
    state: RwSignal<UIComponentState>,
    
    /// Component lifecycle
    lifecycle: RwSignal<ComponentLifecycle>,
    
    /// Component properties
    properties: RwSignal<HashMap<String, serde_json::Value>>,
    
    /// Event handler ID
    event_handler_id: Option<Uuid>,
}

impl BaseComponent {
    /// Create a new base component
    pub fn new(name: &str) -> Self {
        Self {
            metadata: ComponentMetadata::new(name),
            state: create_rw_signal(UIComponentState::new()),
            lifecycle: create_rw_signal(ComponentLifecycle::Created),
            properties: create_rw_signal(HashMap::new()),
            event_handler_id: None,
        }
    }
    
    /// Get component ID
    pub fn id(&self) -> Uuid {
        self.metadata.id
    }
    
    /// Get component name
    pub fn name(&self) -> &str {
        &self.metadata.name
    }
    
    /// Set component property
    pub fn set_property(&self, key: &str, value: serde_json::Value) {
        self.properties.update(|props| {
            props.insert(key.to_string(), value);
        });
    }
    
    /// Get component property
    pub fn get_property(&self, key: &str) -> Option<serde_json::Value> {
        self.properties.get().get(key).cloned()
    }
    
    /// Update component state
    pub fn update_state<F>(&self, updater: F)
    where
        F: FnOnce(&mut UIComponentState),
    {
        self.state.update(updater);
    }
    
    /// Get component state
    pub fn get_state(&self) -> UIComponentState {
        self.state.get()
    }
}

impl ComponentTrait for BaseComponent {
    fn metadata(&self) -> &ComponentMetadata {
        &self.metadata
    }
    
    fn state(&self) -> Box<dyn ReactiveState> {
        Box::new(self.state.get())
    }
    
    fn initialize(&mut self) -> Result<(), UiError> {
        self.lifecycle.update(|lifecycle| *lifecycle = ComponentLifecycle::Initializing);
        
        // Perform initialization logic here
        
        self.lifecycle.update(|lifecycle| *lifecycle = ComponentLifecycle::Ready);
        Ok(())
    }
    
    fn update(&mut self, _delta_time: f32) -> Result<(), UiError> {
        // Default update implementation
        Ok(())
    }
    
    fn render(&self) -> Result<Box<dyn View>, UiError> {
        // Default render implementation - should be overridden
        use floem::views::{container, label, Decorators};
        
        let component_name = self.metadata.name.clone();
        Ok(Box::new(
            container(
                label(move || format!("Component: {}", component_name))
                    .style(|s| s.font_size(14.0))
            )
            .style(|s| s.padding(10.0))
        ))
    }
    
    fn handle_event(&mut self, _event: &UIEvent) -> Result<(), UiError> {
        // Default event handling - should be overridden
        Ok(())
    }
    
    fn lifecycle(&self) -> ComponentLifecycle {
        self.lifecycle.get()
    }
    
    fn set_lifecycle(&mut self, lifecycle: ComponentLifecycle) {
        self.lifecycle.update(|current| *current = lifecycle);
    }
    
    fn cleanup(&mut self) -> Result<(), UiError> {
        self.lifecycle.update(|lifecycle| *lifecycle = ComponentLifecycle::Destroying);
        
        // Perform cleanup logic here
        
        self.lifecycle.update(|lifecycle| *lifecycle = ComponentLifecycle::Destroyed);
        Ok(())
    }
}

/// Component manager for handling component lifecycle and communication
pub struct ComponentManager {
    /// Registered components
    components: Arc<RwLock<HashMap<Uuid, Box<dyn ComponentTrait>>>>,
    
    /// Component dependencies
    dependencies: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    
    /// Event bus for component communication
    event_bus: Arc<UIEventBus>,
    
    /// Manager ID
    id: Uuid,
}

impl ComponentManager {
    /// Create a new component manager
    pub fn new(event_bus: Arc<UIEventBus>) -> Result<Self, UiError> {
        Ok(Self {
            components: Arc::new(RwLock::new(HashMap::new())),
            dependencies: Arc::new(RwLock::new(HashMap::new())),
            event_bus,
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the component manager
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Setup component event handling
        self.setup_component_events().await?;
        
        Ok(())
    }
    
    /// Setup component event handling
    async fn setup_component_events(&self) -> Result<(), UiError> {
        // Create event handler for component lifecycle events
        let components = self.components.clone();
        
        struct ComponentEventHandler {
            id: Uuid,
            components: Arc<RwLock<HashMap<Uuid, Box<dyn ComponentTrait>>>>,
        }
        
        impl EventHandler for ComponentEventHandler {
            fn handle_event(&self, event: &UIEvent) -> Result<(), UiError> {
                let mut components = self.components.write().map_err(|_| {
                    UiError::LockError("Failed to acquire write lock on components".to_string())
                })?;
                
                // Forward event to all components
                for component in components.values_mut() {
                    component.handle_event(event)?;
                }
                
                Ok(())
            }
            
            fn handler_id(&self) -> Uuid {
                self.id
            }
            
            fn interested_events(&self) -> Vec<&'static str> {
                vec![
                    "theme_changed",
                    "layout_changed",
                    "focus_changed",
                    "user_interaction",
                    "component_state_changed",
                ]
            }
        }
        
        let handler = ComponentEventHandler {
            id: Uuid::new_v4(),
            components,
        };
        
        let interested_events = handler.interested_events();
        self.event_bus.subscribe(handler, interested_events)?;
        
        Ok(())
    }
    
    /// Register a component
    pub fn register_component<C>(&self, mut component: C) -> Result<Uuid, UiError>
    where
        C: ComponentTrait + 'static,
    {
        let component_id = component.metadata().id;
        
        // Initialize the component
        component.initialize()?;
        
        // Register component
        {
            let mut components = self.components.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on components".to_string())
            })?;
            
            components.insert(component_id, Box::new(component));
        }
        
        // Emit component registered event
        self.event_bus.emit(UIEvent::ComponentStateChanged {
            component_id: component_id.to_string(),
            state: serde_json::json!({"lifecycle": "registered"}),
            timestamp: chrono::Utc::now(),
        })?;
        
        Ok(component_id)
    }
    
    /// Unregister a component
    pub fn unregister_component(&self, component_id: Uuid) -> Result<(), UiError> {
        let mut component = {
            let mut components = self.components.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on components".to_string())
            })?;
            
            components.remove(&component_id).ok_or_else(|| {
                UiError::ComponentError(format!("Component {} not found", component_id))
            })?
        };
        
        // Cleanup component
        component.cleanup()?;
        
        // Remove dependencies
        {
            let mut dependencies = self.dependencies.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on dependencies".to_string())
            })?;
            
            dependencies.remove(&component_id);
        }
        
        // Emit component unregistered event
        self.event_bus.emit(UIEvent::ComponentStateChanged {
            component_id: component_id.to_string(),
            state: serde_json::json!({"lifecycle": "unregistered"}),
            timestamp: chrono::Utc::now(),
        })?;
        
        Ok(())
    }
    
    /// Get component by ID
    pub fn get_component(&self, component_id: Uuid) -> Result<Box<dyn ComponentTrait>, UiError> {
        let components = self.components.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on components".to_string())
        })?;
        
        // Note: This is a simplified version. In practice, we'd need to handle
        // the borrowing more carefully or return a reference/handle instead.
        Err(UiError::ComponentError("Direct component access not supported".to_string()))
    }
    
    /// Update all components
    pub fn update_components(&self, delta_time: f32) -> Result<(), UiError> {
        let mut components = self.components.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on components".to_string())
        })?;
        
        for component in components.values_mut() {
            if component.is_active() {
                component.update(delta_time)?;
            }
        }
        
        Ok(())
    }
    
    /// Get all component IDs
    pub fn get_component_ids(&self) -> Result<Vec<Uuid>, UiError> {
        let components = self.components.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on components".to_string())
        })?;
        
        Ok(components.keys().copied().collect())
    }
    
    /// Get component count
    pub fn component_count(&self) -> usize {
        self.components.read().unwrap().len()
    }
    
    /// Connect to event bus
    pub async fn connect_to_event_bus(&self, _event_bus: Arc<UIEventBus>) -> Result<(), UiError> {
        // Already connected during initialization
        Ok(())
    }
    
    /// Get manager ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Shutdown the component manager
    pub async fn shutdown(&self) -> Result<(), UiError> {
        // Unregister all components
        let component_ids: Vec<Uuid> = {
            let components = self.components.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on components".to_string())
            })?;
            
            components.keys().copied().collect()
        };
        
        for component_id in component_ids {
            self.unregister_component(component_id)?;
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::event_bus::UIEventBus;
    
    #[tokio::test]
    async fn test_base_component() {
        let mut component = BaseComponent::new("test_component");
        
        assert_eq!(component.name(), "test_component");
        assert_eq!(component.lifecycle(), ComponentLifecycle::Created);
        
        component.initialize().unwrap();
        assert_eq!(component.lifecycle(), ComponentLifecycle::Ready);
        
        component.set_property("test_prop", serde_json::json!("test_value"));
        assert_eq!(component.get_property("test_prop"), Some(serde_json::json!("test_value")));
    }
    
    #[tokio::test]
    async fn test_component_manager() {
        let event_bus = Arc::new(UIEventBus::new().unwrap());
        let manager = ComponentManager::new(event_bus).unwrap();
        manager.initialize().await.unwrap();
        
        let component = BaseComponent::new("test_component");
        let component_id = manager.register_component(component).unwrap();
        
        assert_eq!(manager.component_count(), 1);
        assert!(manager.get_component_ids().unwrap().contains(&component_id));
        
        manager.unregister_component(component_id).unwrap();
        assert_eq!(manager.component_count(), 0);
    }
}