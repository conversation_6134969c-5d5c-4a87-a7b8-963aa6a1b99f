//! Core UI Architecture for MATRIX IDE
//!
//! This module provides the foundational UI architecture with Floem 0.2 reactive system,
//! event bus, component communication, and theme integration.

pub mod event_bus;
pub mod reactive;
pub mod component;
pub mod theme_integration;

pub use event_bus::{UIEventBus, UIEvent, EventHandler};
pub use reactive::{ReactiveSystem, ReactiveState, StateManager};
pub use component::{BaseComponent, ComponentTrait, ComponentManager};
pub use theme_integration::{ThemeIntegration, StyleProvider};

use crate::error::UiError;
use std::sync::Arc;

/// Core UI Architecture manager
pub struct CoreUIArchitecture {
    /// Event bus for component communication
    event_bus: Arc<UIEventBus>,
    
    /// Reactive system for state management
    reactive_system: Arc<ReactiveSystem>,
    
    /// Component manager for modular components
    component_manager: Arc<ComponentManager>,
    
    /// Theme integration system
    theme_integration: Arc<ThemeIntegration>,
}

impl CoreUIArchitecture {
    /// Create a new core UI architecture instance
    pub fn new() -> Result<Self, UiError> {
        let event_bus = Arc::new(UIEventBus::new()?);
        let reactive_system = Arc::new(ReactiveSystem::new()?);
        let component_manager = Arc::new(ComponentManager::new(event_bus.clone())?);
        let theme_integration = Arc::new(ThemeIntegration::new()?);
        
        Ok(Self {
            event_bus,
            reactive_system,
            component_manager,
            theme_integration,
        })
    }
    
    /// Initialize the core UI architecture
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Initialize event bus
        self.event_bus.initialize().await?;
        
        // Initialize reactive system
        self.reactive_system.initialize().await?;
        
        // Initialize component manager
        self.component_manager.initialize().await?;
        
        // Initialize theme integration
        self.theme_integration.initialize().await?;
        
        // Setup cross-system communication
        self.setup_system_communication().await?;
        
        Ok(())
    }
    
    /// Setup communication between different systems
    async fn setup_system_communication(&self) -> Result<(), UiError> {
        // Connect theme changes to reactive system
        self.theme_integration.connect_to_reactive_system(
            self.reactive_system.clone()
        ).await?;
        
        // Connect component events to event bus
        self.component_manager.connect_to_event_bus(
            self.event_bus.clone()
        ).await?;
        
        Ok(())
    }
    
    /// Get event bus reference
    pub fn event_bus(&self) -> Arc<UIEventBus> {
        self.event_bus.clone()
    }
    
    /// Get reactive system reference
    pub fn reactive_system(&self) -> Arc<ReactiveSystem> {
        self.reactive_system.clone()
    }
    
    /// Get component manager reference
    pub fn component_manager(&self) -> Arc<ComponentManager> {
        self.component_manager.clone()
    }
    
    /// Get theme integration reference
    pub fn theme_integration(&self) -> Arc<ThemeIntegration> {
        self.theme_integration.clone()
    }
    
    /// Shutdown the core UI architecture
    pub async fn shutdown(&self) -> Result<(), UiError> {
        self.component_manager.shutdown().await?;
        self.theme_integration.shutdown().await?;
        self.reactive_system.shutdown().await?;
        self.event_bus.shutdown().await?;
        
        Ok(())
    }
}

impl Default for CoreUIArchitecture {
    fn default() -> Self {
        Self::new().expect("Failed to create default CoreUIArchitecture")
    }
}