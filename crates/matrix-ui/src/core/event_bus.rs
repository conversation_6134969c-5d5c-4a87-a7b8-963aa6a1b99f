//! Event Bus System for UI Component Communication
//!
//! This module implements a reactive event bus system for communication between
//! UI components using Floem 0.2 reactive primitives.

use crate::error::UiError;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use tokio::sync::broadcast;

/// UI Event types for component communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UIEvent {
    /// Theme changed event
    ThemeChanged {
        theme_name: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// Component state changed
    ComponentStateChanged {
        component_id: String,
        state: serde_json::Value,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// Layout changed event
    LayoutChanged {
        layout_id: String,
        changes: Vec<String>,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// Plugin event
    PluginEvent {
        plugin_id: String,
        event_type: String,
        data: serde_json::Value,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// Focus changed event
    FocusChanged {
        previous_component: Option<String>,
        current_component: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// User interaction event
    UserInteraction {
        component_id: String,
        interaction_type: String,
        data: serde_json::Value,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    
    /// System event
    SystemEvent {
        event_type: String,
        data: serde_json::Value,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}

impl UIEvent {
    /// Get event timestamp
    pub fn timestamp(&self) -> chrono::DateTime<chrono::Utc> {
        match self {
            UIEvent::ThemeChanged { timestamp, .. } => *timestamp,
            UIEvent::ComponentStateChanged { timestamp, .. } => *timestamp,
            UIEvent::LayoutChanged { timestamp, .. } => *timestamp,
            UIEvent::PluginEvent { timestamp, .. } => *timestamp,
            UIEvent::FocusChanged { timestamp, .. } => *timestamp,
            UIEvent::UserInteraction { timestamp, .. } => *timestamp,
            UIEvent::SystemEvent { timestamp, .. } => *timestamp,
        }
    }
    
    /// Get event type as string
    pub fn event_type(&self) -> &'static str {
        match self {
            UIEvent::ThemeChanged { .. } => "theme_changed",
            UIEvent::ComponentStateChanged { .. } => "component_state_changed",
            UIEvent::LayoutChanged { .. } => "layout_changed",
            UIEvent::PluginEvent { .. } => "plugin_event",
            UIEvent::FocusChanged { .. } => "focus_changed",
            UIEvent::UserInteraction { .. } => "user_interaction",
            UIEvent::SystemEvent { .. } => "system_event",
        }
    }
}

/// Event handler trait for components
pub trait EventHandler: Send + Sync {
    /// Handle an event
    fn handle_event(&self, event: &UIEvent) -> Result<(), UiError>;
    
    /// Get handler ID
    fn handler_id(&self) -> Uuid;
    
    /// Get event types this handler is interested in
    fn interested_events(&self) -> Vec<&'static str>;
}

/// Event subscription information
#[derive(Debug, Clone)]
pub struct EventSubscription {
    pub id: Uuid,
    pub handler_id: Uuid,
    pub event_types: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// UI Event Bus for component communication
pub struct UIEventBus {
    /// Event broadcast channel
    event_sender: broadcast::Sender<UIEvent>,
    
    /// Event handlers registry
    handlers: Arc<RwLock<HashMap<Uuid, Arc<dyn EventHandler>>>>,
    
    /// Event subscriptions
    subscriptions: Arc<RwLock<HashMap<Uuid, EventSubscription>>>,
    
    /// Event history (for debugging and replay)
    event_history: RwSignal<Vec<UIEvent>>,
    
    /// Event statistics
    event_stats: RwSignal<EventStatistics>,
    
    /// Bus ID
    id: Uuid,
}

/// Event statistics for monitoring
#[derive(Debug, Clone, Default)]
pub struct EventStatistics {
    pub total_events: u64,
    pub events_by_type: HashMap<String, u64>,
    pub active_handlers: u64,
    pub active_subscriptions: u64,
    pub last_event_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl UIEventBus {
    /// Create a new UI event bus
    pub fn new() -> Result<Self, UiError> {
        let (event_sender, _) = broadcast::channel(1000);
        
        Ok(Self {
            event_sender,
            handlers: Arc::new(RwLock::new(HashMap::new())),
            subscriptions: Arc::new(RwLock::new(HashMap::new())),
            event_history: create_rw_signal(Vec::new()),
            event_stats: create_rw_signal(EventStatistics::default()),
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the event bus
    pub async fn initialize(&self) -> Result<(), UiError> {
        // Start event processing task
        self.start_event_processor().await?;
        
        Ok(())
    }
    
    /// Start the event processor task
    async fn start_event_processor(&self) -> Result<(), UiError> {
        let mut receiver = self.event_sender.subscribe();
        let handlers = self.handlers.clone();
        let subscriptions = self.subscriptions.clone();
        let event_history = self.event_history;
        let event_stats = self.event_stats;
        
        tokio::spawn(async move {
            while let Ok(event) = receiver.recv().await {
                // Add to history
                event_history.update(|history| {
                    history.push(event.clone());
                    // Keep only last 1000 events
                    if history.len() > 1000 {
                        history.remove(0);
                    }
                });
                
                // Update statistics
                event_stats.update(|stats| {
                    stats.total_events += 1;
                    *stats.events_by_type.entry(event.event_type().to_string()).or_insert(0) += 1;
                    stats.last_event_time = Some(event.timestamp());
                });
                
                // Process event with handlers
                let handlers_guard = handlers.read().unwrap();
                let subscriptions_guard = subscriptions.read().unwrap();
                
                for subscription in subscriptions_guard.values() {
                    if subscription.event_types.contains(&event.event_type().to_string()) {
                        if let Some(handler) = handlers_guard.get(&subscription.handler_id) {
                            if let Err(e) = handler.handle_event(&event) {
                                eprintln!("Error handling event: {}", e);
                            }
                        }
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Emit an event
    pub fn emit(&self, event: UIEvent) -> Result<(), UiError> {
        self.event_sender.send(event).map_err(|e| {
            UiError::EventError(format!("Failed to emit event: {}", e))
        })?;
        
        Ok(())
    }
    
    /// Subscribe to events with a handler
    pub fn subscribe<H>(&self, handler: H, event_types: Vec<&'static str>) -> Result<Uuid, UiError>
    where
        H: EventHandler + 'static,
    {
        let handler_id = handler.handler_id();
        let subscription_id = Uuid::new_v4();
        
        // Register handler
        {
            let mut handlers = self.handlers.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on handlers".to_string())
            })?;
            handlers.insert(handler_id, Arc::new(handler));
        }
        
        // Create subscription
        let subscription = EventSubscription {
            id: subscription_id,
            handler_id,
            event_types: event_types.iter().map(|s| s.to_string()).collect(),
            created_at: chrono::Utc::now(),
        };
        
        {
            let mut subscriptions = self.subscriptions.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on subscriptions".to_string())
            })?;
            subscriptions.insert(subscription_id, subscription);
        }
        
        // Update statistics
        self.event_stats.update(|stats| {
            stats.active_handlers = self.handlers.read().unwrap().len() as u64;
            stats.active_subscriptions = self.subscriptions.read().unwrap().len() as u64;
        });
        
        Ok(subscription_id)
    }
    
    /// Unsubscribe from events
    pub fn unsubscribe(&self, subscription_id: Uuid) -> Result<(), UiError> {
        let handler_id = {
            let mut subscriptions = self.subscriptions.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on subscriptions".to_string())
            })?;
            
            if let Some(subscription) = subscriptions.remove(&subscription_id) {
                subscription.handler_id
            } else {
                return Err(UiError::EventError("Subscription not found".to_string()));
            }
        };
        
        // Check if handler has other subscriptions
        let has_other_subscriptions = {
            let subscriptions = self.subscriptions.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on subscriptions".to_string())
            })?;
            
            subscriptions.values().any(|s| s.handler_id == handler_id)
        };
        
        // Remove handler if no other subscriptions
        if !has_other_subscriptions {
            let mut handlers = self.handlers.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on handlers".to_string())
            })?;
            handlers.remove(&handler_id);
        }
        
        // Update statistics
        self.event_stats.update(|stats| {
            stats.active_handlers = self.handlers.read().unwrap().len() as u64;
            stats.active_subscriptions = self.subscriptions.read().unwrap().len() as u64;
        });
        
        Ok(())
    }
    
    /// Get event history
    pub fn get_event_history(&self) -> Vec<UIEvent> {
        self.event_history.get()
    }
    
    /// Get event statistics
    pub fn get_statistics(&self) -> EventStatistics {
        self.event_stats.get()
    }
    
    /// Clear event history
    pub fn clear_history(&self) {
        self.event_history.update(|history| history.clear());
    }
    
    /// Get bus ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Shutdown the event bus
    pub async fn shutdown(&self) -> Result<(), UiError> {
        // Clear all handlers and subscriptions
        {
            let mut handlers = self.handlers.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on handlers".to_string())
            })?;
            handlers.clear();
        }
        
        {
            let mut subscriptions = self.subscriptions.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on subscriptions".to_string())
            })?;
            subscriptions.clear();
        }
        
        // Clear history and stats
        self.event_history.update(|history| history.clear());
        self.event_stats.update(|stats| *stats = EventStatistics::default());
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    struct TestHandler {
        id: Uuid,
        events_received: Arc<RwLock<Vec<UIEvent>>>,
    }
    
    impl TestHandler {
        fn new() -> Self {
            Self {
                id: Uuid::new_v4(),
                events_received: Arc::new(RwLock::new(Vec::new())),
            }
        }
        
        fn get_received_events(&self) -> Vec<UIEvent> {
            self.events_received.read().unwrap().clone()
        }
    }
    
    impl EventHandler for TestHandler {
        fn handle_event(&self, event: &UIEvent) -> Result<(), UiError> {
            self.events_received.write().unwrap().push(event.clone());
            Ok(())
        }
        
        fn handler_id(&self) -> Uuid {
            self.id
        }
        
        fn interested_events(&self) -> Vec<&'static str> {
            vec!["theme_changed", "component_state_changed"]
        }
    }
    
    #[tokio::test]
    async fn test_event_bus_creation() {
        let event_bus = UIEventBus::new().unwrap();
        assert_ne!(event_bus.id(), Uuid::nil());
    }
    
    #[tokio::test]
    async fn test_event_subscription_and_emission() {
        let event_bus = UIEventBus::new().unwrap();
        event_bus.initialize().await.unwrap();
        
        let handler = TestHandler::new();
        let subscription_id = event_bus.subscribe(handler, vec!["theme_changed"]).unwrap();
        
        let event = UIEvent::ThemeChanged {
            theme_name: "dark".to_string(),
            timestamp: chrono::Utc::now(),
        };
        
        event_bus.emit(event.clone()).unwrap();
        
        // Give some time for event processing
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        
        // Verify statistics
        let stats = event_bus.get_statistics();
        assert_eq!(stats.total_events, 1);
        assert_eq!(stats.active_subscriptions, 1);
        
        // Cleanup
        event_bus.unsubscribe(subscription_id).unwrap();
    }
}