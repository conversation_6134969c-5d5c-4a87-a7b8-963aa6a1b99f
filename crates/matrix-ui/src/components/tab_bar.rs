//! Tab Bar Component for MATRIX IDE
//!
//! Componente per gestire i tab dell'editor con supporto per apertura, chiusura,
//! e switching tra file multipli.

use std::sync::Arc;

use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate},
    views::{
        container, h_stack, v_stack, label, button, scroll, empty, 
        Decorators, dyn_stack
    },
    style::{CursorStyle, AlignItems, JustifyContent, Position},
    View, IntoView,
};

use crate::{
    lapce_bridge_improved::{LapceIntegration, EditorTab, TabId},
    theme::{Theme, ThemeManager},
    error::UiError,
};

/// Tab Bar per gestire i tab dell'editor
pub struct TabBar {
    /// Integrazione Lapce
    lapce_integration: Arc<LapceIntegration>,
    
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
}

impl TabBar {
    /// Crea un nuovo TabBar
    pub fn new(
        lapce_integration: Arc<LapceIntegration>,
        theme_manager: Arc<ThemeManager>,
    ) -> Self {
        Self {
            lapce_integration,
            theme_manager,
        }
    }

    /// Crea la vista del tab bar
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let lapce_integration = self.lapce_integration.clone();
        
        container(
            h_stack((
                // Tab esistenti
                scroll(
                    dyn_stack(
                        {
                            let lapce = lapce_integration.clone();
                            move || lapce.get_tabs()
                        },
                        |tab| tab.id,
                        {
                            let lapce = lapce_integration.clone();
                            let theme_manager = self.theme_manager.clone();
                            move |tab: EditorTab| {
                                Self::create_tab_item(tab, lapce.clone(), theme_manager.clone())
                            }
                        }
                    )
                    .style(move |s| {
                        s.flex_direction(floem::style::FlexDirection::Row)
                         .align_items(AlignItems::Center)
                    })
                )
                .style(move |s| {
                    s.flex_grow(1.0)
                     .height(32.0)
                }),
                
                // Pulsante per nuovo tab
                button(
                    label(|| "+".to_string())
                        .style(move |s| {
                            s.font_size(16.0)
                             .color(theme.colors.text)
                             .font_weight(floem::text::Weight::BOLD)
                        })
                )
                .on_click_stop({
                    let lapce = lapce_integration.clone();
                    move |_| {
                        if let Err(e) = lapce.create_new_file() {
                            eprintln!("Errore nella creazione di nuovo file: {}", e);
                        }
                    }
                })
                .style(move |s| {
                    s.width(32.0)
                     .height(32.0)
                     .background(theme.colors.transparent)
                     .hover(|s| s.background(theme.colors.hover))
                     .cursor(CursorStyle::Pointer)
                     .border_radius(4.0)
                     .justify_center()
                     .align_items(AlignItems::Center)
                }),
            ))
            .style(move |s| {
                s.width_full()
                 .height(32.0)
                 .align_items(AlignItems::Center)
            })
        )
        .style(move |s| {
            s.width_full()
             .height(32.0)
             .background(theme.colors.background_secondary)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Crea un singolo elemento tab
    fn create_tab_item(
        tab: EditorTab,
        lapce_integration: Arc<LapceIntegration>,
        theme_manager: Arc<ThemeManager>,
    ) -> impl View {
        let theme = theme_manager.get_active_theme().unwrap_or_default();
        let tab_id = tab.id;
        let is_active = tab.is_active;
        let title = tab.title.clone();
        let is_dirty = tab.is_dirty;
        
        container(
            h_stack((
                // Titolo del tab
                label(move || title.clone())
                    .style(move |s| {
                        s.font_size(12.0)
                         .color(if is_active { 
                             theme.colors.text 
                         } else { 
                             theme.colors.text_secondary 
                         })
                         .margin_right(8.0)
                    }),
                
                // Indicatore modificato
                if is_dirty {
                    label(|| "●".to_string())
                        .style(move |s| {
                            s.font_size(8.0)
                             .color(theme.colors.warning)
                             .margin_right(4.0)
                        })
                        .into_any()
                } else {
                    empty().into_any()
                },
                
                // Pulsante chiudi
                button(
                    label(|| "×".to_string())
                        .style(move |s| {
                            s.font_size(14.0)
                             .color(theme.colors.text_secondary)
                        })
                )
                .on_click_stop({
                    let lapce = lapce_integration.clone();
                    move |_| {
                        if let Err(e) = lapce.close_tab(tab_id) {
                            eprintln!("Errore nella chiusura del tab: {}", e);
                        }
                    }
                })
                .style(move |s| {
                    s.width(16.0)
                     .height(16.0)
                     .background(theme.colors.transparent)
                     .hover(|s| s.background(theme.colors.error.multiply_alpha(0.2)))
                     .cursor(CursorStyle::Pointer)
                     .border_radius(2.0)
                     .justify_center()
                     .align_items(AlignItems::Center)
                }),
            ))
            .style(move |s| {
                s.align_items(AlignItems::Center)
                 .padding_horiz(12.0)
                 .padding_vert(6.0)
            })
        )
        .on_click_stop({
            let lapce = lapce_integration.clone();
            move |_| {
                if let Err(e) = lapce.activate_tab(tab_id) {
                    eprintln!("Errore nell'attivazione del tab: {}", e);
                }
            }
        })
        .style(move |s| {
            s.height(32.0)
             .min_width(100.0)
             .max_width(200.0)
             .background(if is_active {
                 theme.colors.background
             } else {
                 theme.colors.transparent
             })
             .hover(|s| {
                 if !is_active {
                     s.background(theme.colors.hover.multiply_alpha(0.5))
                 } else {
                     s
                 }
             })
             .cursor(CursorStyle::Pointer)
             .border_radius(4.0)
             .border_top(if is_active { 2.0 } else { 0.0 })
             .border_color(if is_active { 
                 theme.colors.accent 
             } else { 
                 theme.colors.transparent 
             })
        })
    }
}
