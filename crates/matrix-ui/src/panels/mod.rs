//! Moduli per i pannelli dell'interfaccia utente
//!
//! Questo modulo esporta i vari pannelli specializzati dell'interfaccia utente.

pub mod manager;
pub mod panel_manager_v2;
pub mod dock_container;
pub mod tab_system;
pub mod tab_system_tests;
pub mod tab_system_simple_test;
pub mod docking_preview;
pub mod split_view;
pub mod space_redistribution;
pub mod docking_integration_tests;
pub mod god_mode_panel;
pub mod terminal_panel;
pub mod properties_panel;
pub mod output_panel;
pub mod problems_panel;
pub mod ai_panel;
pub mod tests;
pub mod simple_test;

// Re-export dei tipi principali (legacy)
pub use manager::{PanelManager, Panel, PanelConfig, PanelPosition, PanelState, PanelLayout, SplitDirection, PanelType};

// Re-export dei nuovi tipi (v2)
pub use panel_manager_v2::{PanelManagerV2, PanelContent, PanelPreferences};
pub use dock_container::{DockContainer, DockArea, DockAreaId, TabGroupId, TabId, DockContainerState, DockContainerConfig};
pub use tab_system::{TabSystem, TabState, TabGroupConfig, TabEvent, DragState, DropTarget};

// Re-export docking and split view components
pub use docking_preview::{DockingPreview, DockingPreviewConfig, DockZone, DockingResult, PreviewBounds};
pub use split_view::{SplitView, SplitViewManager, SplitDirection as SplitViewDirection, SplitPaneContent, SplitViewConfig};
pub use space_redistribution::{SpaceRedistribution, RedistributionStrategy, PanelSpaceInfo, RedistributionEvent};

// Re-export dei pannelli specifici
pub use terminal_panel::TerminalPanel;
pub use properties_panel::{PropertiesPanel, Property, PropertyValue, PropertyType};
pub use output_panel::{OutputPanel, OutputMessage, OutputType};
pub use problems_panel::{ProblemsPanel, Problem, ProblemSeverity, ProblemCategory, RelatedInformation};
pub use ai_panel::{AiPanel, AiMessage, MessageType, MessageStatus, ToolCall};
