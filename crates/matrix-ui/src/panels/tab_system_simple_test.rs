//! Simple tests for the Tab System to verify basic functionality

#[cfg(test)]
mod tests {
    use super::super::tab_system::{TabSystem, TabState, TabGroupConfig};
    use super::super::dock_container::DockArea;

    fn create_test_tab_state(id: &str, title: &str) -> TabState {
        TabState {
            id: id.to_string(),
            title: title.to_string(),
            content_type: "text".to_string(),
            has_changes: false,
            has_errors: false,
            is_active: false,
            is_closable: true,
            icon: None,
            tooltip: None,
        }
    }

    fn create_test_group_config(dock_area: DockArea) -> TabGroupConfig {
        TabGroupConfig {
            dock_area,
            ..Default::default()
        }
    }

    #[test]
    fn test_tab_system_creation() {
        let tab_system = TabSystem::new();
        assert!(tab_system.get_drag_state().is_none());
        assert!(tab_system.get_drop_target().is_none());
    }

    #[test]
    fn test_tab_group_creation() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        
        let group_id = tab_system.create_tab_group(config.clone()).unwrap();
        assert!(!group_id.is_empty());
        
        // Verify group exists
        let tabs = tab_system.get_group_tabs(&group_id);
        assert!(tabs.is_empty()); // Should be empty initially
    }

    #[test]
    fn test_tab_creation() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create a tab
        let tab_state = create_test_tab_state("tab1", "Test Tab 1");
        tab_system.create_tab(&group_id, tab_state.clone()).unwrap();
        
        // Verify tab exists
        let retrieved_tab = tab_system.get_tab("tab1").unwrap();
        assert_eq!(retrieved_tab.id, "tab1");
        assert_eq!(retrieved_tab.title, "Test Tab 1");
        
        // Verify tab is in group
        let group_tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(group_tabs.len(), 1);
        assert_eq!(group_tabs[0].id, "tab1");
    }

    #[test]
    fn test_drag_operations() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("drag_tab", "Draggable Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Test drag capability check
        assert!(tab_system.can_drag_tab("drag_tab"));
        
        // Start drag
        tab_system.start_drag("drag_tab", (100.0, 50.0), (10.0, 5.0)).unwrap();
        
        // Verify drag state
        let drag_state = tab_system.get_drag_state().unwrap();
        assert_eq!(drag_state.dragged_tab, "drag_tab");
        assert_eq!(drag_state.drag_position, (100.0, 50.0));
        assert!(drag_state.is_dragging);
        
        // Cancel drag
        tab_system.cancel_drag().unwrap();
        
        // Verify drag state is cleared
        assert!(tab_system.get_drag_state().is_none());
        assert!(tab_system.get_drop_target().is_none());
    }

    #[test]
    fn test_visual_indicators() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("visual_tab", "Visual Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Initially no changes or errors
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(!tab.has_changes);
        assert!(!tab.has_errors);
        
        // Set has changes
        tab_system.set_tab_has_changes("visual_tab", true).unwrap();
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(tab.has_changes);
        
        // Set has errors
        tab_system.set_tab_has_errors("visual_tab", true).unwrap();
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(tab.has_errors);
    }

    #[test]
    fn test_tab_reordering() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tabs
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        let tab3 = create_test_tab_state("tab3", "Tab 3");
        
        tab_system.create_tab(&group_id, tab1).unwrap();
        tab_system.create_tab(&group_id, tab2).unwrap();
        tab_system.create_tab(&group_id, tab3).unwrap();
        
        // Initial order should be tab1, tab2, tab3
        let tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(tabs[0].id, "tab1");
        assert_eq!(tabs[1].id, "tab2");
        assert_eq!(tabs[2].id, "tab3");
        
        // Reorder: move tab1 to position 2 (between tab2 and tab3)
        tab_system.reorder_tab("tab1", 2).unwrap();
        
        // New order should be tab2, tab1, tab3
        let tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(tabs[0].id, "tab2");
        assert_eq!(tabs[1].id, "tab1");
        assert_eq!(tabs[2].id, "tab3");
    }
}