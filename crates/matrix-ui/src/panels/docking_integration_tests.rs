//! Integration Tests for Panel Docking and Split View System
//!
//! This module contains comprehensive integration tests for complex docking scenarios,
//! testing the interaction between docking preview, split views, and space redistribution.

#[cfg(test)]
mod tests {
    use super::super::{
        docking_preview::{DockingPreview, DockingPreviewConfig, DockZone, PreviewBounds},
        split_view::{SplitView, SplitViewManager, SplitDirection, SplitPaneContent},
        space_redistribution::{SpaceRedistribution, PanelSpaceInfo, RedistributionStrategy},
        dock_container::{DockContainer, DockArea},
        panel_manager_v2::{PanelManagerV2, PanelContent},
    };
    use crate::{error::UiError, theme::ThemeManager};
    use floem::reactive::SignalGet;
    use std::{collections::HashMap, sync::Arc};
    use uuid::Uuid;

    /// Mock panel content for testing
    struct MockPanelContent {
        id: String,
        title: String,
        size: (f64, f64),
    }

    impl MockPanelContent {
        fn new(id: &str, title: &str) -> Self {
            Self {
                id: id.to_string(),
                title: title.to_string(),
                size: (300.0, 200.0),
            }
        }
    }

    impl PanelContent for MockPanelContent {
        fn id(&self) -> &str {
            &self.id
        }

        fn title(&self) -> &str {
            &self.title
        }

        fn create_view_content(&self) -> String {
            format!("Mock content for {}", self.title)
        }
    }

    /// Test fixture for integration tests
    struct DockingTestFixture {
        panel_manager: PanelManagerV2,
        split_manager: SplitViewManager,
        docking_preview: DockingPreview,
        space_redistribution: SpaceRedistribution,
    }

    impl DockingTestFixture {
        fn new() -> Self {
            let theme_manager = Arc::new(ThemeManager::new().unwrap());
            
            Self {
                panel_manager: PanelManagerV2::new(theme_manager),
                split_manager: SplitViewManager::new(),
                docking_preview: DockingPreview::new(),
                space_redistribution: SpaceRedistribution::new(),
            }
        }

        fn setup_basic_layout(&self) -> Result<(), UiError> {
            // Create main horizontal split (left sidebar + main area)
            let main_split = SplitView::new(
                SplitDirection::Horizontal,
                SplitPaneContent::DockContainer(DockArea::Left),
                SplitPaneContent::DockContainer(DockArea::Center),
            );
            let main_split_id = self.split_manager.add_split_view(main_split);
            self.split_manager.set_root_split(&main_split_id)?;

            // Create vertical split in main area (center + bottom)
            let center_split = SplitView::new(
                SplitDirection::Vertical,
                SplitPaneContent::DockContainer(DockArea::Center),
                SplitPaneContent::DockContainer(DockArea::Bottom),
            );
            self.split_manager.add_split_view(center_split);

            // Register some test panels
            let panel1 = Box::new(MockPanelContent::new("panel1", "File Explorer"));
            let panel2 = Box::new(MockPanelContent::new("panel2", "Terminal"));
            let panel3 = Box::new(MockPanelContent::new("panel3", "Output"));

            self.panel_manager.register_panel(panel1)?;
            self.panel_manager.register_panel(panel2)?;
            self.panel_manager.register_panel(panel3)?;

            // Setup space redistribution tracking
            self.space_redistribution.register_panel(PanelSpaceInfo {
                panel_id: "panel1".to_string(),
                current_size: 250.0,
                min_size: 100.0,
                max_size: None,
                priority: 1,
                last_accessed: chrono::Utc::now(),
                dock_area: DockArea::Left,
                visible: true,
            });

            self.space_redistribution.register_panel(PanelSpaceInfo {
                panel_id: "panel2".to_string(),
                current_size: 200.0,
                min_size: 100.0,
                max_size: None,
                priority: 2,
                last_accessed: chrono::Utc::now(),
                dock_area: DockArea::Bottom,
                visible: true,
            });

            self.space_redistribution.register_panel(PanelSpaceInfo {
                panel_id: "panel3".to_string(),
                current_size: 150.0,
                min_size: 100.0,
                max_size: None,
                priority: 1,
                last_accessed: chrono::Utc::now(),
                dock_area: DockArea::Bottom,
                visible: true,
            });

            Ok(())
        }
    }

    #[test]
    fn test_basic_docking_preview() {
        let fixture = DockingTestFixture::new();
        fixture.setup_basic_layout().unwrap();

        let preview = &fixture.docking_preview;

        // Setup container bounds
        preview.update_container_bounds("dock_center", PreviewBounds {
            x: 300.0,
            y: 0.0,
            width: 600.0,
            height: 400.0,
        });

        // Start preview
        preview.start_preview("panel1", (500.0, 200.0)).unwrap();
        assert!(preview.is_active());

        // Update preview with mouse in center
        preview.update_preview((500.0, 200.0)).unwrap();
        let state = preview.get_state();
        assert_eq!(state.zone, Some(DockZone::Center));

        // Update preview with mouse near left edge
        preview.update_preview((320.0, 200.0)).unwrap();
        let state = preview.get_state();
        assert_eq!(state.zone, Some(DockZone::Left));

        // End preview
        let result = preview.end_preview().unwrap();
        assert!(result.is_some());
        assert_eq!(result.unwrap().target_zone, DockZone::Left);
        assert!(!preview.is_active());
    }

    #[test]
    fn test_split_view_docking_integration() {
        let fixture = DockingTestFixture::new();
        fixture.setup_basic_layout().unwrap();

        let split_manager = &fixture.split_manager;
        let root_split = split_manager.get_root_split().unwrap();

        // Test initial split configuration
        let state = root_split.get_state();
        assert_eq!(state.direction, SplitDirection::Horizontal);
        assert_eq!(state.ratio, 0.5);

        // Test resizing
        root_split.set_total_size(1000.0).unwrap();
        root_split.set_ratio(0.3).unwrap();

        let state = root_split.get_state();
        assert_eq!(state.ratio, 0.3);
        assert_eq!(state.first_pane.size, 298.8); // (1000 - 4) * 0.3
        assert_eq!(state.second_pane.size, 697.2); // (1000 - 4) * 0.7

        // Test pane collapse
        let first_pane_id = state.first_pane.id.clone();
        root_split.collapse_pane(&first_pane_id).unwrap();

        let state = root_split.get_state();
        assert!(state.first_pane.collapsed);
        assert!(!state.first_pane.visible);
        assert_eq!(state.ratio, 0.0);
    }

    #[test]
    fn test_space_redistribution_on_panel_close() {
        let fixture = DockingTestFixture::new();
        fixture.setup_basic_layout().unwrap();

        let redistribution = &fixture.space_redistribution;

        // Close panel2 in bottom dock area
        let event = redistribution.handle_panel_closed("panel2").unwrap();

        assert_eq!(event.closed_panel_id, "panel2");
        assert_eq!(event.freed_space, 200.0);
        assert_eq!(event.dock_area, DockArea::Bottom);
        assert_eq!(event.beneficiary_panels, vec!["panel3"]);

        // panel3 should receive all the space since it's the only other panel in bottom area
        assert_eq!(event.space_distribution.get("panel3"), Some(&200.0));

        // Verify panel3 size was updated
        let panel3_info = redistribution.get_panel_space("panel3").unwrap();
        assert_eq!(panel3_info.current_size, 350.0); // 150 + 200
    }

    #[test]
    fn test_complex_docking_scenario() {
        let fixture = DockingTestFixture::new();
        fixture.setup_basic_layout().unwrap();

        // Scenario: Drag panel from left sidebar to bottom panel area
        let preview = &fixture.docking_preview;
        let panel_manager = &fixture.panel_manager;
        let redistribution = &fixture.space_redistribution;

        // Setup container bounds for bottom area
        preview.update_container_bounds("dock_bottom", PreviewBounds {
            x: 300.0,
            y: 300.0,
            width: 600.0,
            height: 200.0,
        });

        // Start dragging panel1 from left sidebar
        preview.start_preview("panel1", (500.0, 400.0)).unwrap();

        // Move to bottom area
        preview.update_preview((500.0, 400.0)).unwrap();
        let state = preview.get_state();
        assert_eq!(state.zone, Some(DockZone::Center));
        assert_eq!(state.target_area, Some(DockArea::Bottom));

        // Complete the docking operation
        let docking_result = preview.end_preview().unwrap().unwrap();
        assert_eq!(docking_result.panel_id, "panel1");
        assert_eq!(docking_result.target_zone, DockZone::Center);
        assert_eq!(docking_result.target_area, Some(DockArea::Bottom));

        // Simulate moving panel1 to bottom area
        panel_manager.remove_panel_from_dock("panel1").unwrap();
        panel_manager.add_panel_to_dock("panel1", &DockArea::Bottom).unwrap();

        // Update space redistribution
        redistribution.unregister_panel("panel1");
        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "panel1".to_string(),
            current_size: 200.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        // Verify panel is now in bottom area
        let panel_state = panel_manager.get_panel_state("panel1").unwrap();
        assert_eq!(panel_state.get().dock_area, DockArea::Bottom);
    }

    #[test]
    fn test_nested_split_view_scenario() {
        let fixture = DockingTestFixture::new();
        let split_manager = &fixture.split_manager;

        // Create a complex nested split layout
        // Root: Horizontal split (Left | Right)
        // Right: Vertical split (Top | Bottom)
        // Bottom: Horizontal split (Left | Right)

        let root_split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::DockContainer(DockArea::Left),
            SplitPaneContent::SplitView("right_split".to_string()),
        );
        let root_id = split_manager.add_split_view(root_split);
        split_manager.set_root_split(&root_id).unwrap();

        let right_split = SplitView::new(
            SplitDirection::Vertical,
            SplitPaneContent::DockContainer(DockArea::Center),
            SplitPaneContent::SplitView("bottom_split".to_string()),
        );
        split_manager.add_split_view(right_split);

        let bottom_split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::DockContainer(DockArea::Bottom),
            SplitPaneContent::DockContainer(DockArea::Right),
        );
        split_manager.add_split_view(bottom_split);

        // Test that we can navigate the nested structure
        let root = split_manager.get_root_split().unwrap();
        assert_eq!(root.get_state().direction, SplitDirection::Horizontal);

        // Test space redistribution in nested scenario
        let redistribution = &fixture.space_redistribution;
        
        // Register panels in different areas
        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "left_panel".to_string(),
            current_size: 250.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Left,
            visible: true,
        });

        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "bottom_panel".to_string(),
            current_size: 200.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "right_panel".to_string(),
            current_size: 180.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Right,
            visible: true,
        });

        // Close bottom panel and verify space goes to right panel (same split level)
        let event = redistribution.handle_panel_closed("bottom_panel").unwrap();
        assert_eq!(event.beneficiary_panels, vec!["right_panel"]);
        assert_eq!(event.space_distribution.get("right_panel"), Some(&200.0));
    }

    #[test]
    fn test_docking_preview_edge_cases() {
        let fixture = DockingTestFixture::new();
        let preview = &fixture.docking_preview;

        // Test preview without container bounds
        preview.start_preview("test_panel", (100.0, 100.0)).unwrap();
        preview.update_preview((100.0, 100.0)).unwrap();
        
        let state = preview.get_state();
        assert_eq!(state.zone, None); // No zone detected without bounds

        // Test preview with multiple overlapping containers
        preview.update_container_bounds("dock_center", PreviewBounds {
            x: 0.0,
            y: 0.0,
            width: 400.0,
            height: 300.0,
        });

        preview.update_container_bounds("dock_right", PreviewBounds {
            x: 350.0,
            y: 0.0,
            width: 200.0,
            height: 300.0,
        });

        // Mouse in overlapping area - should detect first matching container
        preview.update_preview((375.0, 150.0)).unwrap();
        let state = preview.get_state();
        assert!(state.zone.is_some());

        // Test canceling preview
        preview.cancel_preview();
        assert!(!preview.is_active());
    }

    #[test]
    fn test_space_redistribution_strategies() {
        let fixture = DockingTestFixture::new();
        let redistribution = &fixture.space_redistribution;

        // Setup panels with different sizes and priorities
        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "small_panel".to_string(),
            current_size: 100.0,
            min_size: 50.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now() - chrono::Duration::hours(1),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "large_panel".to_string(),
            current_size: 300.0,
            min_size: 50.0,
            max_size: None,
            priority: 3,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "medium_panel".to_string(),
            current_size: 200.0,
            min_size: 50.0,
            max_size: Some(400.0),
            priority: 2,
            last_accessed: chrono::Utc::now() - chrono::Duration::minutes(30),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        // Test proportional redistribution (default)
        let event = redistribution.handle_panel_closed("medium_panel").unwrap();
        assert_eq!(event.strategy, RedistributionStrategy::Proportional);
        
        // Verify proportional distribution
        let small_space = event.space_distribution.get("small_panel").unwrap();
        let large_space = event.space_distribution.get("large_panel").unwrap();
        
        // Large panel should get more space proportionally
        assert!(large_space > small_space);
        assert_eq!(small_space + large_space, 200.0); // Total freed space
    }

    #[test]
    fn test_animation_and_timing() {
        let fixture = DockingTestFixture::new();
        let redistribution = &fixture.space_redistribution;

        // Setup panels
        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "panel1".to_string(),
            current_size: 200.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        redistribution.register_panel(PanelSpaceInfo {
            panel_id: "panel2".to_string(),
            current_size: 150.0,
            min_size: 100.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area: DockArea::Bottom,
            visible: true,
        });

        // Close panel and trigger redistribution
        let initial_size = redistribution.get_panel_space("panel1").unwrap().current_size;
        redistribution.handle_panel_closed("panel2").unwrap();

        // Update animations (simulate time passing)
        redistribution.update_animations().unwrap();

        // Verify size changed
        let final_size = redistribution.get_panel_space("panel1").unwrap().current_size;
        assert!(final_size > initial_size);
    }

    #[test]
    fn test_error_handling() {
        let fixture = DockingTestFixture::new();

        // Test invalid panel operations
        let result = fixture.panel_manager.show_panel("nonexistent_panel");
        assert!(result.is_err());

        let result = fixture.space_redistribution.handle_panel_closed("nonexistent_panel");
        assert!(result.is_err());

        // Test invalid split operations
        let split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::Empty,
            SplitPaneContent::Empty,
        );

        let result = split.set_total_size(50.0); // Too small for minimum pane sizes
        assert!(result.is_err());

        let result = split.collapse_pane("nonexistent_pane");
        assert!(result.is_err());
    }

    #[test]
    fn test_performance_with_many_panels() {
        let fixture = DockingTestFixture::new();
        let redistribution = &fixture.space_redistribution;

        // Register many panels
        for i in 0..100 {
            redistribution.register_panel(PanelSpaceInfo {
                panel_id: format!("panel_{}", i),
                current_size: 100.0 + (i as f64 * 10.0),
                min_size: 50.0,
                max_size: None,
                priority: i % 5,
                last_accessed: chrono::Utc::now(),
                dock_area: DockArea::Bottom,
                visible: true,
            });
        }

        // Measure time for redistribution
        let start = std::time::Instant::now();
        let _event = redistribution.handle_panel_closed("panel_50").unwrap();
        let duration = start.elapsed();

        // Should complete quickly even with many panels
        assert!(duration.as_millis() < 100);

        // Verify statistics
        let stats = redistribution.get_space_statistics();
        assert_eq!(stats.visible_panels, 99); // One panel was closed
        assert!(stats.total_space > 0.0);
    }
}