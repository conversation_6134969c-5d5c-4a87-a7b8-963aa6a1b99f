//! Space Redistribution System for MATRIX IDE
//!
//! This module handles automatic space redistribution when panels are closed,
//! ensuring optimal use of available screen real estate.

use std::collections::HashMap;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate, SignalWith};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use super::dock_container::{DockArea, DockAreaId, TabGroupId};
use super::split_view::{SplitView, SplitDirection, SplitPaneContent};

/// Space redistribution strategy
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RedistributionStrategy {
    /// Distribute space equally among remaining panels
    Equal,
    /// Distribute space proportionally based on current sizes
    Proportional,
    /// Give all space to the largest remaining panel
    ToLargest,
    /// Give all space to the most recently used panel
    ToMostRecent,
    /// Custom distribution based on panel priorities
    Priority,
}

/// Space redistribution configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RedistributionConfig {
    /// Default strategy to use
    pub default_strategy: RedistributionStrategy,
    /// Minimum size to maintain for panels
    pub min_panel_size: f64,
    /// Animation duration for redistribution
    pub animation_duration: u32,
    /// Enable smooth transitions
    pub enable_animations: bool,
    /// Panel priorities for priority-based redistribution
    pub panel_priorities: HashMap<String, i32>,
    /// Dock area preferences
    pub area_preferences: HashMap<DockArea, RedistributionStrategy>,
}

impl Default for RedistributionConfig {
    fn default() -> Self {
        Self {
            default_strategy: RedistributionStrategy::Proportional,
            min_panel_size: 100.0,
            animation_duration: 300,
            enable_animations: true,
            panel_priorities: HashMap::new(),
            area_preferences: HashMap::new(),
        }
    }
}

/// Panel space information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelSpaceInfo {
    /// Panel identifier
    pub panel_id: String,
    /// Current size
    pub current_size: f64,
    /// Minimum size
    pub min_size: f64,
    /// Maximum size (optional)
    pub max_size: Option<f64>,
    /// Priority for space allocation
    pub priority: i32,
    /// Last access time
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    /// Dock area
    pub dock_area: DockArea,
    /// Is panel visible
    pub visible: bool,
}

/// Space redistribution event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedistributionEvent {
    /// Event identifier
    pub id: String,
    /// Closed panel ID
    pub closed_panel_id: String,
    /// Freed space amount
    pub freed_space: f64,
    /// Affected dock area
    pub dock_area: DockArea,
    /// Redistribution strategy used
    pub strategy: RedistributionStrategy,
    /// Panels that received space
    pub beneficiary_panels: Vec<String>,
    /// Space distribution map
    pub space_distribution: HashMap<String, f64>,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Space redistribution system
pub struct SpaceRedistribution {
    /// Configuration
    config: RedistributionConfig,
    /// Current panel space information
    panel_spaces: RwSignal<HashMap<String, PanelSpaceInfo>>,
    /// Redistribution history
    redistribution_history: RwSignal<Vec<RedistributionEvent>>,
    /// Active redistributions (for animations)
    active_redistributions: RwSignal<HashMap<String, RedistributionAnimation>>,
}

/// Animation state for redistribution
#[derive(Debug, Clone)]
pub struct RedistributionAnimation {
    /// Animation ID
    pub id: String,
    /// Start time
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// Duration in milliseconds
    pub duration: u32,
    /// Initial sizes
    pub initial_sizes: HashMap<String, f64>,
    /// Target sizes
    pub target_sizes: HashMap<String, f64>,
    /// Current progress (0.0 to 1.0)
    pub progress: f64,
}

impl SpaceRedistribution {
    /// Create a new space redistribution system
    pub fn new() -> Self {
        Self {
            config: RedistributionConfig::default(),
            panel_spaces: create_rw_signal(HashMap::new()),
            redistribution_history: create_rw_signal(Vec::new()),
            active_redistributions: create_rw_signal(HashMap::new()),
        }
    }

    /// Create with custom configuration
    pub fn with_config(config: RedistributionConfig) -> Self {
        Self {
            config,
            panel_spaces: create_rw_signal(HashMap::new()),
            redistribution_history: create_rw_signal(Vec::new()),
            active_redistributions: create_rw_signal(HashMap::new()),
        }
    }

    /// Register a panel for space tracking
    pub fn register_panel(&self, panel_info: PanelSpaceInfo) {
        self.panel_spaces.update(|spaces| {
            spaces.insert(panel_info.panel_id.clone(), panel_info);
        });
    }

    /// Unregister a panel
    pub fn unregister_panel(&self, panel_id: &str) {
        self.panel_spaces.update(|spaces| {
            spaces.remove(panel_id);
        });
    }

    /// Update panel space information
    pub fn update_panel_space(&self, panel_id: &str, size: f64) -> Result<(), UiError> {
        let panel_found = self.panel_spaces.with_untracked(|spaces| {
            spaces.contains_key(panel_id)
        });

        if !panel_found {
            return Err(UiError::PanelError(format!("Panel {} not registered", panel_id)));
        }

        self.panel_spaces.update(|spaces| {
            if let Some(panel_info) = spaces.get_mut(panel_id) {
                panel_info.current_size = size;
                panel_info.last_accessed = chrono::Utc::now();
            }
        });

        Ok(())
    }

    /// Handle panel closure and redistribute space
    pub fn handle_panel_closed(&self, panel_id: &str) -> Result<RedistributionEvent, UiError> {
        let panel_info = self.panel_spaces.with_untracked(|spaces| {
            spaces.get(panel_id).cloned()
        });

        let panel_info = panel_info.ok_or_else(|| {
            UiError::PanelError(format!("Panel {} not found", panel_id))
        })?;

        let freed_space = panel_info.current_size;
        let dock_area = panel_info.dock_area.clone();

        // Get strategy for this dock area
        let strategy = self.config.area_preferences
            .get(&dock_area)
            .copied()
            .unwrap_or(self.config.default_strategy);

        // Find panels in the same dock area
        let area_panels = self.get_panels_in_area(&dock_area);
        let visible_panels: Vec<_> = area_panels.into_iter()
            .filter(|p| p.visible && p.panel_id != panel_id)
            .collect();

        if visible_panels.is_empty() {
            return Err(UiError::PanelError("No visible panels to redistribute space to".to_string()));
        }

        // Calculate space distribution
        let space_distribution = self.calculate_space_distribution(
            &visible_panels,
            freed_space,
            strategy,
        )?;

        // Create redistribution event
        let event = RedistributionEvent {
            id: Uuid::new_v4().to_string(),
            closed_panel_id: panel_id.to_string(),
            freed_space,
            dock_area: dock_area.clone(),
            strategy,
            beneficiary_panels: space_distribution.keys().cloned().collect(),
            space_distribution: space_distribution.clone(),
            timestamp: chrono::Utc::now(),
        };

        // Apply the redistribution
        self.apply_redistribution(&space_distribution)?;

        // Record the event
        self.redistribution_history.update(|history| {
            history.push(event.clone());
            // Keep only last 100 events
            if history.len() > 100 {
                history.remove(0);
            }
        });

        // Remove the closed panel
        self.unregister_panel(panel_id);

        Ok(event)
    }

    /// Get panels in a specific dock area
    fn get_panels_in_area(&self, dock_area: &DockArea) -> Vec<PanelSpaceInfo> {
        self.panel_spaces.with_untracked(|spaces| {
            spaces.values()
                .filter(|panel| panel.dock_area == *dock_area)
                .cloned()
                .collect()
        })
    }

    /// Calculate space distribution based on strategy
    fn calculate_space_distribution(
        &self,
        panels: &[PanelSpaceInfo],
        freed_space: f64,
        strategy: RedistributionStrategy,
    ) -> Result<HashMap<String, f64>, UiError> {
        let mut distribution = HashMap::new();

        match strategy {
            RedistributionStrategy::Equal => {
                let space_per_panel = freed_space / panels.len() as f64;
                for panel in panels {
                    distribution.insert(panel.panel_id.clone(), space_per_panel);
                }
            }

            RedistributionStrategy::Proportional => {
                let total_current_size: f64 = panels.iter().map(|p| p.current_size).sum();
                if total_current_size > 0.0 {
                    for panel in panels {
                        let proportion = panel.current_size / total_current_size;
                        let additional_space = freed_space * proportion;
                        distribution.insert(panel.panel_id.clone(), additional_space);
                    }
                } else {
                    // Fallback to equal distribution
                    let space_per_panel = freed_space / panels.len() as f64;
                    for panel in panels {
                        distribution.insert(panel.panel_id.clone(), space_per_panel);
                    }
                }
            }

            RedistributionStrategy::ToLargest => {
                if let Some(largest_panel) = panels.iter().max_by(|a, b| {
                    a.current_size.partial_cmp(&b.current_size).unwrap()
                }) {
                    distribution.insert(largest_panel.panel_id.clone(), freed_space);
                }
            }

            RedistributionStrategy::ToMostRecent => {
                if let Some(most_recent_panel) = panels.iter().max_by_key(|p| p.last_accessed) {
                    distribution.insert(most_recent_panel.panel_id.clone(), freed_space);
                }
            }

            RedistributionStrategy::Priority => {
                // Sort panels by priority (higher priority gets more space)
                let mut sorted_panels: Vec<_> = panels.iter().collect();
                sorted_panels.sort_by_key(|p| std::cmp::Reverse(p.priority));

                let total_priority: i32 = sorted_panels.iter().map(|p| p.priority.max(1)).sum();
                
                if total_priority > 0 {
                    for panel in sorted_panels {
                        let priority_ratio = panel.priority.max(1) as f64 / total_priority as f64;
                        let additional_space = freed_space * priority_ratio;
                        distribution.insert(panel.panel_id.clone(), additional_space);
                    }
                } else {
                    // Fallback to equal distribution
                    let space_per_panel = freed_space / panels.len() as f64;
                    for panel in panels {
                        distribution.insert(panel.panel_id.clone(), space_per_panel);
                    }
                }
            }
        }

        // Apply minimum size constraints
        self.apply_size_constraints(&mut distribution)?;

        Ok(distribution)
    }

    /// Apply size constraints to distribution
    fn apply_size_constraints(&self, distribution: &mut HashMap<String, f64>) -> Result<(), UiError> {
        let panel_spaces = self.panel_spaces.get();
        
        for (panel_id, additional_space) in distribution.iter_mut() {
            if let Some(panel_info) = panel_spaces.get(panel_id) {
                let new_size = panel_info.current_size + *additional_space;
                
                // Apply minimum size constraint
                if new_size < panel_info.min_size {
                    *additional_space = panel_info.min_size - panel_info.current_size;
                }
                
                // Apply maximum size constraint if set
                if let Some(max_size) = panel_info.max_size {
                    if new_size > max_size {
                        *additional_space = max_size - panel_info.current_size;
                    }
                }
            }
        }

        Ok(())
    }

    /// Apply redistribution to panels
    fn apply_redistribution(&self, distribution: &HashMap<String, f64>) -> Result<(), UiError> {
        if self.config.enable_animations {
            self.start_redistribution_animation(distribution)?;
        } else {
            self.apply_redistribution_immediately(distribution)?;
        }

        Ok(())
    }

    /// Apply redistribution immediately without animation
    fn apply_redistribution_immediately(&self, distribution: &HashMap<String, f64>) -> Result<(), UiError> {
        self.panel_spaces.update(|spaces| {
            for (panel_id, additional_space) in distribution {
                if let Some(panel_info) = spaces.get_mut(panel_id) {
                    panel_info.current_size += additional_space;
                    panel_info.last_accessed = chrono::Utc::now();
                }
            }
        });

        Ok(())
    }

    /// Start animated redistribution
    fn start_redistribution_animation(&self, distribution: &HashMap<String, f64>) -> Result<(), UiError> {
        let animation_id = Uuid::new_v4().to_string();
        
        // Get initial sizes
        let initial_sizes = self.panel_spaces.with_untracked(|spaces| {
            distribution.keys()
                .filter_map(|panel_id| {
                    spaces.get(panel_id).map(|info| (panel_id.clone(), info.current_size))
                })
                .collect::<HashMap<_, _>>()
        });

        // Calculate target sizes
        let target_sizes: HashMap<String, f64> = initial_sizes.iter()
            .map(|(panel_id, initial_size)| {
                let additional = distribution.get(panel_id).copied().unwrap_or(0.0);
                (panel_id.clone(), initial_size + additional)
            })
            .collect();

        let animation = RedistributionAnimation {
            id: animation_id.clone(),
            start_time: chrono::Utc::now(),
            duration: self.config.animation_duration,
            initial_sizes,
            target_sizes,
            progress: 0.0,
        };

        self.active_redistributions.update(|animations| {
            animations.insert(animation_id, animation);
        });

        Ok(())
    }

    /// Update animation progress (should be called from animation loop)
    pub fn update_animations(&self) -> Result<(), UiError> {
        let now = chrono::Utc::now();
        let mut completed_animations = Vec::new();

        self.active_redistributions.update(|animations| {
            for (animation_id, animation) in animations.iter_mut() {
                let elapsed = now.signed_duration_since(animation.start_time);
                let elapsed_ms = elapsed.num_milliseconds() as u32;
                
                if elapsed_ms >= animation.duration {
                    animation.progress = 1.0;
                    completed_animations.push(animation_id.clone());
                } else {
                    animation.progress = elapsed_ms as f64 / animation.duration as f64;
                }

                // Apply easing function (ease-out)
                let eased_progress = 1.0 - (1.0 - animation.progress).powi(3);

                // Update panel sizes based on animation progress
                self.panel_spaces.update(|spaces| {
                    for (panel_id, initial_size) in &animation.initial_sizes {
                        if let (Some(target_size), Some(panel_info)) = (
                            animation.target_sizes.get(panel_id),
                            spaces.get_mut(panel_id)
                        ) {
                            let size_diff = target_size - initial_size;
                            panel_info.current_size = initial_size + (size_diff * eased_progress);
                        }
                    }
                });
            }
        });

        // Remove completed animations
        for animation_id in completed_animations {
            self.active_redistributions.update(|animations| {
                animations.remove(&animation_id);
            });
        }

        Ok(())
    }

    /// Get redistribution history
    pub fn get_history(&self) -> Vec<RedistributionEvent> {
        self.redistribution_history.get()
    }

    /// Clear redistribution history
    pub fn clear_history(&self) {
        self.redistribution_history.update(|history| {
            history.clear();
        });
    }

    /// Get panel space information
    pub fn get_panel_space(&self, panel_id: &str) -> Option<PanelSpaceInfo> {
        self.panel_spaces.with_untracked(|spaces| {
            spaces.get(panel_id).cloned()
        })
    }

    /// Get all panel spaces
    pub fn get_all_panel_spaces(&self) -> HashMap<String, PanelSpaceInfo> {
        self.panel_spaces.get()
    }

    /// Update configuration
    pub fn update_config(&mut self, config: RedistributionConfig) {
        self.config = config;
    }

    /// Get configuration
    pub fn get_config(&self) -> &RedistributionConfig {
        &self.config
    }

    /// Set panel priority
    pub fn set_panel_priority(&self, panel_id: &str, priority: i32) -> Result<(), UiError> {
        let panel_found = self.panel_spaces.with_untracked(|spaces| {
            spaces.contains_key(panel_id)
        });

        if !panel_found {
            return Err(UiError::PanelError(format!("Panel {} not found", panel_id)));
        }

        self.panel_spaces.update(|spaces| {
            if let Some(panel_info) = spaces.get_mut(panel_id) {
                panel_info.priority = priority;
            }
        });

        Ok(())
    }

    /// Get statistics about space usage
    pub fn get_space_statistics(&self) -> SpaceStatistics {
        let panel_spaces = self.panel_spaces.get();
        
        let total_space: f64 = panel_spaces.values().map(|p| p.current_size).sum();
        let visible_panels = panel_spaces.values().filter(|p| p.visible).count();
        let total_panels = panel_spaces.len();
        
        let average_size = if visible_panels > 0 {
            total_space / visible_panels as f64
        } else {
            0.0
        };

        let largest_panel = panel_spaces.values()
            .max_by(|a, b| a.current_size.partial_cmp(&b.current_size).unwrap())
            .map(|p| (p.panel_id.clone(), p.current_size));

        let smallest_panel = panel_spaces.values()
            .filter(|p| p.visible)
            .min_by(|a, b| a.current_size.partial_cmp(&b.current_size).unwrap())
            .map(|p| (p.panel_id.clone(), p.current_size));

        SpaceStatistics {
            total_space,
            visible_panels,
            total_panels,
            average_size,
            largest_panel,
            smallest_panel,
        }
    }
}

/// Space usage statistics
#[derive(Debug, Clone)]
pub struct SpaceStatistics {
    pub total_space: f64,
    pub visible_panels: usize,
    pub total_panels: usize,
    pub average_size: f64,
    pub largest_panel: Option<(String, f64)>,
    pub smallest_panel: Option<(String, f64)>,
}

impl Default for SpaceRedistribution {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_panel(id: &str, size: f64, dock_area: DockArea) -> PanelSpaceInfo {
        PanelSpaceInfo {
            panel_id: id.to_string(),
            current_size: size,
            min_size: 50.0,
            max_size: None,
            priority: 1,
            last_accessed: chrono::Utc::now(),
            dock_area,
            visible: true,
        }
    }

    #[test]
    fn test_space_redistribution_creation() {
        let redistribution = SpaceRedistribution::new();
        assert_eq!(redistribution.get_all_panel_spaces().len(), 0);
    }

    #[test]
    fn test_panel_registration() {
        let redistribution = SpaceRedistribution::new();
        let panel = create_test_panel("test_panel", 200.0, DockArea::Bottom);
        
        redistribution.register_panel(panel.clone());
        
        let registered = redistribution.get_panel_space("test_panel");
        assert!(registered.is_some());
        assert_eq!(registered.unwrap().current_size, 200.0);
    }

    #[test]
    fn test_equal_redistribution() {
        let redistribution = SpaceRedistribution::new();
        
        // Register three panels
        redistribution.register_panel(create_test_panel("panel1", 100.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("panel2", 150.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("panel3", 200.0, DockArea::Bottom));
        
        // Close panel3 (200.0 space freed)
        let event = redistribution.handle_panel_closed("panel3").unwrap();
        
        assert_eq!(event.freed_space, 200.0);
        assert_eq!(event.beneficiary_panels.len(), 2);
        
        // Each remaining panel should get 100.0 additional space
        assert_eq!(event.space_distribution.get("panel1"), Some(&100.0));
        assert_eq!(event.space_distribution.get("panel2"), Some(&100.0));
    }

    #[test]
    fn test_proportional_redistribution() {
        let mut config = RedistributionConfig::default();
        config.default_strategy = RedistributionStrategy::Proportional;
        
        let redistribution = SpaceRedistribution::with_config(config);
        
        // Register panels with different sizes
        redistribution.register_panel(create_test_panel("panel1", 100.0, DockArea::Bottom)); // 1/3 of total
        redistribution.register_panel(create_test_panel("panel2", 200.0, DockArea::Bottom)); // 2/3 of total
        redistribution.register_panel(create_test_panel("panel3", 300.0, DockArea::Bottom)); // To be closed
        
        let event = redistribution.handle_panel_closed("panel3").unwrap();
        
        // panel1 should get 1/3 of freed space (100.0)
        // panel2 should get 2/3 of freed space (200.0)
        assert_eq!(event.space_distribution.get("panel1"), Some(&100.0));
        assert_eq!(event.space_distribution.get("panel2"), Some(&200.0));
    }

    #[test]
    fn test_to_largest_redistribution() {
        let mut config = RedistributionConfig::default();
        config.default_strategy = RedistributionStrategy::ToLargest;
        
        let redistribution = SpaceRedistribution::with_config(config);
        
        redistribution.register_panel(create_test_panel("small", 100.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("large", 300.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("medium", 200.0, DockArea::Bottom));
        
        let event = redistribution.handle_panel_closed("medium").unwrap();
        
        // All space should go to the largest panel
        assert_eq!(event.space_distribution.get("large"), Some(&200.0));
        assert_eq!(event.space_distribution.get("small"), None);
    }

    #[test]
    fn test_priority_redistribution() {
        let mut config = RedistributionConfig::default();
        config.default_strategy = RedistributionStrategy::Priority;
        
        let redistribution = SpaceRedistribution::with_config(config);
        
        let mut panel1 = create_test_panel("low_priority", 100.0, DockArea::Bottom);
        panel1.priority = 1;
        let mut panel2 = create_test_panel("high_priority", 100.0, DockArea::Bottom);
        panel2.priority = 3;
        let panel3 = create_test_panel("to_close", 200.0, DockArea::Bottom);
        
        redistribution.register_panel(panel1);
        redistribution.register_panel(panel2);
        redistribution.register_panel(panel3);
        
        let event = redistribution.handle_panel_closed("to_close").unwrap();
        
        // High priority panel should get more space
        let low_space = event.space_distribution.get("low_priority").unwrap();
        let high_space = event.space_distribution.get("high_priority").unwrap();
        
        assert!(high_space > low_space);
        assert_eq!(low_space + high_space, 200.0); // Total freed space
    }

    #[test]
    fn test_space_statistics() {
        let redistribution = SpaceRedistribution::new();
        
        redistribution.register_panel(create_test_panel("panel1", 100.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("panel2", 200.0, DockArea::Bottom));
        redistribution.register_panel(create_test_panel("panel3", 300.0, DockArea::Bottom));
        
        let stats = redistribution.get_space_statistics();
        
        assert_eq!(stats.total_space, 600.0);
        assert_eq!(stats.visible_panels, 3);
        assert_eq!(stats.total_panels, 3);
        assert_eq!(stats.average_size, 200.0);
        assert_eq!(stats.largest_panel, Some(("panel3".to_string(), 300.0)));
        assert_eq!(stats.smallest_panel, Some(("panel1".to_string(), 100.0)));
    }
}