//! Tests for the Tab System
//!
//! This module contains comprehensive tests for tab operations, drag-drop functionality,
//! and visual state updates.

#[cfg(test)]
mod tests {
    use super::super::tab_system::{TabSystem, TabState, TabGroupConfig, TabEvent};
    use super::super::dock_container::DockArea;
    use crate::error::UiError;
    use std::sync::{Arc, Mutex};

    fn create_test_tab_state(id: &str, title: &str) -> TabState {
        TabState {
            id: id.to_string(),
            title: title.to_string(),
            content_type: "text".to_string(),
            has_changes: false,
            has_errors: false,
            is_active: false,
            is_closable: true,
            icon: None,
            tooltip: None,
        }
    }

    fn create_test_group_config(dock_area: DockArea) -> TabGroupConfig {
        TabGroupConfig {
            dock_area,
            ..Default::default()
        }
    }

    #[tokio::test]
    async fn test_tab_group_creation() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        
        let group_id = tab_system.create_tab_group(config.clone()).unwrap();
        assert!(!group_id.is_empty());
        
        // Verify group exists
        let tabs = tab_system.get_group_tabs(&group_id);
        assert!(tabs.is_empty()); // Should be empty initially
    }

    #[tokio::test]
    async fn test_tab_creation_and_management() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create a tab
        let tab_state = create_test_tab_state("tab1", "Test Tab 1");
        tab_system.create_tab(&group_id, tab_state.clone()).unwrap();
        
        // Verify tab exists
        let retrieved_tab = tab_system.get_tab("tab1").unwrap();
        assert_eq!(retrieved_tab.id, "tab1");
        assert_eq!(retrieved_tab.title, "Test Tab 1");
        
        // Verify tab is in group
        let group_tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(group_tabs.len(), 1);
        assert_eq!(group_tabs[0].id, "tab1");
        
        // Verify tab is active (first tab should be active)
        let active_tab = tab_system.get_active_tab(&group_id).unwrap();
        assert_eq!(active_tab.id, "tab1");
    }

    #[tokio::test]
    async fn test_tab_activation() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create multiple tabs
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        
        tab_system.create_tab(&group_id, tab1).unwrap();
        tab_system.create_tab(&group_id, tab2).unwrap();
        
        // First tab should be active initially
        let active_tab = tab_system.get_active_tab(&group_id).unwrap();
        assert_eq!(active_tab.id, "tab1");
        
        // Activate second tab
        tab_system.activate_tab("tab2").unwrap();
        let active_tab = tab_system.get_active_tab(&group_id).unwrap();
        assert_eq!(active_tab.id, "tab2");
    }

    #[tokio::test]
    async fn test_tab_closing() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tabs
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        
        tab_system.create_tab(&group_id, tab1).unwrap();
        tab_system.create_tab(&group_id, tab2).unwrap();
        
        // Verify both tabs exist
        assert_eq!(tab_system.get_group_tabs(&group_id).len(), 2);
        
        // Close first tab
        tab_system.close_tab("tab1").unwrap();
        
        // Verify tab is removed
        assert!(tab_system.get_tab("tab1").is_none());
        assert_eq!(tab_system.get_group_tabs(&group_id).len(), 1);
        
        // Verify active tab switched to remaining tab
        let active_tab = tab_system.get_active_tab(&group_id).unwrap();
        assert_eq!(active_tab.id, "tab2");
    }

    #[tokio::test]
    async fn test_tab_reordering() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tabs
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        let tab3 = create_test_tab_state("tab3", "Tab 3");
        
        tab_system.create_tab(&group_id, tab1).unwrap();
        tab_system.create_tab(&group_id, tab2).unwrap();
        tab_system.create_tab(&group_id, tab3).unwrap();
        
        // Initial order should be tab1, tab2, tab3
        let tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(tabs[0].id, "tab1");
        assert_eq!(tabs[1].id, "tab2");
        assert_eq!(tabs[2].id, "tab3");
        
        // Reorder: move tab1 to position 2 (between tab2 and tab3)
        tab_system.reorder_tab("tab1", 2).unwrap();
        
        // New order should be tab2, tab1, tab3
        let tabs = tab_system.get_group_tabs(&group_id);
        assert_eq!(tabs[0].id, "tab2");
        assert_eq!(tabs[1].id, "tab1");
        assert_eq!(tabs[2].id, "tab3");
    }

    #[tokio::test]
    async fn test_tab_moving_between_groups() {
        let tab_system = TabSystem::new();
        
        // Create two groups
        let config1 = create_test_group_config(DockArea::Bottom);
        let config2 = create_test_group_config(DockArea::Right);
        let group1_id = tab_system.create_tab_group(config1).unwrap();
        let group2_id = tab_system.create_tab_group(config2).unwrap();
        
        // Create tab in first group
        let tab_state = create_test_tab_state("tab1", "Movable Tab");
        tab_system.create_tab(&group1_id, tab_state).unwrap();
        
        // Verify tab is in first group
        assert_eq!(tab_system.get_group_tabs(&group1_id).len(), 1);
        assert_eq!(tab_system.get_group_tabs(&group2_id).len(), 0);
        
        // Move tab to second group
        tab_system.move_tab("tab1", &group2_id, 0).unwrap();
        
        // Verify tab moved
        assert_eq!(tab_system.get_group_tabs(&group1_id).len(), 0);
        assert_eq!(tab_system.get_group_tabs(&group2_id).len(), 1);
        
        let moved_tab = tab_system.get_group_tabs(&group2_id)[0].clone();
        assert_eq!(moved_tab.id, "tab1");
    }

    #[tokio::test]
    async fn test_drag_and_drop_operations() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("drag_tab", "Draggable Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Test drag capability check
        assert!(tab_system.can_drag_tab("drag_tab"));
        
        // Start drag
        tab_system.start_drag("drag_tab", (100.0, 50.0), (10.0, 5.0)).unwrap();
        
        // Verify drag state
        let drag_state = tab_system.get_drag_state().unwrap();
        assert_eq!(drag_state.dragged_tab, "drag_tab");
        assert_eq!(drag_state.drag_position, (100.0, 50.0));
        assert!(drag_state.is_dragging);
        
        // Update drag position
        tab_system.update_drag((150.0, 75.0)).unwrap();
        
        // Verify updated position
        let updated_drag_state = tab_system.get_drag_state().unwrap();
        assert_eq!(updated_drag_state.drag_position, (150.0, 75.0));
        
        // End drag (should complete successfully even without valid drop target)
        tab_system.end_drag().unwrap();
        
        // Verify drag state is cleared
        assert!(tab_system.get_drag_state().is_none());
        assert!(tab_system.get_drop_target().is_none());
    }

    #[tokio::test]
    async fn test_visual_state_indicators() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("visual_tab", "Visual Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Initially no changes or errors
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(!tab.has_changes);
        assert!(!tab.has_errors);
        
        // Set has changes
        tab_system.set_tab_has_changes("visual_tab", true).unwrap();
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(tab.has_changes);
        
        // Set has errors
        tab_system.set_tab_has_errors("visual_tab", true).unwrap();
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(tab.has_errors);
        
        // Clear changes
        tab_system.set_tab_has_changes("visual_tab", false).unwrap();
        let tab = tab_system.get_tab("visual_tab").unwrap();
        assert!(!tab.has_changes);
        assert!(tab.has_errors); // Errors should still be there
    }

    #[tokio::test]
    async fn test_event_handling() {
        let tab_system = TabSystem::new();
        let events = Arc::new(Mutex::new(Vec::new()));
        let events_clone = events.clone();
        
        // Register event handler
        tab_system.on_event("test_handler", move |event| {
            events_clone.lock().unwrap().push(format!("{:?}", event));
            Ok(())
        });
        
        // Create group and tab to trigger events
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        let tab_state = create_test_tab_state("event_tab", "Event Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Activate tab
        tab_system.activate_tab("event_tab").unwrap();
        
        // Close tab
        tab_system.close_tab("event_tab").unwrap();
        
        // Verify events were captured
        let captured_events = events.lock().unwrap();
        assert!(captured_events.len() >= 3); // At least GroupCreated, TabCreated, TabActivated, TabClosed
        
        // Check for specific events
        let events_str = captured_events.join("\n");
        assert!(events_str.contains("GroupCreated"));
        assert!(events_str.contains("TabCreated"));
        assert!(events_str.contains("TabActivated"));
        assert!(events_str.contains("TabClosed"));
    }

    #[tokio::test]
    async fn test_tab_group_removal() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tabs in the group
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        
        tab_system.create_tab(&group_id, tab1).unwrap();
        tab_system.create_tab(&group_id, tab2).unwrap();
        
        // Verify tabs exist
        assert_eq!(tab_system.get_group_tabs(&group_id).len(), 2);
        assert!(tab_system.get_tab("tab1").is_some());
        assert!(tab_system.get_tab("tab2").is_some());
        
        // Remove group
        tab_system.remove_tab_group(&group_id).unwrap();
        
        // Verify all tabs in group are removed
        assert_eq!(tab_system.get_group_tabs(&group_id).len(), 0);
        assert!(tab_system.get_tab("tab1").is_none());
        assert!(tab_system.get_tab("tab2").is_none());
    }

    #[tokio::test]
    async fn test_max_tabs_limit() {
        let tab_system = TabSystem::new();
        let mut config = create_test_group_config(DockArea::Bottom);
        config.max_tabs = Some(2); // Limit to 2 tabs
        
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create first two tabs (should succeed)
        let tab1 = create_test_tab_state("tab1", "Tab 1");
        let tab2 = create_test_tab_state("tab2", "Tab 2");
        
        assert!(tab_system.create_tab(&group_id, tab1).is_ok());
        assert!(tab_system.create_tab(&group_id, tab2).is_ok());
        
        // Try to create third tab (should fail)
        let tab3 = create_test_tab_state("tab3", "Tab 3");
        assert!(tab_system.create_tab(&group_id, tab3).is_err());
        
        // Verify only 2 tabs exist
        assert_eq!(tab_system.get_group_tabs(&group_id).len(), 2);
    }

    #[tokio::test]
    async fn test_drag_restrictions() {
        let tab_system = TabSystem::new();
        
        // Create group that doesn't allow drag out
        let mut config = create_test_group_config(DockArea::Bottom);
        config.allow_drag_out = false;
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("restricted_tab", "Restricted Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Try to start drag (should fail)
        let result = tab_system.start_drag("restricted_tab", (100.0, 50.0), (10.0, 5.0));
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_drag_cancel_operation() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("cancel_tab", "Cancel Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // Start drag
        tab_system.start_drag("cancel_tab", (100.0, 50.0), (10.0, 5.0)).unwrap();
        assert!(tab_system.get_drag_state().is_some());
        
        // Cancel drag
        tab_system.cancel_drag().unwrap();
        assert!(tab_system.get_drag_state().is_none());
        assert!(tab_system.get_drop_target().is_none());
    }

    #[tokio::test]
    async fn test_drag_feedback_messages() {
        let tab_system = TabSystem::new();
        let config = create_test_group_config(DockArea::Bottom);
        let group_id = tab_system.create_tab_group(config).unwrap();
        
        // Create tab
        let tab_state = create_test_tab_state("feedback_tab", "Feedback Tab");
        tab_system.create_tab(&group_id, tab_state).unwrap();
        
        // No feedback when not dragging
        assert!(tab_system.get_drag_feedback().is_none());
        
        // Start drag
        tab_system.start_drag("feedback_tab", (100.0, 50.0), (10.0, 5.0)).unwrap();
        
        // Still no feedback without drop target
        assert!(tab_system.get_drag_feedback().is_none());
    }

    #[tokio::test]
    async fn test_complex_drag_drop_scenario() {
        let tab_system = TabSystem::new();
        
        // Create two groups
        let config1 = create_test_group_config(DockArea::Bottom);
        let config2 = create_test_group_config(DockArea::Right);
        let group1_id = tab_system.create_tab_group(config1).unwrap();
        let group2_id = tab_system.create_tab_group(config2).unwrap();
        
        // Create multiple tabs in first group
        for i in 1..=3 {
            let tab_state = create_test_tab_state(&format!("tab{}", i), &format!("Tab {}", i));
            tab_system.create_tab(&group1_id, tab_state).unwrap();
        }
        
        // Verify initial state
        assert_eq!(tab_system.get_group_tabs(&group1_id).len(), 3);
        assert_eq!(tab_system.get_group_tabs(&group2_id).len(), 0);
        
        // Test drag capability
        assert!(tab_system.can_drag_tab("tab2"));
        assert!(tab_system.can_drop_tab(&group2_id));
        
        // Perform drag and drop operation
        tab_system.start_drag("tab2", (100.0, 50.0), (10.0, 5.0)).unwrap();
        tab_system.move_tab("tab2", &group2_id, 0).unwrap();
        tab_system.end_drag().unwrap();
        
        // Verify final state
        assert_eq!(tab_system.get_group_tabs(&group1_id).len(), 2);
        assert_eq!(tab_system.get_group_tabs(&group2_id).len(), 1);
        
        let moved_tab = tab_system.get_group_tabs(&group2_id)[0].clone();
        assert_eq!(moved_tab.id, "tab2");
    }

    #[tokio::test]
    async fn test_error_handling_invalid_operations() {
        let tab_system = TabSystem::new();
        
        // Try to create tab in non-existent group
        let tab_state = create_test_tab_state("orphan_tab", "Orphan Tab");
        let result = tab_system.create_tab("non_existent_group", tab_state);
        assert!(result.is_err());
        
        // Try to activate non-existent tab
        let result = tab_system.activate_tab("non_existent_tab");
        assert!(result.is_err());
        
        // Try to close non-existent tab
        let result = tab_system.close_tab("non_existent_tab");
        assert!(result.is_err());
        
        // Try to move non-existent tab
        let result = tab_system.move_tab("non_existent_tab", "some_group", 0);
        assert!(result.is_err());
        
        // Try to drag non-existent tab
        let result = tab_system.start_drag("non_existent_tab", (0.0, 0.0), (0.0, 0.0));
        assert!(result.is_err());
        
        // Test drag capability for non-existent tab
        assert!(!tab_system.can_drag_tab("non_existent_tab"));
    }
}