//! Docking Preview System for MATRIX IDE
//!
//! This module implements the visual feedback system for panel docking operations,
//! providing real-time preview of where panels will be docked during drag operations.

use std::collections::HashMap;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use floem::views::{container, empty, Decorators};
use floem::{View, IntoView};
use floem::style::{Position, Display};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use super::dock_container::{DockArea, DockAreaId, TabGroupId};

/// Docking preview zones for visual feedback
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DockZone {
    /// Top edge of container
    Top,
    /// Bottom edge of container
    Bottom,
    /// Left edge of container
    Left,
    /// Right edge of container
    Right,
    /// Center of container (tab addition)
    Center,
    /// Split horizontally (create new horizontal split)
    SplitHorizontal,
    /// Split vertically (create new vertical split)
    SplitVertical,
}

/// Docking preview state
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DockingPreviewState {
    /// Is preview currently active
    pub active: bool,
    /// Current preview zone
    pub zone: Option<DockZone>,
    /// Target dock area
    pub target_area: Option<DockArea>,
    /// Target tab group
    pub target_group: Option<TabGroupId>,
    /// Preview rectangle bounds
    pub preview_bounds: Option<PreviewBounds>,
    /// Mouse position
    pub mouse_position: (f64, f64),
    /// Dragged panel ID
    pub dragged_panel: Option<String>,
}

/// Preview rectangle bounds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreviewBounds {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

/// Docking preview configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockingPreviewConfig {
    /// Preview overlay opacity
    pub overlay_opacity: f64,
    /// Preview border thickness
    pub border_thickness: f64,
    /// Zone detection threshold (pixels from edge)
    pub zone_threshold: f64,
    /// Animation duration for preview transitions
    pub animation_duration: u32,
    /// Enable drop shadows
    pub enable_shadows: bool,
    /// Preview colors for different zones
    pub zone_colors: HashMap<DockZone, (u8, u8, u8, u8)>, // RGBA
}

impl Default for DockingPreviewConfig {
    fn default() -> Self {
        let mut zone_colors = HashMap::new();
        zone_colors.insert(DockZone::Top, (59, 130, 246, 128)); // Blue
        zone_colors.insert(DockZone::Bottom, (59, 130, 246, 128)); // Blue
        zone_colors.insert(DockZone::Left, (59, 130, 246, 128)); // Blue
        zone_colors.insert(DockZone::Right, (59, 130, 246, 128)); // Blue
        zone_colors.insert(DockZone::Center, (34, 197, 94, 128)); // Green
        zone_colors.insert(DockZone::SplitHorizontal, (168, 85, 247, 128)); // Purple
        zone_colors.insert(DockZone::SplitVertical, (168, 85, 247, 128)); // Purple

        Self {
            overlay_opacity: 0.7,
            border_thickness: 2.0,
            zone_threshold: 50.0,
            animation_duration: 200,
            enable_shadows: true,
            zone_colors,
        }
    }
}

impl Default for DockingPreviewState {
    fn default() -> Self {
        Self {
            active: false,
            zone: None,
            target_area: None,
            target_group: None,
            preview_bounds: None,
            mouse_position: (0.0, 0.0),
            dragged_panel: None,
        }
    }
}

/// Docking preview system
pub struct DockingPreview {
    /// Unique identifier
    id: Uuid,
    /// Configuration
    config: DockingPreviewConfig,
    /// Current state (reactive)
    state: RwSignal<DockingPreviewState>,
    /// Container bounds for zone detection
    container_bounds: RwSignal<HashMap<DockAreaId, PreviewBounds>>,
}

impl DockingPreview {
    /// Create a new docking preview system
    pub fn new() -> Self {
        Self {
            id: Uuid::new_v4(),
            config: DockingPreviewConfig::default(),
            state: create_rw_signal(DockingPreviewState::default()),
            container_bounds: create_rw_signal(HashMap::new()),
        }
    }

    /// Create with custom configuration
    pub fn with_config(config: DockingPreviewConfig) -> Self {
        Self {
            id: Uuid::new_v4(),
            config,
            state: create_rw_signal(DockingPreviewState::default()),
            container_bounds: create_rw_signal(HashMap::new()),
        }
    }

    /// Start docking preview for a panel
    pub fn start_preview(&self, panel_id: &str, mouse_pos: (f64, f64)) -> Result<(), UiError> {
        self.state.update(|state| {
            state.active = true;
            state.dragged_panel = Some(panel_id.to_string());
            state.mouse_position = mouse_pos;
            state.zone = None;
            state.target_area = None;
            state.target_group = None;
            state.preview_bounds = None;
        });

        Ok(())
    }

    /// Update preview based on mouse position
    pub fn update_preview(&self, mouse_pos: (f64, f64)) -> Result<(), UiError> {
        if !self.state.get().active {
            return Ok(());
        }

        let (zone, target_area, bounds) = self.detect_dock_zone(mouse_pos)?;

        self.state.update(|state| {
            state.mouse_position = mouse_pos;
            state.zone = zone.clone();
            state.target_area = target_area.clone();
            state.preview_bounds = bounds;
        });

        Ok(())
    }

    /// End docking preview
    pub fn end_preview(&self) -> Result<Option<DockingResult>, UiError> {
        let current_state = self.state.get();
        
        let result = if current_state.active && current_state.zone.is_some() {
            Some(DockingResult {
                panel_id: current_state.dragged_panel.clone().unwrap_or_default(),
                target_zone: current_state.zone.clone().unwrap(),
                target_area: current_state.target_area.clone(),
                target_group: current_state.target_group.clone(),
            })
        } else {
            None
        };

        self.state.update(|state| {
            *state = DockingPreviewState::default();
        });

        Ok(result)
    }

    /// Cancel docking preview
    pub fn cancel_preview(&self) {
        self.state.update(|state| {
            *state = DockingPreviewState::default();
        });
    }

    /// Update container bounds for zone detection
    pub fn update_container_bounds(&self, area_id: &str, bounds: PreviewBounds) {
        self.container_bounds.update(|bounds_map| {
            bounds_map.insert(area_id.to_string(), bounds);
        });
    }

    /// Detect dock zone based on mouse position
    fn detect_dock_zone(&self, mouse_pos: (f64, f64)) -> Result<(Option<DockZone>, Option<DockArea>, Option<PreviewBounds>), UiError> {
        let container_bounds = self.container_bounds.get();
        let threshold = self.config.zone_threshold;

        for (area_id, bounds) in container_bounds.iter() {
            if self.point_in_bounds(mouse_pos, bounds) {
                let dock_area = self.area_id_to_dock_area(area_id)?;
                let (zone, preview_bounds) = self.calculate_zone_and_bounds(mouse_pos, bounds, threshold);
                
                return Ok((Some(zone), Some(dock_area), Some(preview_bounds)));
            }
        }

        Ok((None, None, None))
    }

    /// Check if point is within bounds
    fn point_in_bounds(&self, point: (f64, f64), bounds: &PreviewBounds) -> bool {
        point.0 >= bounds.x && 
        point.0 <= bounds.x + bounds.width &&
        point.1 >= bounds.y && 
        point.1 <= bounds.y + bounds.height
    }

    /// Calculate dock zone and preview bounds based on mouse position
    fn calculate_zone_and_bounds(&self, mouse_pos: (f64, f64), container_bounds: &PreviewBounds, threshold: f64) -> (DockZone, PreviewBounds) {
        let (mx, my) = mouse_pos;
        let bounds = container_bounds;

        // Calculate distances from edges
        let dist_left = mx - bounds.x;
        let dist_right = (bounds.x + bounds.width) - mx;
        let dist_top = my - bounds.y;
        let dist_bottom = (bounds.y + bounds.height) - my;

        // Find closest edge
        let min_dist = dist_left.min(dist_right).min(dist_top).min(dist_bottom);

        if min_dist > threshold {
            // Center zone - add as tab
            (DockZone::Center, PreviewBounds {
                x: bounds.x + 10.0,
                y: bounds.y + 10.0,
                width: bounds.width - 20.0,
                height: bounds.height - 20.0,
            })
        } else if min_dist == dist_left {
            // Left edge - dock left or split vertically
            if dist_left < threshold / 2.0 {
                (DockZone::Left, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width / 2.0,
                    height: bounds.height,
                })
            } else {
                (DockZone::SplitVertical, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width / 3.0,
                    height: bounds.height,
                })
            }
        } else if min_dist == dist_right {
            // Right edge - dock right or split vertically
            if dist_right < threshold / 2.0 {
                (DockZone::Right, PreviewBounds {
                    x: bounds.x + bounds.width / 2.0,
                    y: bounds.y,
                    width: bounds.width / 2.0,
                    height: bounds.height,
                })
            } else {
                (DockZone::SplitVertical, PreviewBounds {
                    x: bounds.x + bounds.width * 2.0 / 3.0,
                    y: bounds.y,
                    width: bounds.width / 3.0,
                    height: bounds.height,
                })
            }
        } else if min_dist == dist_top {
            // Top edge - dock top or split horizontally
            if dist_top < threshold / 2.0 {
                (DockZone::Top, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height / 2.0,
                })
            } else {
                (DockZone::SplitHorizontal, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height / 3.0,
                })
            }
        } else {
            // Bottom edge - dock bottom or split horizontally
            if dist_bottom < threshold / 2.0 {
                (DockZone::Bottom, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y + bounds.height / 2.0,
                    width: bounds.width,
                    height: bounds.height / 2.0,
                })
            } else {
                (DockZone::SplitHorizontal, PreviewBounds {
                    x: bounds.x,
                    y: bounds.y + bounds.height * 2.0 / 3.0,
                    width: bounds.width,
                    height: bounds.height / 3.0,
                })
            }
        }
    }

    /// Convert area ID to dock area
    fn area_id_to_dock_area(&self, area_id: &str) -> Result<DockArea, UiError> {
        match area_id {
            "dock_top" => Ok(DockArea::Top),
            "dock_bottom" => Ok(DockArea::Bottom),
            "dock_left" => Ok(DockArea::Left),
            "dock_right" => Ok(DockArea::Right),
            "dock_center" => Ok(DockArea::Center),
            _ => Err(UiError::PanelError(format!("Unknown dock area: {}", area_id))),
        }
    }

    /// Get current preview state
    pub fn get_state(&self) -> DockingPreviewState {
        self.state.get()
    }

    /// Check if preview is active
    pub fn is_active(&self) -> bool {
        self.state.get().active
    }

    /// Create the preview overlay view
    pub fn create_preview_overlay(&self) -> impl IntoView {
        let state = self.state;
        let config = self.config.clone();

        container(empty())
            .style(move |s| {
                let current_state = state.get();
                
                if !current_state.active || current_state.preview_bounds.is_none() {
                    return s.display(Display::None);
                }

                let bounds = current_state.preview_bounds.as_ref().unwrap();
                let zone = current_state.zone.as_ref().unwrap();
                
                let color = config.zone_colors.get(zone)
                    .copied()
                    .unwrap_or((59, 130, 246, 128)); // Default blue

                s.position(Position::Absolute)
                    .inset_left(bounds.x)
                    .inset_top(bounds.y)
                    .width(bounds.width)
                    .height(bounds.height)
                    .background(floem::peniko::Color::rgba8(color.0, color.1, color.2, color.3))
                    .border(config.border_thickness)
                    .border_color(floem::peniko::Color::rgba8(color.0, color.1, color.2, 255))
                    .border_radius(4.0)
                    .z_index(1000)
                    .apply_if(config.enable_shadows, |s| {
                        s.box_shadow_blur(8.0)
                            .box_shadow_color(floem::peniko::Color::rgba8(0, 0, 0, 64))
                    })
            })
    }

    /// Update configuration
    pub fn update_config(&mut self, config: DockingPreviewConfig) {
        self.config = config;
    }

    /// Get configuration
    pub fn get_config(&self) -> &DockingPreviewConfig {
        &self.config
    }
}

/// Result of a docking operation
#[derive(Debug, Clone)]
pub struct DockingResult {
    pub panel_id: String,
    pub target_zone: DockZone,
    pub target_area: Option<DockArea>,
    pub target_group: Option<TabGroupId>,
}

impl Default for DockingPreview {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_docking_preview_creation() {
        let preview = DockingPreview::new();
        assert!(!preview.is_active());
    }

    #[test]
    fn test_start_and_end_preview() {
        let preview = DockingPreview::new();
        
        // Start preview
        preview.start_preview("test_panel", (100.0, 100.0)).unwrap();
        assert!(preview.is_active());
        
        let state = preview.get_state();
        assert_eq!(state.dragged_panel, Some("test_panel".to_string()));
        assert_eq!(state.mouse_position, (100.0, 100.0));
        
        // End preview
        let result = preview.end_preview().unwrap();
        assert!(!preview.is_active());
        assert!(result.is_none()); // No valid dock zone detected
    }

    #[test]
    fn test_zone_detection() {
        let preview = DockingPreview::new();
        
        // Add container bounds
        let bounds = PreviewBounds {
            x: 0.0,
            y: 0.0,
            width: 400.0,
            height: 300.0,
        };
        preview.update_container_bounds("dock_center", bounds);
        
        // Test left edge detection
        let (zone, area, preview_bounds) = preview.detect_dock_zone((10.0, 150.0)).unwrap();
        assert_eq!(zone, Some(DockZone::Left));
        assert_eq!(area, Some(DockArea::Center));
        assert!(preview_bounds.is_some());
        
        // Test center detection
        let (zone, area, preview_bounds) = preview.detect_dock_zone((200.0, 150.0)).unwrap();
        assert_eq!(zone, Some(DockZone::Center));
        assert_eq!(area, Some(DockArea::Center));
        assert!(preview_bounds.is_some());
    }

    #[test]
    fn test_point_in_bounds() {
        let preview = DockingPreview::new();
        let bounds = PreviewBounds {
            x: 10.0,
            y: 10.0,
            width: 100.0,
            height: 100.0,
        };
        
        assert!(preview.point_in_bounds((50.0, 50.0), &bounds));
        assert!(preview.point_in_bounds((10.0, 10.0), &bounds)); // Edge case
        assert!(preview.point_in_bounds((110.0, 110.0), &bounds)); // Edge case
        assert!(!preview.point_in_bounds((5.0, 50.0), &bounds)); // Outside left
        assert!(!preview.point_in_bounds((50.0, 5.0), &bounds)); // Outside top
        assert!(!preview.point_in_bounds((115.0, 50.0), &bounds)); // Outside right
        assert!(!preview.point_in_bounds((50.0, 115.0), &bounds)); // Outside bottom
    }

    #[test]
    fn test_cancel_preview() {
        let preview = DockingPreview::new();
        
        preview.start_preview("test_panel", (100.0, 100.0)).unwrap();
        assert!(preview.is_active());
        
        preview.cancel_preview();
        assert!(!preview.is_active());
        
        let state = preview.get_state();
        assert_eq!(state.dragged_panel, None);
    }
}