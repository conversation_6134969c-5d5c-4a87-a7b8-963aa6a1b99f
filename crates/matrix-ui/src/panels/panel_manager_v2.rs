//! Enhanced Panel Manager for MATRIX IDE
//!
//! This module implements the enhanced panel management system with Floem 0.2 reactive signals,
//! dynamic panel registration, and comprehensive dock container support.

use std::collections::HashMap;
use floem::reactive::{RwSignal, ReadSignal, WriteSignal, create_rw_signal, SignalGet, SignalUpdate, SignalWith};
use floem::views::{container, v_stack, h_stack, Decorators};
use floem::{View, IntoView};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use crate::theme::ThemeManager;
use super::dock_container::{DockContainer, DockArea, DockAreaId, TabGroupId, TabId};
use std::sync::Arc;

/// Unique identifier for panels
pub type PanelId = String;

/// Panel content trait for dynamic content
pub trait PanelContent: Send + Sync {
    /// Get the panel's unique identifier
    fn id(&self) -> &str;
    
    /// Get the panel's display title
    fn title(&self) -> &str;
    
    /// Create the view for this panel - returns a string representation for now
    fn create_view_content(&self) -> String;
    
    /// Handle panel updates
    fn update(&mut self) -> Result<(), UiError> {
        Ok(())
    }
    
    /// Handle panel close event
    fn on_close(&mut self) -> Result<(), UiError> {
        Ok(())
    }
    
    /// Handle panel resize event
    fn on_resize(&mut self, width: f64, height: f64) -> Result<(), UiError> {
        let _ = (width, height);
        Ok(())
    }
    
    /// Check if panel has unsaved changes
    fn has_unsaved_changes(&self) -> bool {
        false
    }
    
    /// Get panel icon (optional)
    fn icon(&self) -> Option<&str> {
        None
    }
}

/// Panel state with reactive signals
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelState {
    pub id: PanelId,
    pub title: String,
    pub dock_area: DockArea,
    pub tab_group: Option<TabGroupId>,
    pub visible: bool,
    pub active: bool,
    pub size: (f64, f64),
    pub position: (f64, f64),
    pub has_changes: bool,
    pub has_errors: bool,
}

/// Panel preferences for persistence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelPreferences {
    pub preferred_dock_area: DockArea,
    pub preferred_size: (f64, f64),
    pub auto_hide: bool,
    pub pin_to_dock: bool,
    pub show_in_tab_bar: bool,
}

impl Default for PanelPreferences {
    fn default() -> Self {
        Self {
            preferred_dock_area: DockArea::Bottom,
            preferred_size: (300.0, 200.0),
            auto_hide: false,
            pin_to_dock: true,
            show_in_tab_bar: true,
        }
    }
}

/// Panel layout configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelLayout {
    pub dock_areas: HashMap<DockArea, DockAreaConfig>,
    pub panel_states: HashMap<PanelId, PanelState>,
    pub active_panels: Vec<PanelId>,
    pub layout_version: String,
}

/// Configuration for dock areas
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockAreaConfig {
    pub area: DockArea,
    pub visible: bool,
    pub size: (f64, f64),
    pub min_size: (f64, f64),
    pub max_size: Option<(f64, f64)>,
    pub tab_groups: Vec<TabGroupId>,
}

impl Default for DockAreaConfig {
    fn default() -> Self {
        Self {
            area: DockArea::Bottom,
            visible: true,
            size: (300.0, 200.0),
            min_size: (100.0, 100.0),
            max_size: None,
            tab_groups: Vec::new(),
        }
    }
}

/// Enhanced Panel Manager with Floem 0.2 reactive system
pub struct PanelManagerV2 {
    /// Registered panel content
    panels: RwSignal<HashMap<PanelId, Box<dyn PanelContent>>>,
    
    /// Panel states with reactive signals
    panel_states: RwSignal<HashMap<PanelId, RwSignal<PanelState>>>,
    
    /// Dock containers for each area
    dock_containers: RwSignal<HashMap<DockArea, DockContainer>>,
    
    /// Current layout configuration
    layout: RwSignal<PanelLayout>,
    
    /// Theme manager reference
    theme_manager: Arc<ThemeManager>,
    
    /// Configuration file path for persistence
    config_path: Option<std::path::PathBuf>,
    
    /// Event handlers
    event_handlers: RwSignal<HashMap<String, Box<dyn Fn(&str, &str) -> Result<(), UiError> + Send + Sync>>>,
}

impl PanelManagerV2 {
    /// Create a new enhanced panel manager
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        let default_layout = PanelLayout {
            dock_areas: Self::create_default_dock_areas(),
            panel_states: HashMap::new(),
            active_panels: Vec::new(),
            layout_version: "1.0".to_string(),
        };

        let dock_containers = Self::create_default_dock_containers();

        Self {
            panels: create_rw_signal(HashMap::new()),
            panel_states: create_rw_signal(HashMap::new()),
            dock_containers: create_rw_signal(dock_containers),
            layout: create_rw_signal(default_layout),
            theme_manager,
            config_path: None,
            event_handlers: create_rw_signal(HashMap::new()),
        }
    }

    /// Create default dock area configurations
    fn create_default_dock_areas() -> HashMap<DockArea, DockAreaConfig> {
        let mut areas = HashMap::new();
        
        areas.insert(DockArea::Top, DockAreaConfig {
            area: DockArea::Top,
            size: (800.0, 100.0),
            min_size: (200.0, 50.0),
            ..Default::default()
        });
        
        areas.insert(DockArea::Bottom, DockAreaConfig {
            area: DockArea::Bottom,
            size: (800.0, 200.0),
            min_size: (200.0, 100.0),
            ..Default::default()
        });
        
        areas.insert(DockArea::Left, DockAreaConfig {
            area: DockArea::Left,
            size: (250.0, 600.0),
            min_size: (150.0, 200.0),
            ..Default::default()
        });
        
        areas.insert(DockArea::Right, DockAreaConfig {
            area: DockArea::Right,
            size: (300.0, 600.0),
            min_size: (200.0, 200.0),
            ..Default::default()
        });
        
        areas.insert(DockArea::Center, DockAreaConfig {
            area: DockArea::Center,
            size: (600.0, 400.0),
            min_size: (400.0, 300.0),
            ..Default::default()
        });
        
        areas
    }

    /// Create default dock containers
    fn create_default_dock_containers() -> HashMap<DockArea, DockContainer> {
        let mut containers = HashMap::new();
        
        for area in [DockArea::Top, DockArea::Bottom, DockArea::Left, DockArea::Right, DockArea::Center] {
            containers.insert(area.clone(), DockContainer::new(area));
        }
        
        containers
    }

    /// Register a new panel with dynamic content
    pub fn register_panel(&self, panel: Box<dyn PanelContent>) -> Result<(), UiError> {
        let panel_id = panel.id().to_string();
        
        // Check if panel already exists
        let panel_exists = self.panels.with_untracked(|panels| panels.contains_key(&panel_id));
        if panel_exists {
            return Err(UiError::PanelError(format!("Panel {} already registered", panel_id)));
        }

        // Create initial panel state
        let initial_state = PanelState {
            id: panel_id.clone(),
            title: panel.title().to_string(),
            dock_area: DockArea::Bottom, // Default dock area
            tab_group: None,
            visible: true,
            active: false,
            size: (300.0, 200.0),
            position: (0.0, 0.0),
            has_changes: false,
            has_errors: false,
        };

        // Add panel to collection
        self.panels.update(|panels| {
            panels.insert(panel_id.clone(), panel);
        });

        // Add panel state
        self.panel_states.update(|states| {
            states.insert(panel_id.clone(), create_rw_signal(initial_state.clone()));
        });

        // Update layout
        self.layout.update(|layout| {
            layout.panel_states.insert(panel_id.clone(), initial_state);
            layout.active_panels.push(panel_id.clone());
        });

        // Add panel to default dock area
        self.add_panel_to_dock(&panel_id, &DockArea::Bottom)?;

        Ok(())
    }

    /// Unregister a panel
    pub fn unregister_panel(&self, panel_id: &str) -> Result<(), UiError> {
        // Remove from dock containers first
        self.remove_panel_from_dock(panel_id)?;

        // Remove panel
        self.panels.update(|panels| {
            panels.remove(panel_id);
        });

        // Remove panel state
        self.panel_states.update(|states| {
            states.remove(panel_id);
        });

        // Update layout
        self.layout.update(|layout| {
            layout.panel_states.remove(panel_id);
            layout.active_panels.retain(|id| id != panel_id);
        });

        Ok(())
    }

    /// Add a panel to a specific dock area
    pub fn add_panel_to_dock(&self, panel_id: &str, dock_area: &DockArea) -> Result<(), UiError> {
        // Get or create tab group in the dock area
        let tab_group_id = self.dock_containers.with_untracked(|containers| {
            if let Some(container) = containers.get(dock_area) {
                let groups = container.get_tab_groups();
                if groups.is_empty() {
                    container.add_tab_group()
                } else {
                    Ok(groups.keys().next().unwrap().clone())
                }
            } else {
                Err(UiError::PanelError(format!("Dock area {:?} not found", dock_area)))
            }
        })?;

        // Update panel state
        if let Some(panel_state_signal) = self.panel_states.get().get(panel_id) {
            panel_state_signal.update(|state| {
                state.dock_area = dock_area.clone();
                state.tab_group = Some(tab_group_id.clone());
            });
        }

        // Add tab to the tab group
        self.add_tab_to_group(&tab_group_id, panel_id)?;

        Ok(())
    }

    /// Remove a panel from its current dock
    pub fn remove_panel_from_dock(&self, panel_id: &str) -> Result<(), UiError> {
        // Get current panel state
        let (_dock_area, tab_group_id) = self.panel_states.with_untracked(|states| {
            if let Some(panel_state_signal) = states.get(panel_id) {
                let state = panel_state_signal.get();
                (state.dock_area, state.tab_group.clone())
            } else {
                (DockArea::Bottom, None) // Default values for error case
            }
        });

        // Remove from tab group if assigned
        if let Some(group_id) = tab_group_id {
            self.remove_tab_from_group(&group_id, panel_id)?;
        }

        // Update panel state
        if let Some(panel_state_signal) = self.panel_states.get().get(panel_id) {
            panel_state_signal.update(|state| {
                state.tab_group = None;
            });
        }

        Ok(())
    }

    /// Add a tab to a tab group
    fn add_tab_to_group(&self, group_id: &str, panel_id: &str) -> Result<(), UiError> {
        self.dock_containers.with_untracked(|containers| {
            for container in containers.values() {
                if let Some(group) = container.get_tab_group(group_id) {
                    group.tabs.update(|tabs| {
                        if !tabs.contains(&panel_id.to_string()) {
                            tabs.push(panel_id.to_string());
                        }
                    });
                    
                    // Set as active tab if it's the first one
                    if group.active_tab.get().is_none() {
                        group.active_tab.set(Some(panel_id.to_string()));
                    }
                    
                    return Ok(());
                }
            }
            Err(UiError::PanelError(format!("Tab group {} not found", group_id)))
        })
    }

    /// Remove a tab from a tab group
    fn remove_tab_from_group(&self, group_id: &str, panel_id: &str) -> Result<(), UiError> {
        self.dock_containers.with_untracked(|containers| {
            for container in containers.values() {
                if let Some(group) = container.get_tab_group(group_id) {
                    group.tabs.update(|tabs| {
                        tabs.retain(|id| id != panel_id);
                    });
                    
                    // Update active tab if this was the active one
                    if group.active_tab.get().as_ref() == Some(&panel_id.to_string()) {
                        let new_active = group.tabs.get().first().cloned();
                        group.active_tab.set(new_active);
                    }
                    
                    return Ok(());
                }
            }
            Err(UiError::PanelError(format!("Tab group {} not found", group_id)))
        })
    }

    /// Show a panel
    pub fn show_panel(&self, panel_id: &str) -> Result<(), UiError> {
        if let Some(panel_state_signal) = self.panel_states.get().get(panel_id) {
            panel_state_signal.update(|state| {
                state.visible = true;
            });
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }

    /// Hide a panel
    pub fn hide_panel(&self, panel_id: &str) -> Result<(), UiError> {
        if let Some(panel_state_signal) = self.panel_states.get().get(panel_id) {
            panel_state_signal.update(|state| {
                state.visible = false;
            });
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }

    /// Set active panel
    pub fn set_active_panel(&self, panel_id: &str) -> Result<(), UiError> {
        // Deactivate all panels first
        self.panel_states.with_untracked(|states| {
            for (_, state_signal) in states.iter() {
                state_signal.update(|state| {
                    state.active = false;
                });
            }
        });

        // Activate the specified panel
        let panel_found = self.panel_states.with_untracked(|states| {
            if let Some(panel_state_signal) = states.get(panel_id) {
                panel_state_signal.update(|state| {
                    state.active = true;
                });
                true
            } else {
                false
            }
        });

        if panel_found {
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }

    /// Get dock container for an area
    pub fn get_dock_container(&self, area: &DockArea) -> Option<DockContainer> {
        self.dock_containers.with_untracked(|containers| containers.get(area).cloned())
    }

    /// Get all registered panel IDs
    pub fn get_panel_ids(&self) -> Vec<String> {
        self.panels.with_untracked(|panels| panels.keys().cloned().collect())
    }

    /// Get panel state signal
    pub fn get_panel_state(&self, panel_id: &str) -> Option<RwSignal<PanelState>> {
        self.panel_states.with_untracked(|states| states.get(panel_id).cloned())
    }

    /// Update panel state
    pub fn update_panel_state<F>(&self, panel_id: &str, updater: F) -> Result<(), UiError>
    where
        F: FnOnce(&mut PanelState),
    {
        let panel_found = self.panel_states.with_untracked(|states| {
            if let Some(panel_state_signal) = states.get(panel_id) {
                panel_state_signal.update(updater);
                true
            } else {
                false
            }
        });

        if panel_found {
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }

    /// Save current layout configuration
    pub fn save_layout(&self) -> Result<(), UiError> {
        if let Some(config_path) = &self.config_path {
            let layout = self.layout.get();
            let config_data = serde_json::to_string_pretty(&layout)
                .map_err(|e| UiError::SerializationError(e.to_string()))?;
            
            std::fs::write(config_path, config_data)
                .map_err(|e| UiError::IoError(e.to_string()))?;
        }
        Ok(())
    }

    /// Load layout configuration
    pub fn load_layout(&self) -> Result<(), UiError> {
        if let Some(config_path) = &self.config_path {
            if config_path.exists() {
                let config_data = std::fs::read_to_string(config_path)
                    .map_err(|e| UiError::IoError(e.to_string()))?;
                
                let layout: PanelLayout = serde_json::from_str(&config_data)
                    .map_err(|e| UiError::SerializationError(e.to_string()))?;
                
                self.layout.set(layout);
            }
        }
        Ok(())
    }

    /// Create the main view for all dock containers
    pub fn create_view(&self) -> impl IntoView {
        container(
            v_stack((
                // Top dock area
                container(floem::views::label(|| "Top Dock Area".to_string()))
                    .style(|s| s.height(100.0).background(floem::peniko::Color::rgb8(60, 60, 60))),
                
                // Main content area (Left, Center, Right)
                container(
                    h_stack((
                        // Left dock area
                        container(floem::views::label(|| "Left Dock".to_string()))
                            .style(|s| s.width(250.0).background(floem::peniko::Color::rgb8(50, 50, 50))),
                        
                        // Center dock area
                        container(floem::views::label(|| "Center Dock".to_string()))
                            .style(|s| s.flex_grow(1.0).background(floem::peniko::Color::rgb8(40, 40, 40))),
                        
                        // Right dock area
                        container(floem::views::label(|| "Right Dock".to_string()))
                            .style(|s| s.width(300.0).background(floem::peniko::Color::rgb8(50, 50, 50))),
                    ))
                )
                .style(|s| s.flex_grow(1.0)),
                
                // Bottom dock area
                container(floem::views::label(|| "Bottom Dock Area".to_string()))
                    .style(|s| s.height(200.0).background(floem::peniko::Color::rgb8(60, 60, 60))),
            ))
        )
        .style(|s| s.size_full())
    }
}

impl Default for PanelLayout {
    fn default() -> Self {
        Self {
            dock_areas: PanelManagerV2::create_default_dock_areas(),
            panel_states: HashMap::new(),
            active_panels: Vec::new(),
            layout_version: "1.0".to_string(),
        }
    }
}