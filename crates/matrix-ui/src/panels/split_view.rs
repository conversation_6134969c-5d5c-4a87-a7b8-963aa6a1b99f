//! Split View System for MATRIX IDE
//!
//! This module implements horizontal and vertical split view functionality
//! for organizing panels and content areas with dynamic resizing.

use std::collections::HashMap;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate, SignalWith};
use floem::views::{container, v_stack, h_stack, empty, Decorators};
use floem::{View, IntoView};
use floem::style::{Position, Display};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use crate::layout::splitter::{Splitter, SplitterDirection, SplitterConfig};
use super::dock_container::{DockContainer, DockArea};

/// Split direction for organizing content
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SplitDirection {
    /// Horizontal split (side by side)
    Horizontal,
    /// Vertical split (top and bottom)
    Vertical,
}

/// Split view configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SplitViewConfig {
    /// Minimum size for split panes
    pub min_pane_size: f64,
    /// Default split ratio (0.0 to 1.0)
    pub default_ratio: f64,
    /// Enable splitter snapping
    pub enable_snapping: bool,
    /// Snap threshold in pixels
    pub snap_threshold: f64,
    /// Animation duration for split operations
    pub animation_duration: u32,
    /// Enable automatic space redistribution
    pub auto_redistribute: bool,
}

impl Default for SplitViewConfig {
    fn default() -> Self {
        Self {
            min_pane_size: 100.0,
            default_ratio: 0.5,
            enable_snapping: true,
            snap_threshold: 20.0,
            animation_duration: 200,
            auto_redistribute: true,
        }
    }
}

/// Split pane state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SplitPaneState {
    /// Pane identifier
    pub id: String,
    /// Current size (width or height depending on split direction)
    pub size: f64,
    /// Minimum size
    pub min_size: f64,
    /// Maximum size (optional)
    pub max_size: Option<f64>,
    /// Is pane visible
    pub visible: bool,
    /// Is pane collapsed
    pub collapsed: bool,
    /// Content type
    pub content_type: SplitPaneContent,
}

/// Content type for split panes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SplitPaneContent {
    /// Contains a dock container
    DockContainer(DockArea),
    /// Contains another split view (nested)
    SplitView(String),
    /// Empty pane
    Empty,
    /// Custom content
    Custom(String),
}

/// Split view state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SplitViewState {
    /// Split direction
    pub direction: SplitDirection,
    /// Current split ratio (0.0 to 1.0)
    pub ratio: f64,
    /// Total available size
    pub total_size: f64,
    /// Left/top pane state
    pub first_pane: SplitPaneState,
    /// Right/bottom pane state
    pub second_pane: SplitPaneState,
    /// Is splitter being dragged
    pub is_dragging: bool,
    /// Last update timestamp
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// Split view component
#[derive(Clone)]
pub struct SplitView {
    /// Unique identifier
    id: String,
    /// Configuration
    config: SplitViewConfig,
    /// Current state (reactive)
    state: RwSignal<SplitViewState>,
    /// Splitter thickness
    splitter_thickness: f64,
    /// Child split views (for nesting)
    child_splits: RwSignal<HashMap<String, SplitView>>,
    /// Associated dock containers
    dock_containers: RwSignal<HashMap<DockArea, DockContainer>>,
}

impl SplitView {
    /// Create a new split view
    pub fn new(direction: SplitDirection, first_content: SplitPaneContent, second_content: SplitPaneContent) -> Self {
        let id = format!("split_{}", Uuid::new_v4());
        let config = SplitViewConfig::default();
        
        let initial_state = SplitViewState {
            direction,
            ratio: config.default_ratio,
            total_size: 800.0, // Default size
            first_pane: SplitPaneState {
                id: format!("{}_first", id),
                size: 400.0,
                min_size: config.min_pane_size,
                max_size: None,
                visible: true,
                collapsed: false,
                content_type: first_content,
            },
            second_pane: SplitPaneState {
                id: format!("{}_second", id),
                size: 400.0,
                min_size: config.min_pane_size,
                max_size: None,
                visible: true,
                collapsed: false,
                content_type: second_content,
            },
            is_dragging: false,
            last_updated: chrono::Utc::now(),
        };

        Self {
            id,
            config,
            state: create_rw_signal(initial_state),
            splitter_thickness: 4.0,
            child_splits: create_rw_signal(HashMap::new()),
            dock_containers: create_rw_signal(HashMap::new()),
        }
    }

    /// Create with custom configuration
    pub fn with_config(
        direction: SplitDirection,
        first_content: SplitPaneContent,
        second_content: SplitPaneContent,
        config: SplitViewConfig,
    ) -> Self {
        let id = format!("split_{}", Uuid::new_v4());
        
        let initial_state = SplitViewState {
            direction,
            ratio: config.default_ratio,
            total_size: 800.0,
            first_pane: SplitPaneState {
                id: format!("{}_first", id),
                size: 400.0,
                min_size: config.min_pane_size,
                max_size: None,
                visible: true,
                collapsed: false,
                content_type: first_content,
            },
            second_pane: SplitPaneState {
                id: format!("{}_second", id),
                size: 400.0,
                min_size: config.min_pane_size,
                max_size: None,
                visible: true,
                collapsed: false,
                content_type: second_content,
            },
            is_dragging: false,
            last_updated: chrono::Utc::now(),
        };

        Self {
            id,
            config,
            state: create_rw_signal(initial_state),
            splitter_thickness: 4.0,
            child_splits: create_rw_signal(HashMap::new()),
            dock_containers: create_rw_signal(HashMap::new()),
        }
    }

    /// Get split view ID
    pub fn id(&self) -> &str {
        &self.id
    }

    /// Set total size and recalculate pane sizes
    pub fn set_total_size(&self, size: f64) -> Result<(), UiError> {
        if size < self.config.min_pane_size * 2.0 {
            return Err(UiError::LayoutError(format!(
                "Total size {} is too small for minimum pane sizes",
                size
            )));
        }

        self.state.update(|state| {
            state.total_size = size;
            self.recalculate_pane_sizes(state);
            state.last_updated = chrono::Utc::now();
        });

        Ok(())
    }

    /// Set split ratio
    pub fn set_ratio(&self, ratio: f64) -> Result<(), UiError> {
        let clamped_ratio = ratio.max(0.1).min(0.9); // Prevent extreme ratios
        
        self.state.update(|state| {
            state.ratio = clamped_ratio;
            self.recalculate_pane_sizes(state);
            state.last_updated = chrono::Utc::now();
        });

        Ok(())
    }

    /// Recalculate pane sizes based on ratio and constraints
    fn recalculate_pane_sizes(&self, state: &mut SplitViewState) {
        let available_size = state.total_size;
        let splitter_size = self.splitter_thickness;
        let content_size = available_size - splitter_size;

        // Calculate ideal sizes based on ratio
        let first_ideal = content_size * state.ratio;
        let second_ideal = content_size * (1.0 - state.ratio);

        // Apply minimum size constraints
        let first_min = state.first_pane.min_size;
        let second_min = state.second_pane.min_size;

        let first_size = if first_ideal < first_min {
            first_min
        } else if second_ideal < second_min {
            content_size - second_min
        } else {
            first_ideal
        };

        let second_size = content_size - first_size;

        // Apply maximum size constraints if set
        let first_final = if let Some(first_max) = state.first_pane.max_size {
            first_size.min(first_max)
        } else {
            first_size
        };

        let second_final = if let Some(second_max) = state.second_pane.max_size {
            second_size.min(second_max)
        } else {
            second_size
        };

        state.first_pane.size = first_final;
        state.second_pane.size = second_final;

        // Update ratio based on actual sizes
        if content_size > 0.0 {
            state.ratio = first_final / content_size;
        }
    }

    /// Collapse a pane
    pub fn collapse_pane(&self, pane_id: &str) -> Result<(), UiError> {
        let pane_found = self.state.with_untracked(|state| {
            state.first_pane.id == pane_id || state.second_pane.id == pane_id
        });

        if !pane_found {
            return Err(UiError::PanelError(format!("Pane {} not found", pane_id)));
        }

        self.state.update(|state| {
            if state.first_pane.id == pane_id {
                state.first_pane.collapsed = true;
                state.first_pane.visible = false;
                if self.config.auto_redistribute {
                    state.ratio = 0.0;
                    self.recalculate_pane_sizes(state);
                }
            } else if state.second_pane.id == pane_id {
                state.second_pane.collapsed = true;
                state.second_pane.visible = false;
                if self.config.auto_redistribute {
                    state.ratio = 1.0;
                    self.recalculate_pane_sizes(state);
                }
            }
            state.last_updated = chrono::Utc::now();
        });

        Ok(())
    }

    /// Expand a collapsed pane
    pub fn expand_pane(&self, pane_id: &str) -> Result<(), UiError> {
        let pane_found = self.state.with_untracked(|state| {
            state.first_pane.id == pane_id || state.second_pane.id == pane_id
        });

        if !pane_found {
            return Err(UiError::PanelError(format!("Pane {} not found", pane_id)));
        }

        self.state.update(|state| {
            if state.first_pane.id == pane_id {
                state.first_pane.collapsed = false;
                state.first_pane.visible = true;
                if self.config.auto_redistribute {
                    state.ratio = self.config.default_ratio;
                    self.recalculate_pane_sizes(state);
                }
            } else if state.second_pane.id == pane_id {
                state.second_pane.collapsed = false;
                state.second_pane.visible = true;
                if self.config.auto_redistribute {
                    state.ratio = self.config.default_ratio;
                    self.recalculate_pane_sizes(state);
                }
            }
            state.last_updated = chrono::Utc::now();
        });

        Ok(())
    }

    /// Add a child split view
    pub fn add_child_split(&self, child_split: SplitView) {
        let child_id = child_split.id().to_string();
        self.child_splits.update(|splits| {
            splits.insert(child_id, child_split);
        });
    }

    /// Remove a child split view
    pub fn remove_child_split(&self, child_id: &str) -> Option<SplitView> {
        let mut removed = None;
        self.child_splits.update(|splits| {
            removed = splits.remove(child_id);
        });
        removed
    }

    /// Add a dock container
    pub fn add_dock_container(&self, area: DockArea, container: DockContainer) {
        self.dock_containers.update(|containers| {
            containers.insert(area, container);
        });
    }

    /// Get dock container
    pub fn get_dock_container(&self, area: &DockArea) -> Option<DockContainer> {
        self.dock_containers.with_untracked(|containers| containers.get(area).cloned())
    }

    /// Handle space redistribution when a pane is closed
    pub fn redistribute_space(&self, closed_pane_id: &str) -> Result<(), UiError> {
        if !self.config.auto_redistribute {
            return Ok(());
        }

        let current_state = self.state.get();
        
        if current_state.first_pane.id == closed_pane_id {
            // First pane closed, give all space to second pane
            self.set_ratio(0.0)?;
        } else if current_state.second_pane.id == closed_pane_id {
            // Second pane closed, give all space to first pane
            self.set_ratio(1.0)?;
        }

        Ok(())
    }

    /// Get current state
    pub fn get_state(&self) -> SplitViewState {
        self.state.get()
    }

    /// Get state signal for reactive updates
    pub fn get_state_signal(&self) -> RwSignal<SplitViewState> {
        self.state
    }

    /// Create the split view UI
    pub fn create_view(&self) -> impl IntoView {
        let state = self.state;
        let direction = state.get().direction;
        let child_splits = self.child_splits;
        let dock_containers = self.dock_containers;

        container(
            match direction {
                SplitDirection::Horizontal => {
                    h_stack((
                        // First pane
                        container(self.create_pane_content(true))
                            .style(move |s| {
                                let current_state = state.get();
                                let width = if current_state.first_pane.visible {
                                    current_state.first_pane.size
                                } else {
                                    0.0
                                };
                                s.width(width)
                                    .height_full()
                                    .display(if current_state.first_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                        
                        // Splitter
                        container(empty())
                            .style(move |s| {
                                let current_state = state.get();
                                s.width(4.0)
                                    .height_full()
                                    .background(floem::peniko::Color::rgb8(75, 85, 99))
                                    .cursor(floem::style::CursorStyle::ColResize)
                                    .display(if current_state.first_pane.visible && current_state.second_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                        
                        // Second pane
                        container(self.create_pane_content(false))
                            .style(move |s| {
                                let current_state = state.get();
                                let width = if current_state.second_pane.visible {
                                    current_state.second_pane.size
                                } else {
                                    0.0
                                };
                                s.width(width)
                                    .height_full()
                                    .display(if current_state.second_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                    )).into_any()
                }
                SplitDirection::Vertical => {
                    v_stack((
                        // First pane
                        container(self.create_pane_content(true))
                            .style(move |s| {
                                let current_state = state.get();
                                let height = if current_state.first_pane.visible {
                                    current_state.first_pane.size
                                } else {
                                    0.0
                                };
                                s.height(height)
                                    .width_full()
                                    .display(if current_state.first_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                        
                        // Splitter
                        container(empty())
                            .style(move |s| {
                                let current_state = state.get();
                                s.height(4.0)
                                    .width_full()
                                    .background(floem::peniko::Color::rgb8(75, 85, 99))
                                    .cursor(floem::style::CursorStyle::RowResize)
                                    .display(if current_state.first_pane.visible && current_state.second_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                        
                        // Second pane
                        container(self.create_pane_content(false))
                            .style(move |s| {
                                let current_state = state.get();
                                let height = if current_state.second_pane.visible {
                                    current_state.second_pane.size
                                } else {
                                    0.0
                                };
                                s.height(height)
                                    .width_full()
                                    .display(if current_state.second_pane.visible {
                                        Display::Flex
                                    } else {
                                        Display::None
                                    })
                            }),
                    )).into_any()
                }
            }
        )
        .style(|s| s.size_full())
    }

    /// Create content for a pane
    fn create_pane_content(&self, is_first_pane: bool) -> impl IntoView {
        let state = self.state;
        let child_splits = self.child_splits;
        let dock_containers = self.dock_containers;

        container(
            floem::views::label(move || {
                let current_state = state.get();
                let pane = if is_first_pane {
                    &current_state.first_pane
                } else {
                    &current_state.second_pane
                };

                match &pane.content_type {
                    SplitPaneContent::DockContainer(area) => {
                        format!("Dock Container: {:?}", area)
                    }
                    SplitPaneContent::SplitView(split_id) => {
                        format!("Nested Split: {}", split_id)
                    }
                    SplitPaneContent::Empty => "Empty Pane".to_string(),
                    SplitPaneContent::Custom(content) => {
                        format!("Custom: {}", content)
                    }
                }
            })
        )
        .style(|s| {
            s.size_full()
                .background(floem::peniko::Color::rgb8(40, 40, 40))
                .border(1.0)
                .border_color(floem::peniko::Color::rgb8(60, 60, 60))
                .padding(8.0)
        })
    }

    /// Update configuration
    pub fn update_config(&mut self, config: SplitViewConfig) {
        self.config = config;
        
        // Recalculate sizes with new constraints
        self.state.update(|state| {
            state.first_pane.min_size = self.config.min_pane_size;
            state.second_pane.min_size = self.config.min_pane_size;
            self.recalculate_pane_sizes(state);
            state.last_updated = chrono::Utc::now();
        });
    }

    /// Get configuration
    pub fn get_config(&self) -> &SplitViewConfig {
        &self.config
    }
}

/// Split view manager for coordinating multiple split views
pub struct SplitViewManager {
    /// All managed split views
    split_views: RwSignal<HashMap<String, SplitView>>,
    /// Root split view ID
    root_split: RwSignal<Option<String>>,
    /// Configuration
    config: SplitViewConfig,
}

impl SplitViewManager {
    /// Create a new split view manager
    pub fn new() -> Self {
        Self {
            split_views: create_rw_signal(HashMap::new()),
            root_split: create_rw_signal(None),
            config: SplitViewConfig::default(),
        }
    }

    /// Add a split view
    pub fn add_split_view(&self, split_view: SplitView) -> String {
        let id = split_view.id().to_string();
        
        self.split_views.update(|splits| {
            splits.insert(id.clone(), split_view);
        });

        // Set as root if no root exists
        if self.root_split.get().is_none() {
            self.root_split.set(Some(id.clone()));
        }

        id
    }

    /// Remove a split view
    pub fn remove_split_view(&self, id: &str) -> Option<SplitView> {
        let mut removed = None;
        self.split_views.update(|splits| {
            removed = splits.remove(id);
        });
        
        // Update root if removed split was root
        if self.root_split.get().as_ref() == Some(&id.to_string()) {
            let new_root = self.split_views.with_untracked(|splits| {
                splits.keys().next().cloned()
            });
            self.root_split.set(new_root);
        }

        removed
    }

    /// Get split view by ID
    pub fn get_split_view(&self, id: &str) -> Option<SplitView> {
        self.split_views.with_untracked(|splits| splits.get(id).cloned())
    }

    /// Set root split view
    pub fn set_root_split(&self, id: &str) -> Result<(), UiError> {
        let exists = self.split_views.with_untracked(|splits| splits.contains_key(id));
        
        if exists {
            self.root_split.set(Some(id.to_string()));
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Split view {} not found", id)))
        }
    }

    /// Get root split view
    pub fn get_root_split(&self) -> Option<SplitView> {
        if let Some(root_id) = self.root_split.get() {
            self.get_split_view(&root_id)
        } else {
            None
        }
    }

    /// Create the main view
    pub fn create_view(&self) -> impl IntoView {
        let root_split = self.root_split;
        let split_views = self.split_views;

        container(
            floem::views::label(move || {
                if let Some(root_id) = root_split.get() {
                    format!("Root Split View: {}", root_id)
                } else {
                    "No root split view".to_string()
                }
            })
        )
        .style(|s| s.size_full().background(floem::peniko::Color::rgb8(30, 30, 30)))
    }

    /// Handle automatic space redistribution
    pub fn handle_pane_closed(&self, split_id: &str, pane_id: &str) -> Result<(), UiError> {
        if let Some(split_view) = self.get_split_view(split_id) {
            split_view.redistribute_space(pane_id)?;
        }
        Ok(())
    }
}

impl Default for SplitViewManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_split_view_creation() {
        let split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::DockContainer(DockArea::Left),
            SplitPaneContent::DockContainer(DockArea::Right),
        );

        let state = split.get_state();
        assert_eq!(state.direction, SplitDirection::Horizontal);
        assert_eq!(state.ratio, 0.5);
        assert!(state.first_pane.visible);
        assert!(state.second_pane.visible);
    }

    #[test]
    fn test_split_ratio_adjustment() {
        let split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::Empty,
            SplitPaneContent::Empty,
        );

        split.set_total_size(800.0).unwrap();
        split.set_ratio(0.3).unwrap();

        let state = split.get_state();
        assert_eq!(state.ratio, 0.3);
        assert_eq!(state.first_pane.size, 238.8); // (800 - 4) * 0.3
        assert_eq!(state.second_pane.size, 557.2); // (800 - 4) * 0.7
    }

    #[test]
    fn test_pane_collapse_expand() {
        let split = SplitView::new(
            SplitDirection::Vertical,
            SplitPaneContent::Empty,
            SplitPaneContent::Empty,
        );

        let first_pane_id = split.get_state().first_pane.id.clone();
        
        // Collapse first pane
        split.collapse_pane(&first_pane_id).unwrap();
        let state = split.get_state();
        assert!(state.first_pane.collapsed);
        assert!(!state.first_pane.visible);
        assert_eq!(state.ratio, 0.0);

        // Expand first pane
        split.expand_pane(&first_pane_id).unwrap();
        let state = split.get_state();
        assert!(!state.first_pane.collapsed);
        assert!(state.first_pane.visible);
        assert_eq!(state.ratio, 0.5); // Back to default
    }

    #[test]
    fn test_split_view_manager() {
        let manager = SplitViewManager::new();
        
        let split1 = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::Empty,
            SplitPaneContent::Empty,
        );
        let split1_id = split1.id().to_string();
        
        let added_id = manager.add_split_view(split1);
        assert_eq!(added_id, split1_id);
        
        // Should be set as root
        let root = manager.get_root_split();
        assert!(root.is_some());
        assert_eq!(root.unwrap().id(), split1_id);
        
        // Remove split view
        let removed = manager.remove_split_view(&split1_id);
        assert!(removed.is_some());
        assert!(manager.get_root_split().is_none());
    }

    #[test]
    fn test_space_redistribution() {
        let split = SplitView::new(
            SplitDirection::Horizontal,
            SplitPaneContent::Empty,
            SplitPaneContent::Empty,
        );

        let first_pane_id = split.get_state().first_pane.id.clone();
        
        // Redistribute space when first pane is closed
        split.redistribute_space(&first_pane_id).unwrap();
        
        let state = split.get_state();
        assert_eq!(state.ratio, 0.0); // All space to second pane
    }
}