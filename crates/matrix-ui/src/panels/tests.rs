//! Tests for the Panel System
//!
//! This module contains comprehensive tests for panel creation, destruction,
//! state persistence, and dock container functionality.

#[cfg(test)]
mod tests {
    use super::super::{
        panel_manager_v2::{PanelManagerV2, PanelContent, PanelState},
        dock_container::{DockContainer, DockArea},
    };
    use crate::{error::UiError, theme::ThemeManager};
    use floem::{IntoView, views::{container, label}, reactive::SignalGet};
    use std::sync::Arc;
    use tokio_test;

    /// Mock panel content for testing
    struct MockPanelContent {
        id: String,
        title: String,
        has_changes: bool,
        has_errors: bool,
    }

    impl MockPanelContent {
        fn new(id: &str, title: &str) -> Self {
            Self {
                id: id.to_string(),
                title: title.to_string(),
                has_changes: false,
                has_errors: false,
            }
        }

        fn with_changes(mut self) -> Self {
            self.has_changes = true;
            self
        }

        fn with_errors(mut self) -> Self {
            self.has_errors = true;
            self
        }
    }

    impl PanelContent for MockPanelContent {
        fn id(&self) -> &str {
            &self.id
        }

        fn title(&self) -> &str {
            &self.title
        }

        fn create_view_content(&self) -> String {
            format!("Content for {}", self.title)
        }

        fn has_unsaved_changes(&self) -> bool {
            self.has_changes
        }
    }

    fn create_test_panel_manager() -> PanelManagerV2 {
        let theme_manager = Arc::new(ThemeManager::new().unwrap());
        PanelManagerV2::new(theme_manager)
    }

    #[tokio::test]
    async fn test_panel_registration() {
        let manager = create_test_panel_manager();
        
        // Create a mock panel
        let panel = Box::new(MockPanelContent::new("test_panel", "Test Panel"));
        
        // Register the panel
        let result = manager.register_panel(panel);
        assert!(result.is_ok(), "Panel registration should succeed");
        
        // Verify panel is registered
        let panel_ids = manager.get_panel_ids();
        assert!(panel_ids.contains(&"test_panel".to_string()));
        
        // Verify panel state exists
        let panel_state = manager.get_panel_state("test_panel");
        assert!(panel_state.is_some(), "Panel state should exist");
        
        let state = panel_state.unwrap().get();
        assert_eq!(state.id, "test_panel");
        assert_eq!(state.title, "Test Panel");
        assert_eq!(state.dock_area, DockArea::Bottom); // Default dock area
        assert!(state.visible);
    }

    #[tokio::test]
    async fn test_duplicate_panel_registration() {
        let manager = create_test_panel_manager();
        
        // Register first panel
        let panel1 = Box::new(MockPanelContent::new("duplicate", "Panel 1"));
        let result1 = manager.register_panel(panel1);
        assert!(result1.is_ok());
        
        // Try to register panel with same ID
        let panel2 = Box::new(MockPanelContent::new("duplicate", "Panel 2"));
        let result2 = manager.register_panel(panel2);
        assert!(result2.is_err(), "Duplicate panel registration should fail");
        
        if let Err(UiError::PanelError(msg)) = result2 {
            assert!(msg.contains("already registered"));
        } else {
            panic!("Expected PanelError for duplicate registration");
        }
    }

    #[tokio::test]
    async fn test_panel_unregistration() {
        let manager = create_test_panel_manager();
        
        // Register a panel
        let panel = Box::new(MockPanelContent::new("temp_panel", "Temporary Panel"));
        manager.register_panel(panel).unwrap();
        
        // Verify it's registered
        assert!(manager.get_panel_ids().contains(&"temp_panel".to_string()));
        
        // Unregister the panel
        let result = manager.unregister_panel("temp_panel");
        assert!(result.is_ok(), "Panel unregistration should succeed");
        
        // Verify it's no longer registered
        assert!(!manager.get_panel_ids().contains(&"temp_panel".to_string()));
        
        // Verify panel state is removed
        let panel_state = manager.get_panel_state("temp_panel");
        assert!(panel_state.is_none(), "Panel state should be removed");
    }

    #[tokio::test]
    async fn test_panel_visibility() {
        let manager = create_test_panel_manager();
        
        // Register a panel
        let panel = Box::new(MockPanelContent::new("visibility_test", "Visibility Test"));
        manager.register_panel(panel).unwrap();
        
        // Panel should be visible by default
        let state = manager.get_panel_state("visibility_test").unwrap().get();
        assert!(state.visible);
        
        // Hide the panel
        manager.hide_panel("visibility_test").unwrap();
        let state = manager.get_panel_state("visibility_test").unwrap().get();
        assert!(!state.visible);
        
        // Show the panel again
        manager.show_panel("visibility_test").unwrap();
        let state = manager.get_panel_state("visibility_test").unwrap().get();
        assert!(state.visible);
    }

    #[tokio::test]
    async fn test_active_panel_management() {
        let manager = create_test_panel_manager();
        
        // Register multiple panels
        let panel1 = Box::new(MockPanelContent::new("panel1", "Panel 1"));
        let panel2 = Box::new(MockPanelContent::new("panel2", "Panel 2"));
        
        manager.register_panel(panel1).unwrap();
        manager.register_panel(panel2).unwrap();
        
        // No panel should be active initially
        let state1 = manager.get_panel_state("panel1").unwrap().get();
        let state2 = manager.get_panel_state("panel2").unwrap().get();
        assert!(!state1.active);
        assert!(!state2.active);
        
        // Set panel1 as active
        manager.set_active_panel("panel1").unwrap();
        let state1 = manager.get_panel_state("panel1").unwrap().get();
        let state2 = manager.get_panel_state("panel2").unwrap().get();
        assert!(state1.active);
        assert!(!state2.active);
        
        // Set panel2 as active (should deactivate panel1)
        manager.set_active_panel("panel2").unwrap();
        let state1 = manager.get_panel_state("panel1").unwrap().get();
        let state2 = manager.get_panel_state("panel2").unwrap().get();
        assert!(!state1.active);
        assert!(state2.active);
    }

    #[tokio::test]
    async fn test_dock_area_assignment() {
        let manager = create_test_panel_manager();
        
        // Register a panel
        let panel = Box::new(MockPanelContent::new("dock_test", "Dock Test"));
        manager.register_panel(panel).unwrap();
        
        // Panel should be in bottom dock by default
        let state = manager.get_panel_state("dock_test").unwrap().get();
        assert_eq!(state.dock_area, DockArea::Bottom);
        
        // Move to right dock
        manager.add_panel_to_dock("dock_test", &DockArea::Right).unwrap();
        let state = manager.get_panel_state("dock_test").unwrap().get();
        assert_eq!(state.dock_area, DockArea::Right);
        assert!(state.tab_group.is_some());
    }

    #[tokio::test]
    async fn test_panel_state_updates() {
        let manager = create_test_panel_manager();
        
        // Register a panel
        let panel = Box::new(MockPanelContent::new("state_test", "State Test"));
        manager.register_panel(panel).unwrap();
        
        // Update panel state
        manager.update_panel_state("state_test", |state| {
            state.has_changes = true;
            state.has_errors = true;
            state.size = (400.0, 300.0);
        }).unwrap();
        
        // Verify updates
        let state = manager.get_panel_state("state_test").unwrap().get();
        assert!(state.has_changes);
        assert!(state.has_errors);
        assert_eq!(state.size, (400.0, 300.0));
    }

    #[tokio::test]
    async fn test_dock_container_creation() {
        let container = DockContainer::new(DockArea::Bottom);
        
        assert_eq!(container.area(), DockArea::Bottom);
        assert_eq!(container.id(), "dock_bottom");
        assert!(container.is_visible());
        assert_eq!(container.size(), (300.0, 200.0)); // Default size
    }

    #[tokio::test]
    async fn test_dock_container_tab_groups() {
        let container = DockContainer::new(DockArea::Left);
        
        // Add a tab group
        let group_id = container.add_tab_group().unwrap();
        assert!(!group_id.is_empty());
        
        // Verify tab group exists
        let tab_group = container.get_tab_group(&group_id);
        assert!(tab_group.is_some());
        
        // Add another tab group
        let group_id2 = container.add_tab_group().unwrap();
        assert_ne!(group_id, group_id2);
        
        // Verify both groups exist
        let groups = container.get_tab_groups();
        assert_eq!(groups.len(), 2);
        assert!(groups.contains_key(&group_id));
        assert!(groups.contains_key(&group_id2));
    }

    #[tokio::test]
    async fn test_dock_container_resize() {
        let container = DockContainer::new(DockArea::Right);
        
        // Resize within valid bounds
        let result = container.resize(500.0, 400.0);
        assert!(result.is_ok());
        assert_eq!(container.size(), (500.0, 400.0));
        
        // Try to resize below minimum
        let result = container.resize(50.0, 50.0);
        assert!(result.is_err());
        
        if let Err(UiError::LayoutError(msg)) = result {
            assert!(msg.contains("below minimum"));
        } else {
            panic!("Expected LayoutError for size below minimum");
        }
    }

    #[tokio::test]
    async fn test_dock_container_visibility() {
        let container = DockContainer::new(DockArea::Top);
        
        // Should be visible by default
        assert!(container.is_visible());
        
        // Hide container
        container.set_visible(false);
        assert!(!container.is_visible());
        
        // Show container again
        container.set_visible(true);
        assert!(container.is_visible());
    }

    #[tokio::test]
    async fn test_panel_manager_dock_containers() {
        let manager = create_test_panel_manager();
        
        // Verify all default dock containers exist
        for area in [DockArea::Top, DockArea::Bottom, DockArea::Left, DockArea::Right, DockArea::Center] {
            let container = manager.get_dock_container(&area);
            assert!(container.is_some(), "Dock container for {:?} should exist", area);
            
            let container = container.unwrap();
            assert_eq!(container.area(), area);
        }
    }

    #[tokio::test]
    async fn test_multiple_panels_in_same_dock() {
        let manager = create_test_panel_manager();
        
        // Register multiple panels
        let panel1 = Box::new(MockPanelContent::new("multi1", "Multi Panel 1"));
        let panel2 = Box::new(MockPanelContent::new("multi2", "Multi Panel 2"));
        let panel3 = Box::new(MockPanelContent::new("multi3", "Multi Panel 3"));
        
        manager.register_panel(panel1).unwrap();
        manager.register_panel(panel2).unwrap();
        manager.register_panel(panel3).unwrap();
        
        // Move all to the same dock area
        manager.add_panel_to_dock("multi1", &DockArea::Right).unwrap();
        manager.add_panel_to_dock("multi2", &DockArea::Right).unwrap();
        manager.add_panel_to_dock("multi3", &DockArea::Right).unwrap();
        
        // Verify all panels are in the right dock
        for panel_id in ["multi1", "multi2", "multi3"] {
            let state = manager.get_panel_state(panel_id).unwrap().get();
            assert_eq!(state.dock_area, DockArea::Right);
            assert!(state.tab_group.is_some());
        }
        
        // Verify the dock container has the panels
        let container = manager.get_dock_container(&DockArea::Right).unwrap();
        let groups = container.get_tab_groups();
        assert!(!groups.is_empty());
        
        // At least one group should have tabs
        let has_tabs = groups.values().any(|group| !group.tabs.get().is_empty());
        assert!(has_tabs, "At least one tab group should have tabs");
    }

    #[tokio::test]
    async fn test_error_handling_invalid_operations() {
        let manager = create_test_panel_manager();
        
        // Try to show non-existent panel
        let result = manager.show_panel("non_existent");
        assert!(result.is_err());
        
        // Try to hide non-existent panel
        let result = manager.hide_panel("non_existent");
        assert!(result.is_err());
        
        // Try to set non-existent panel as active
        let result = manager.set_active_panel("non_existent");
        assert!(result.is_err());
        
        // Try to update state of non-existent panel
        let result = manager.update_panel_state("non_existent", |_| {});
        assert!(result.is_err());
        
        // Try to unregister non-existent panel
        let result = manager.unregister_panel("non_existent");
        assert!(result.is_ok()); // Should not error, just do nothing
    }

    #[tokio::test]
    async fn test_panel_content_with_changes_and_errors() {
        let manager = create_test_panel_manager();
        
        // Register panel with changes and errors
        let panel = Box::new(
            MockPanelContent::new("status_test", "Status Test")
                .with_changes()
                .with_errors()
        );
        
        manager.register_panel(panel).unwrap();
        
        // The panel content has changes/errors, but the state should be managed separately
        let state = manager.get_panel_state("status_test").unwrap().get();
        assert_eq!(state.id, "status_test");
        
        // Update state to reflect content status
        manager.update_panel_state("status_test", |state| {
            state.has_changes = true;
            state.has_errors = true;
        }).unwrap();
        
        let state = manager.get_panel_state("status_test").unwrap().get();
        assert!(state.has_changes);
        assert!(state.has_errors);
    }
}