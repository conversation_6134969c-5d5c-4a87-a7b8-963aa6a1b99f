//! Dock Container System for MATRIX IDE
//!
//! This module implements the dock container system that supports multiple dock areas
//! (top, bottom, left, right) with Floem 0.2 reactive signals.

use std::collections::HashMap;
use floem::reactive::{RwSignal, ReadSignal, WriteSignal, SignalGet, SignalUpdate};
use floem::views::{container, v_stack, h_stack, Decorators};
use floem::{View, IntoView};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use super::tab_system::{TabSystem, TabState, TabGroupConfig};

/// Unique identifier for dock areas
pub type DockAreaId = String;

/// Unique identifier for tab groups
pub type TabGroupId = String;

/// Dock areas where panels can be placed
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DockArea {
    Top,
    Bottom,
    Left,
    Right,
    Center,
}

impl DockArea {
    pub fn as_str(&self) -> &'static str {
        match self {
            DockArea::Top => "top",
            DockArea::Bottom => "bottom", 
            DockArea::Left => "left",
            DockArea::Right => "right",
            DockArea::Center => "center",
        }
    }
}

/// State of a dock container
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockContainerState {
    pub area: DockArea,
    pub visible: bool,
    pub size: (f64, f64),
    pub tab_groups: Vec<TabGroupId>,
    pub active_tab_group: Option<TabGroupId>,
}

/// Configuration for a dock container
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockContainerConfig {
    pub area: DockArea,
    pub min_size: (f64, f64),
    pub max_size: Option<(f64, f64)>,
    pub default_size: (f64, f64),
    pub resizable: bool,
    pub collapsible: bool,
}

impl Default for DockContainerConfig {
    fn default() -> Self {
        Self {
            area: DockArea::Bottom,
            min_size: (100.0, 100.0),
            max_size: None,
            default_size: (300.0, 200.0),
            resizable: true,
            collapsible: true,
        }
    }
}

/// A dock container that holds tab groups and manages layout
#[derive(Clone)]
pub struct DockContainer {
    id: DockAreaId,
    config: DockContainerConfig,
    state: RwSignal<DockContainerState>,
    tab_groups: RwSignal<HashMap<TabGroupId, TabGroup>>,
    splitters: RwSignal<Vec<Splitter>>,
    layout_tree: RwSignal<LayoutTree>,
    tab_system: TabSystem,
}

/// Tab group within a dock container
#[derive(Debug, Clone)]
pub struct TabGroup {
    pub id: TabGroupId,
    pub tabs: RwSignal<Vec<TabId>>,
    pub active_tab: RwSignal<Option<TabId>>,
    pub position: RwSignal<(f64, f64)>,
    pub size: RwSignal<(f64, f64)>,
}

/// Unique identifier for tabs
pub type TabId = String;

/// Splitter for resizing dock areas
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Splitter {
    pub id: String,
    pub direction: SplitterDirection,
    pub position: f64,
    pub min_position: f64,
    pub max_position: f64,
}

/// Direction of splitter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SplitterDirection {
    Horizontal,
    Vertical,
}

/// Layout tree for organizing dock containers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutTree {
    pub root: LayoutNode,
}

/// Node in the layout tree
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutNode {
    Container {
        area: DockArea,
        size_ratio: f64,
    },
    Split {
        direction: SplitterDirection,
        ratio: f64,
        left: Box<LayoutNode>,
        right: Box<LayoutNode>,
    },
}

impl DockContainer {
    /// Create a new dock container
    pub fn new(area: DockArea) -> Self {
        let id = format!("dock_{}", area.as_str());
        let config = DockContainerConfig {
            area: area.clone(),
            ..Default::default()
        };
        
        let initial_state = DockContainerState {
            area: area.clone(),
            visible: true,
            size: config.default_size,
            tab_groups: Vec::new(),
            active_tab_group: None,
        };

        Self {
            id,
            config,
            state: RwSignal::new(initial_state),
            tab_groups: RwSignal::new(HashMap::new()),
            splitters: RwSignal::new(Vec::new()),
            layout_tree: RwSignal::new(LayoutTree {
                root: LayoutNode::Container {
                    area: area.clone(),
                    size_ratio: 1.0,
                },
            }),
            tab_system: TabSystem::new(),
        }
    }

    /// Create a new dock container with custom configuration
    pub fn with_config(area: DockArea, config: DockContainerConfig) -> Self {
        let id = format!("dock_{}", area.as_str());
        
        let initial_state = DockContainerState {
            area: area.clone(),
            visible: true,
            size: config.default_size,
            tab_groups: Vec::new(),
            active_tab_group: None,
        };

        Self {
            id,
            config: config.clone(),
            state: RwSignal::new(initial_state),
            tab_groups: RwSignal::new(HashMap::new()),
            splitters: RwSignal::new(Vec::new()),
            layout_tree: RwSignal::new(LayoutTree {
                root: LayoutNode::Container {
                    area: area.clone(),
                    size_ratio: 1.0,
                },
            }),
            tab_system: TabSystem::new(),
        }
    }

    /// Get the dock area
    pub fn area(&self) -> DockArea {
        self.state.get().area
    }

    /// Get the container ID
    pub fn id(&self) -> &str {
        &self.id
    }

    /// Add a new tab group to this container
    pub fn add_tab_group(&self) -> Result<TabGroupId, UiError> {
        let group_id = format!("group_{}", Uuid::new_v4());
        
        let tab_group = TabGroup {
            id: group_id.clone(),
            tabs: RwSignal::new(Vec::new()),
            active_tab: RwSignal::new(None),
            position: RwSignal::new((0.0, 0.0)),
            size: RwSignal::new((300.0, 200.0)),
        };

        // Add to tab groups
        self.tab_groups.update(|groups| {
            groups.insert(group_id.clone(), tab_group);
        });

        // Update state
        self.state.update(|state| {
            state.tab_groups.push(group_id.clone());
            if state.active_tab_group.is_none() {
                state.active_tab_group = Some(group_id.clone());
            }
        });

        Ok(group_id)
    }

    /// Remove a tab group from this container
    pub fn remove_tab_group(&self, group_id: &str) -> Result<(), UiError> {
        // Remove from tab groups
        self.tab_groups.update(|groups| {
            groups.remove(group_id);
        });

        // Update state
        self.state.update(|state| {
            state.tab_groups.retain(|id| id != group_id);
            if state.active_tab_group.as_ref() == Some(&group_id.to_string()) {
                state.active_tab_group = state.tab_groups.first().cloned();
            }
        });

        Ok(())
    }

    /// Get a tab group by ID
    pub fn get_tab_group(&self, group_id: &str) -> Option<TabGroup> {
        self.tab_groups.get().get(group_id).cloned()
    }

    /// Set the active tab group
    pub fn set_active_tab_group(&self, group_id: &str) -> Result<(), UiError> {
        if self.tab_groups.get().contains_key(group_id) {
            self.state.update(|state| {
                state.active_tab_group = Some(group_id.to_string());
            });
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Tab group {} not found", group_id)))
        }
    }

    /// Get all tab groups
    pub fn get_tab_groups(&self) -> HashMap<TabGroupId, TabGroup> {
        self.tab_groups.get()
    }

    /// Set container visibility
    pub fn set_visible(&self, visible: bool) {
        self.state.update(|state| {
            state.visible = visible;
        });
    }

    /// Check if container is visible
    pub fn is_visible(&self) -> bool {
        self.state.get().visible
    }

    /// Resize the container
    pub fn resize(&self, width: f64, height: f64) -> Result<(), UiError> {
        let (min_width, min_height) = self.config.min_size;
        
        if width < min_width || height < min_height {
            return Err(UiError::LayoutError(format!(
                "Size ({}, {}) is below minimum ({}, {})",
                width, height, min_width, min_height
            )));
        }

        if let Some((max_width, max_height)) = self.config.max_size {
            if width > max_width || height > max_height {
                return Err(UiError::LayoutError(format!(
                    "Size ({}, {}) exceeds maximum ({}, {})",
                    width, height, max_width, max_height
                )));
            }
        }

        self.state.update(|state| {
            state.size = (width, height);
        });

        Ok(())
    }

    /// Get current size
    pub fn size(&self) -> (f64, f64) {
        self.state.get().size
    }

    /// Get the tab system
    pub fn tab_system(&self) -> &TabSystem {
        &self.tab_system
    }

    /// Create a tab in this container
    pub fn create_tab(&self, tab_state: TabState) -> Result<(), UiError> {
        // Get or create a tab group
        let group_id = if let Some(group_id) = self.state.get().active_tab_group {
            group_id
        } else {
            // Create a new tab group
            let group_config = TabGroupConfig {
                dock_area: self.area(),
                ..Default::default()
            };
            self.tab_system.create_tab_group(group_config)?
        };

        // Create the tab in the group
        self.tab_system.create_tab(&group_id, tab_state)?;
        Ok(())
    }

    /// Close a tab in this container
    pub fn close_tab(&self, tab_id: &str) -> Result<(), UiError> {
        self.tab_system.close_tab(tab_id)
    }

    /// Activate a tab in this container
    pub fn activate_tab(&self, tab_id: &str) -> Result<(), UiError> {
        self.tab_system.activate_tab(tab_id)
    }

    /// Get all tabs in this container
    pub fn get_all_tabs(&self) -> Vec<TabState> {
        let mut all_tabs = Vec::new();
        for group_id in &self.state.get().tab_groups {
            let group_tabs = self.tab_system.get_group_tabs(group_id);
            all_tabs.extend(group_tabs);
        }
        all_tabs
    }

    /// Create the view for this dock container
    pub fn create_view(&self) -> impl IntoView {
        let state = self.state;
        
        container(
            v_stack((
                // Tab groups area
                container({
                    let current_state = state.get();
                    if current_state.tab_groups.is_empty() {
                        floem::views::label(|| "No tabs".to_string()).into_any()
                    } else {
                        // Show the first tab group (simplified for now)
                        if let Some(group_id) = current_state.tab_groups.first() {
                            self.tab_system.create_tab_group_view(group_id).into_any()
                        } else {
                            floem::views::label(|| "No active tab group".to_string()).into_any()
                        }
                    }
                })
                .style(|s| s.flex_grow(1.0)),
            ))
        )
        .style(move |s| {
            let current_state = state.get();
            s.size(current_state.size.0, current_state.size.1)
                .display(if current_state.visible {
                    floem::taffy::Display::Flex
                } else {
                    floem::taffy::Display::None
                })
        })
    }

    /// Create view for a tab group
    fn create_tab_group_view(&self, group: TabGroup) -> impl IntoView {
        let tabs = group.tabs;
        let active_tab = group.active_tab;
        
        container(
            v_stack((
                // Tab bar
                container(
                    floem::views::label(|| "Tab Bar".to_string())
                )
                .style(|s| s.padding(4.0).background(floem::peniko::Color::rgb8(50, 50, 50))),
                
                // Content area
                container(
                    floem::views::label(|| "Panel content will go here".to_string())
                )
                .style(|s| s.flex_grow(1.0).padding(8.0).background(floem::peniko::Color::rgb8(40, 40, 40))),
            ))
        )
        .style(|s| s.flex_grow(1.0).border(1.0).border_color(floem::peniko::Color::rgb8(80, 80, 80)))
    }
}

impl Default for LayoutTree {
    fn default() -> Self {
        Self {
            root: LayoutNode::Container {
                area: DockArea::Center,
                size_ratio: 1.0,
            },
        }
    }
}