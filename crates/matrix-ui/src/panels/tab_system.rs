//! Tab System with Drag and Drop for MATRIX IDE
//!
//! This module implements a comprehensive tab system with support for tab groups,
//! reordering, drag-and-drop functionality, and visual indicators.

use std::collections::HashMap;
use floem::reactive::{RwSignal, SignalGet, SignalUpdate, SignalWith};
use floem::views::{container, h_stack, v_stack, button, label, Decorators};
use floem::{IntoView, View};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::error::UiError;
use super::dock_container::{TabGroupId, TabId, DockArea};

/// Tab state information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TabState {
    pub id: TabId,
    pub title: String,
    pub content_type: String,
    pub has_changes: bool,
    pub has_errors: bool,
    pub is_active: bool,
    pub is_closable: bool,
    pub icon: Option<String>,
    pub tooltip: Option<String>,
}

/// Tab group configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TabGroupConfig {
    pub id: TabGroupId,
    pub dock_area: DockArea,
    pub max_tabs: Option<usize>,
    pub allow_reorder: bool,
    pub allow_drag_out: bool,
    pub allow_drag_in: bool,
    pub show_close_buttons: bool,
}

impl Default for TabGroupConfig {
    fn default() -> Self {
        Self {
            id: format!("group_{}", Uuid::new_v4()),
            dock_area: DockArea::Bottom,
            max_tabs: None,
            allow_reorder: true,
            allow_drag_out: true,
            allow_drag_in: true,
            show_close_buttons: true,
        }
    }
}

/// Drag and drop state
#[derive(Debug, Clone)]
pub struct DragState {
    pub dragged_tab: TabId,
    pub source_group: TabGroupId,
    pub drag_position: (f64, f64),
    pub drag_offset: (f64, f64),
    pub is_dragging: bool,
}

/// Drop target information
#[derive(Debug, Clone)]
pub struct DropTarget {
    pub target_group: TabGroupId,
    pub insert_index: usize,
    pub is_valid: bool,
}

/// Tab system manager
#[derive(Clone)]
pub struct TabSystem {
    /// All tab groups
    tab_groups: RwSignal<HashMap<TabGroupId, TabGroupState>>,
    
    /// All tabs across all groups
    tabs: RwSignal<HashMap<TabId, TabState>>,
    
    /// Current drag state
    drag_state: RwSignal<Option<DragState>>,
    
    /// Current drop target
    drop_target: RwSignal<Option<DropTarget>>,
    
    /// Event handlers
    event_handlers: RwSignal<HashMap<String, Box<dyn Fn(&TabEvent) -> Result<(), UiError> + Send + Sync>>>,
}

/// Tab group state with reactive signals
#[derive(Debug, Clone)]
pub struct TabGroupState {
    pub config: TabGroupConfig,
    pub tabs: RwSignal<Vec<TabId>>,
    pub active_tab: RwSignal<Option<TabId>>,
    pub position: RwSignal<(f64, f64)>,
    pub size: RwSignal<(f64, f64)>,
    pub is_visible: RwSignal<bool>,
}

/// Tab events
#[derive(Debug, Clone)]
pub enum TabEvent {
    TabCreated { tab_id: TabId, group_id: TabGroupId },
    TabClosed { tab_id: TabId, group_id: TabGroupId },
    TabActivated { tab_id: TabId, group_id: TabGroupId },
    TabMoved { tab_id: TabId, from_group: TabGroupId, to_group: TabGroupId, index: usize },
    TabReordered { tab_id: TabId, group_id: TabGroupId, old_index: usize, new_index: usize },
    TabContentChanged { tab_id: TabId, has_changes: bool },
    TabErrorStateChanged { tab_id: TabId, has_errors: bool },
    GroupCreated { group_id: TabGroupId },
    GroupRemoved { group_id: TabGroupId },
}

impl TabSystem {
    /// Create a new tab system
    pub fn new() -> Self {
        Self {
            tab_groups: RwSignal::new(HashMap::new()),
            tabs: RwSignal::new(HashMap::new()),
            drag_state: RwSignal::new(None),
            drop_target: RwSignal::new(None),
            event_handlers: RwSignal::new(HashMap::new()),
        }
    }

    /// Create a new tab group
    pub fn create_tab_group(&self, config: TabGroupConfig) -> Result<TabGroupId, UiError> {
        let group_id = config.id.clone();
        
        let group_state = TabGroupState {
            config: config.clone(),
            tabs: RwSignal::new(Vec::new()),
            active_tab: RwSignal::new(None),
            position: RwSignal::new((0.0, 0.0)),
            size: RwSignal::new((300.0, 200.0)),
            is_visible: RwSignal::new(true),
        };

        self.tab_groups.update(|groups| {
            groups.insert(group_id.clone(), group_state);
        });

        self.emit_event(TabEvent::GroupCreated { group_id: group_id.clone() })?;
        Ok(group_id)
    }

    /// Remove a tab group
    pub fn remove_tab_group(&self, group_id: &str) -> Result<(), UiError> {
        // Close all tabs in the group first
        let tab_ids = self.tab_groups.with_untracked(|groups| {
            groups.get(group_id)
                .map(|group| group.tabs.get())
                .unwrap_or_default()
        });

        for tab_id in tab_ids {
            self.close_tab(&tab_id)?;
        }

        // Remove the group
        self.tab_groups.update(|groups| {
            groups.remove(group_id);
        });

        self.emit_event(TabEvent::GroupRemoved { group_id: group_id.to_string() })?;
        Ok(())
    }

    /// Create a new tab
    pub fn create_tab(&self, group_id: &str, tab_state: TabState) -> Result<(), UiError> {
        let tab_id = tab_state.id.clone();

        // Add tab to tabs collection
        self.tabs.update(|tabs| {
            tabs.insert(tab_id.clone(), tab_state);
        });

        // Add tab to group
        let group_exists = self.tab_groups.with_untracked(|groups| {
            if let Some(group) = groups.get(group_id) {
                // Check max tabs limit
                if let Some(max_tabs) = group.config.max_tabs {
                    if group.tabs.get().len() >= max_tabs {
                        return false;
                    }
                }

                group.tabs.update(|tabs| {
                    tabs.push(tab_id.clone());
                });

                // Set as active if it's the first tab
                if group.active_tab.get().is_none() {
                    group.active_tab.set(Some(tab_id.clone()));
                }

                true
            } else {
                false
            }
        });

        if !group_exists {
            return Err(UiError::PanelError(format!("Tab group {} not found or full", group_id)));
        }

        self.emit_event(TabEvent::TabCreated { 
            tab_id: tab_id.clone(), 
            group_id: group_id.to_string() 
        })?;

        Ok(())
    }

    /// Close a tab
    pub fn close_tab(&self, tab_id: &str) -> Result<(), UiError> {
        // Find which group contains this tab
        let group_id = self.find_tab_group(tab_id)
            .ok_or_else(|| UiError::PanelError(format!("Tab {} not found", tab_id)))?;

        // Remove from group
        self.tab_groups.with_untracked(|groups| {
            if let Some(group) = groups.get(&group_id) {
                group.tabs.update(|tabs| {
                    tabs.retain(|id| id != tab_id);
                });

                // Update active tab if this was the active one
                if group.active_tab.get().as_ref() == Some(&tab_id.to_string()) {
                    let new_active = group.tabs.get().first().cloned();
                    group.active_tab.set(new_active);
                }
            }
        });

        // Remove from tabs collection
        self.tabs.update(|tabs| {
            tabs.remove(tab_id);
        });

        self.emit_event(TabEvent::TabClosed { 
            tab_id: tab_id.to_string(), 
            group_id 
        })?;

        Ok(())
    }

    /// Activate a tab
    pub fn activate_tab(&self, tab_id: &str) -> Result<(), UiError> {
        let group_id = self.find_tab_group(tab_id)
            .ok_or_else(|| UiError::PanelError(format!("Tab {} not found", tab_id)))?;

        // Set as active in its group
        self.tab_groups.with_untracked(|groups| {
            if let Some(group) = groups.get(&group_id) {
                group.active_tab.set(Some(tab_id.to_string()));
            }
        });

        // Update tab state
        self.tabs.update(|tabs| {
            // Deactivate all tabs first
            for tab in tabs.values_mut() {
                tab.is_active = false;
            }
            // Activate the selected tab
            if let Some(tab) = tabs.get_mut(tab_id) {
                tab.is_active = true;
            }
        });

        self.emit_event(TabEvent::TabActivated { 
            tab_id: tab_id.to_string(), 
            group_id 
        })?;

        Ok(())
    }

    /// Move a tab to a different group
    pub fn move_tab(&self, tab_id: &str, target_group_id: &str, index: usize) -> Result<(), UiError> {
        let source_group_id = self.find_tab_group(tab_id)
            .ok_or_else(|| UiError::PanelError(format!("Tab {} not found", tab_id)))?;

        if source_group_id == target_group_id {
            // Same group - reorder
            return self.reorder_tab(tab_id, index);
        }

        // Check if target group allows drag in
        let can_move = self.tab_groups.with_untracked(|groups| {
            groups.get(target_group_id)
                .map(|group| group.config.allow_drag_in)
                .unwrap_or(false)
        });

        if !can_move {
            return Err(UiError::PanelError("Target group does not allow drag in".to_string()));
        }

        // Remove from source group
        self.tab_groups.with_untracked(|groups| {
            if let Some(source_group) = groups.get(&source_group_id) {
                source_group.tabs.update(|tabs| {
                    tabs.retain(|id| id != tab_id);
                });

                // Update active tab if this was the active one
                if source_group.active_tab.get().as_ref() == Some(&tab_id.to_string()) {
                    let new_active = source_group.tabs.get().first().cloned();
                    source_group.active_tab.set(new_active);
                }
            }
        });

        // Add to target group
        self.tab_groups.with_untracked(|groups| {
            if let Some(target_group) = groups.get(target_group_id) {
                target_group.tabs.update(|tabs| {
                    let insert_index = index.min(tabs.len());
                    tabs.insert(insert_index, tab_id.to_string());
                });

                // Set as active if target group has no active tab
                if target_group.active_tab.get().is_none() {
                    target_group.active_tab.set(Some(tab_id.to_string()));
                }
            }
        });

        self.emit_event(TabEvent::TabMoved { 
            tab_id: tab_id.to_string(), 
            from_group: source_group_id, 
            to_group: target_group_id.to_string(), 
            index 
        })?;

        Ok(())
    }

    /// Reorder a tab within its group
    pub fn reorder_tab(&self, tab_id: &str, new_index: usize) -> Result<(), UiError> {
        let group_id = self.find_tab_group(tab_id)
            .ok_or_else(|| UiError::PanelError(format!("Tab {} not found", tab_id)))?;

        let old_index = self.tab_groups.with_untracked(|groups| {
            groups.get(&group_id)
                .and_then(|group| {
                    let tabs = group.tabs.get();
                    tabs.iter().position(|id| id == tab_id)
                })
        }).ok_or_else(|| UiError::PanelError("Tab not found in group".to_string()))?;

        if old_index == new_index {
            return Ok(()); // No change needed
        }

        // Reorder in group
        self.tab_groups.with_untracked(|groups| {
            if let Some(group) = groups.get(&group_id) {
                group.tabs.update(|tabs| {
                    if old_index < tabs.len() && new_index <= tabs.len() {
                        let tab = tabs.remove(old_index);
                        let insert_index = if new_index > old_index { new_index - 1 } else { new_index };
                        tabs.insert(insert_index.min(tabs.len()), tab);
                    }
                });
            }
        });

        self.emit_event(TabEvent::TabReordered { 
            tab_id: tab_id.to_string(), 
            group_id, 
            old_index, 
            new_index 
        })?;

        Ok(())
    }

    /// Start dragging a tab
    pub fn start_drag(&self, tab_id: &str, position: (f64, f64), offset: (f64, f64)) -> Result<(), UiError> {
        let group_id = self.find_tab_group(tab_id)
            .ok_or_else(|| UiError::PanelError(format!("Tab {} not found", tab_id)))?;

        // Check if source group allows drag out
        let can_drag = self.tab_groups.with_untracked(|groups| {
            groups.get(&group_id)
                .map(|group| group.config.allow_drag_out)
                .unwrap_or(false)
        });

        if !can_drag {
            return Err(UiError::PanelError("Source group does not allow drag out".to_string()));
        }

        self.drag_state.set(Some(DragState {
            dragged_tab: tab_id.to_string(),
            source_group: group_id,
            drag_position: position,
            drag_offset: offset,
            is_dragging: true,
        }));

        Ok(())
    }

    /// Update drag position
    pub fn update_drag(&self, position: (f64, f64)) -> Result<(), UiError> {
        self.drag_state.update(|state| {
            if let Some(drag) = state.as_mut() {
                drag.drag_position = position;
            }
        });

        // Update drop target based on current position
        self.update_drop_target(position)?;
        Ok(())
    }

    /// End dragging and perform drop
    pub fn end_drag(&self) -> Result<(), UiError> {
        let drag_state = self.drag_state.get();
        let drop_target = self.drop_target.get();

        if let (Some(drag), Some(target)) = (drag_state, drop_target) {
            if target.is_valid {
                self.move_tab(&drag.dragged_tab, &target.target_group, target.insert_index)?;
            }
        }

        // Clear drag state
        self.drag_state.set(None);
        self.drop_target.set(None);

        Ok(())
    }

    /// Update tab content change state
    pub fn set_tab_has_changes(&self, tab_id: &str, has_changes: bool) -> Result<(), UiError> {
        let updated = self.tabs.with_untracked(|tabs| {
            if let Some(tab) = tabs.get(tab_id) {
                let mut updated_tab = tab.clone();
                updated_tab.has_changes = has_changes;
                true
            } else {
                false
            }
        });

        if updated {
            self.tabs.update(|tabs| {
                if let Some(tab) = tabs.get_mut(tab_id) {
                    tab.has_changes = has_changes;
                }
            });

            self.emit_event(TabEvent::TabContentChanged { 
                tab_id: tab_id.to_string(), 
                has_changes 
            })?;
        }

        Ok(())
    }

    /// Update tab error state
    pub fn set_tab_has_errors(&self, tab_id: &str, has_errors: bool) -> Result<(), UiError> {
        let updated = self.tabs.with_untracked(|tabs| {
            tabs.contains_key(tab_id)
        });

        if updated {
            self.tabs.update(|tabs| {
                if let Some(tab) = tabs.get_mut(tab_id) {
                    tab.has_errors = has_errors;
                }
            });

            self.emit_event(TabEvent::TabErrorStateChanged { 
                tab_id: tab_id.to_string(), 
                has_errors 
            })?;
        }

        Ok(())
    }

    /// Get tab state
    pub fn get_tab(&self, tab_id: &str) -> Option<TabState> {
        self.tabs.with_untracked(|tabs| tabs.get(tab_id).cloned())
    }

    /// Get all tabs in a group
    pub fn get_group_tabs(&self, group_id: &str) -> Vec<TabState> {
        self.tab_groups.with_untracked(|groups| {
            if let Some(group) = groups.get(group_id) {
                let tab_ids = group.tabs.get();
                self.tabs.with_untracked(|tabs| {
                    tab_ids.iter()
                        .filter_map(|id| tabs.get(id).cloned())
                        .collect()
                })
            } else {
                Vec::new()
            }
        })
    }

    /// Get active tab in a group
    pub fn get_active_tab(&self, group_id: &str) -> Option<TabState> {
        self.tab_groups.with_untracked(|groups| {
            groups.get(group_id)
                .and_then(|group| group.active_tab.get())
                .and_then(|tab_id| self.tabs.with_untracked(|tabs| tabs.get(&tab_id).cloned()))
        })
    }

    /// Create view for a tab group
    pub fn create_tab_group_view(&self, group_id: &str) -> impl IntoView {
        let group_state = self.tab_groups.with_untracked(|groups| groups.get(group_id).cloned());
        
        if let Some(group) = group_state {
            let tabs = group.tabs;
            let active_tab = group.active_tab;
            let tab_system = self.clone();
            let group_id_clone = group_id.to_string();
            
            container(
                v_stack((
                    // Tab bar with drag and drop support
                    container(
                        h_stack((
                            label(|| "Tab Bar".to_string()),
                        ))
                    )
                    .style(|s| s.padding(4.0).background(floem::peniko::Color::rgb8(50, 50, 50))),
                    
                    // Content area
                    container(
                        label(|| "Tab content area".to_string())
                    )
                    .style(|s| s.flex_grow(1.0).padding(8.0).background(floem::peniko::Color::rgb8(40, 40, 40))),
                ))
            )
            .style(|s| s.flex_grow(1.0).border(1.0).border_color(floem::peniko::Color::rgb8(80, 80, 80)))
        } else {
            container(label(|| "Group not found".to_string()))
        }
    }

    /// Create a tab button with drag and drop support
    fn create_tab_button(&self, tab_id: &str, active_tab: &RwSignal<Option<TabId>>, _tab_system: &TabSystem, _group_id: &str) -> impl IntoView {
        let tab_state = self.get_tab(tab_id);
        let tab_id_clone = tab_id.to_string();
        
        if let Some(tab) = tab_state {
            let is_active = active_tab.get().as_ref() == Some(&tab_id_clone);
            
            button(
                label(move || {
                    let mut title = tab.title.clone();
                    if tab.has_changes {
                        title = format!("● {}", title); // Dot for unsaved changes
                    }
                    if tab.has_errors {
                        title = format!("⚠ {}", title); // Warning for errors
                    }
                    title
                })
            )
            .draggable()
            .style(move |s| {
                let mut style = s.padding(8.0).margin_right(2.0);
                if is_active {
                    style = style.background(floem::peniko::Color::rgb8(70, 130, 180));
                } else {
                    style = style.background(floem::peniko::Color::rgb8(60, 60, 60));
                }
                if tab.has_errors {
                    style = style.border_color(floem::peniko::Color::rgb8(255, 100, 100));
                } else if tab.has_changes {
                    style = style.border_color(floem::peniko::Color::rgb8(255, 200, 100));
                }
                style
            })
        } else {
            button(label(|| "Invalid Tab".to_string()))
        }
    }

    /// Find which group contains a tab
    fn find_tab_group(&self, tab_id: &str) -> Option<TabGroupId> {
        self.tab_groups.with_untracked(|groups| {
            for (group_id, group) in groups.iter() {
                if group.tabs.get().contains(&tab_id.to_string()) {
                    return Some(group_id.clone());
                }
            }
            None
        })
    }

    /// Update drop target based on drag position
    fn update_drop_target(&self, position: (f64, f64)) -> Result<(), UiError> {
        // Find the closest tab group and insertion point
        let mut closest_target: Option<DropTarget> = None;
        let mut min_distance = f64::MAX;
        
        self.tab_groups.with_untracked(|groups| {
            for (group_id, group_state) in groups.iter() {
                let group_position = group_state.position.get();
                let group_size = group_state.size.get();
                
                // Check if position is within group bounds (with some tolerance)
                let tolerance = 50.0;
                if position.0 >= group_position.0 - tolerance 
                    && position.0 <= group_position.0 + group_size.0 + tolerance
                    && position.1 >= group_position.1 - tolerance 
                    && position.1 <= group_position.1 + group_size.1 + tolerance {
                    
                    let distance = ((position.0 - group_position.0).powi(2) + 
                                   (position.1 - group_position.1).powi(2)).sqrt();
                    
                    if distance < min_distance {
                        min_distance = distance;
                        
                        // Determine insertion index based on horizontal position
                        let tabs = group_state.tabs.get();
                        let tab_width = if !tabs.is_empty() { 
                            group_size.0 / tabs.len() as f64 
                        } else { 
                            100.0 
                        };
                        
                        let relative_x = position.0 - group_position.0;
                        let insert_index = ((relative_x / tab_width).round() as usize).min(tabs.len());
                        
                        closest_target = Some(DropTarget {
                            target_group: group_id.clone(),
                            insert_index,
                            is_valid: group_state.config.allow_drag_in,
                        });
                    }
                }
            }
        });
        
        self.drop_target.set(closest_target);
        Ok(())
    }

    /// Emit a tab event
    fn emit_event(&self, event: TabEvent) -> Result<(), UiError> {
        self.event_handlers.with_untracked(|handlers| {
            for handler in handlers.values() {
                if let Err(e) = handler(&event) {
                    eprintln!("Tab event handler error: {:?}", e);
                }
            }
        });
        Ok(())
    }

    /// Register an event handler
    pub fn on_event<F>(&self, name: &str, handler: F) 
    where 
        F: Fn(&TabEvent) -> Result<(), UiError> + Send + Sync + 'static 
    {
        self.event_handlers.update(|handlers| {
            handlers.insert(name.to_string(), Box::new(handler));
        });
    }

    /// Get current drag state
    pub fn get_drag_state(&self) -> Option<DragState> {
        self.drag_state.get()
    }

    /// Get current drop target
    pub fn get_drop_target(&self) -> Option<DropTarget> {
        self.drop_target.get()
    }

    /// Cancel current drag operation
    pub fn cancel_drag(&self) -> Result<(), UiError> {
        self.drag_state.set(None);
        self.drop_target.set(None);
        Ok(())
    }

    /// Check if a tab can be dragged from its group
    pub fn can_drag_tab(&self, tab_id: &str) -> bool {
        if let Some(group_id) = self.find_tab_group(tab_id) {
            self.tab_groups.with_untracked(|groups| {
                groups.get(&group_id)
                    .map(|group| group.config.allow_drag_out)
                    .unwrap_or(false)
            })
        } else {
            false
        }
    }

    /// Check if a tab can be dropped into a group
    pub fn can_drop_tab(&self, group_id: &str) -> bool {
        self.tab_groups.with_untracked(|groups| {
            groups.get(group_id)
                .map(|group| group.config.allow_drag_in)
                .unwrap_or(false)
        })
    }

    /// Get visual feedback for drag operation
    pub fn get_drag_feedback(&self) -> Option<String> {
        if let (Some(drag), Some(target)) = (self.drag_state.get(), self.drop_target.get()) {
            if target.is_valid {
                Some(format!("Drop tab '{}' at position {}", drag.dragged_tab, target.insert_index))
            } else {
                Some("Cannot drop here".to_string())
            }
        } else {
            None
        }
    }
}

impl Default for TabSystem {
    fn default() -> Self {
        Self::new()
    }
}