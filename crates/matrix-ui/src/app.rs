//! Modulo principale dell'applicazione UI
//!
//! Questo modulo gestisce l'applicazione UI principale, coordinando
//! tutti i componenti dell'interfaccia utente.

use std::sync::{Arc, RwLock};
use std::path::PathBuf;
use matrix_core::{Engine as CoreEngine};
use crate::{
    error::UiError,
    theme::ThemeManager,
    layout::LayoutManager,
    framework::{MatrixUIFramework, layout::ProfessionalLayoutManager, state::StateManager},
    panels::PanelManager,
    lapce_bridge::LapceIntegration,
    plugin_host::PluginHost,
    plugin_integrator::{PluginHostIntegrator},
    editor::AdvancedEditor,
    components::title_bar::initialize_global_state,
    core::CoreUIArchitecture,
    floem_bridge::FloemBridge,
};

/// Main UI Application with Core Architecture Integration
pub struct App {
    /// Core UI Architecture
    core_ui: Arc<CoreUIArchitecture>,
    
    /// Enhanced Floem Bridge
    floem_bridge: Option<FloemBridge>,

    /// Core Engine reference
    core: Arc<CoreEngine>,

    /// Theme Manager
    theme_manager: Arc<ThemeManager>,

    /// Professional UI Framework
    ui_framework: Arc<MatrixUIFramework>,

    /// Professional Layout Manager
    professional_layout: Arc<ProfessionalLayoutManager>,

    /// Panel Manager
    panel_manager: Arc<PanelManager>,

    /// Lapce Integration
    lapce: Arc<LapceIntegration>,

    /// Plugin Host
    plugin_host: Arc<PluginHost>,

    /// Plugin Integrator
    plugin_integrator: Option<PluginHostIntegrator>,

    /// Layout Manager
    layout: Arc<LayoutManager>,

    /// Advanced Editor
    editor: Arc<AdvancedEditor>,

    /// Event subscription ID
    subscription_id: Option<uuid::Uuid>,

    /// Headless configuration (for testing)
    headless: bool,
}

impl App {
    /// Create a new UI application instance with core architecture
    pub fn new() -> Result<Self, UiError> {
        // Initialize Core UI Architecture
        let core_ui = Arc::new(CoreUIArchitecture::new()?);
        
        // Initialize Core Engine
        let core = Arc::new(matrix_core::init()?);

        // Initialize Theme Manager
        let theme_manager = Arc::new(ThemeManager::new()?);

        // Initialize Professional UI Framework
        let ui_framework = Arc::new(MatrixUIFramework::new()?);

        // Initialize Professional Layout Manager
        let state_manager = Arc::new(StateManager::new());
        let professional_layout = Arc::new(ProfessionalLayoutManager::new(
            theme_manager.clone(),
            state_manager,
        )?);

        // Initialize Lapce Integration
        let lapce = Arc::new(LapceIntegration::new(core.clone(), theme_manager.clone())?);

        // Initialize Panel Manager
        let panel_manager = Arc::new(PanelManager::new(theme_manager.clone()));

        // Initialize Plugin Host
        let plugin_host = Arc::new(PluginHost::new(
            core.clone(),
            panel_manager.clone(),
            theme_manager.clone(),
        ));

        // Initialize Layout Manager
        let layout = Arc::new(LayoutManager::new()?);

        // Initialize Advanced Editor
        let editor = Arc::new(AdvancedEditor::new(
            core.clone(),
            theme_manager.clone(),
        )?);

        // Initialize global state for menu actions
        initialize_global_state(
            core.clone(),
            theme_manager.clone(),
            editor.clone(),
        );

        // Create the application
        let app = Self {
            core_ui,
            floem_bridge: None,
            core,
            theme_manager,
            ui_framework,
            professional_layout,
            panel_manager,
            lapce,
            plugin_host,
            plugin_integrator: None,
            layout,
            editor,
            subscription_id: None,
            headless: false,
        };

        Ok(app)
    }

    /// Initialize the application with core architecture
    pub async fn initialize(&mut self) -> Result<(), UiError> {
        // Initialize Core UI Architecture
        self.core_ui.initialize().await?;
        
        // Initialize Enhanced Floem Bridge
        let mut floem_bridge = FloemBridge::new(self.core_ui.clone())?;
        floem_bridge.initialize().await?;
        self.floem_bridge = Some(floem_bridge);
        
        // Connect theme manager to core architecture
        self.setup_theme_integration().await?;

        // Start Core Engine
        self.core.start()?;

        // Initialize components
        self.theme_manager.initialize()?;
        self.lapce.initialize()?;
        self.panel_manager.initialize()?;

        // Initialize default panels
        self.panel_manager.initialize_default_panels(self.core.clone())?;

        // Initialize plugin host
        self.plugin_host.initialize().map_err(|e| {
            UiError::PluginError(format!("Failed to initialize plugin host: {}", e))
        })?;

        // Initialize plugin integrator
        self.initialize_plugin_integrator()?;

        Ok(())
    }
    
    /// Setup theme integration between theme manager and core architecture
    async fn setup_theme_integration(&mut self) -> Result<(), UiError> {
        // Get current theme from theme manager
        let current_theme = self.theme_manager.get_active_theme()?;
        
        // Update core architecture theme integration
        self.core_ui.theme_integration().update_theme(current_theme)?;
        
        // Setup theme change propagation from theme manager to core architecture
        // This would typically involve setting up a callback or event listener
        // For now, we'll handle this manually when themes change
        
        Ok(())
    }

    /// Inizializza l'integratore plugin-host e carica i plugin
    fn initialize_plugin_integrator(&mut self) -> Result<(), UiError> {
        // Ottieni il percorso del registry
        let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
        let registry_path = home_dir.join(".matrix").join("plugins").join("registry");

        // Crea l'integratore
        let plugin_integrator = PluginHostIntegrator::new(
            self.plugin_host.clone(),
            registry_path,
        ).map_err(|e| {
            UiError::PluginError(format!("Failed to create plugin integrator: {}", e))
        })?;

        self.plugin_integrator = Some(plugin_integrator);

        // Carica tutti i plugin
        if !self.headless {
            if let Some(integrator) = &mut self.plugin_integrator {
                integrator.load_all_plugins().map_err(|e| {
                    UiError::PluginError(format!("Failed to load plugins: {}", e))
                })?;
            }
        }

        Ok(())
    }

    /// Imposta la modalità headless (per test)
    pub fn set_headless(&mut self, headless: bool) {
        self.headless = headless;
    }

    /// Avvia l'applicazione
    pub fn run(&self) -> Result<(), UiError> {
        println!("🚀 Avvio MATRIX_IDE con Layout Professionale...");

        // Usa il nuovo layout professionale invece di quello legacy
        let professional_layout = self.professional_layout.clone();

        // Avvia l'applicazione Floem con il layout professionale
        floem::launch(move || {
            match professional_layout.create_main_layout() {
                Ok(layout) => {
                    println!("✅ Layout professionale creato con successo");
                    Box::new(layout) as Box<dyn floem::View>
                }
                Err(e) => {
                    eprintln!("❌ Errore nella creazione del layout professionale: {}", e);
                    // Fallback a un layout semplice
                    use floem::views::{container, label, Decorators};
                    Box::new(container(
                        label(move || format!("MATRIX_IDE - Errore: {}", e))
                            .style(|s| s.font_size(16.0).color(floem::peniko::Color::RED))
                    )
                    .style(|s| s.size_full().justify_center().items_center()))
                }
            }
        });

        Ok(())
    }

    /// Shutdown the application
    pub async fn shutdown(&mut self) -> Result<(), UiError> {
        // Shutdown Floem Bridge
        if let Some(mut bridge) = self.floem_bridge.take() {
            bridge.shutdown().await?;
        }
        
        // Remove event subscription
        if let Some(subscription_id) = self.subscription_id {
            self.core.event_bus().unsubscribe(subscription_id)?;
        }

        // Shutdown components
        self.plugin_host.shutdown().map_err(|e| {
            UiError::PluginError(format!("Failed to shutdown plugin host: {}", e))
        })?;
        self.panel_manager.shutdown()?;
        self.lapce.shutdown()?;
        self.theme_manager.shutdown()?;

        // Shutdown Core UI Architecture
        self.core_ui.shutdown().await?;

        // Stop Core Engine
        self.core.stop()?;

        Ok(())
    }

    /// Ottiene un riferimento al Core Engine
    pub fn core(&self) -> Arc<CoreEngine> {
        self.core.clone()
    }

    /// Ottiene un riferimento al gestore dei temi
    pub fn theme_manager(&self) -> Arc<ThemeManager> {
        self.theme_manager.clone()
    }

    /// Ottiene un riferimento al gestore dei pannelli
    pub fn panel_manager(&self) -> Arc<PanelManager> {
        self.panel_manager.clone()
    }

    /// Ottiene un riferimento all'integrazione con Lapce
    pub fn lapce(&self) -> Arc<LapceIntegration> {
        self.lapce.clone()
    }

    /// Ottiene un riferimento al gestore dei plugin
    pub fn plugin_host(&self) -> Arc<PluginHost> {
        self.plugin_host.clone()
    }

    /// Ottiene un riferimento all'integratore plugin-host
    pub fn plugin_integrator(&self) -> Option<&PluginHostIntegrator> {
        self.plugin_integrator.as_ref()
    }

    /// Ottiene un riferimento mutabile all'integratore plugin-host
    pub fn plugin_integrator_mut(&mut self) -> Option<&mut PluginHostIntegrator> {
        self.plugin_integrator.as_mut()
    }

    /// Get advanced editor reference
    pub fn editor(&self) -> Arc<AdvancedEditor> {
        self.editor.clone()
    }
    
    /// Get core UI architecture reference
    pub fn core_ui(&self) -> Arc<CoreUIArchitecture> {
        self.core_ui.clone()
    }
    
    /// Get Floem bridge reference
    pub fn floem_bridge(&self) -> Option<&FloemBridge> {
        self.floem_bridge.as_ref()
    }
    
    /// Update theme through the enhanced bridge
    pub fn update_theme(&self, theme_name: &str) -> Result<(), UiError> {
        // Update theme manager
        self.theme_manager.set_active_theme(theme_name)?;
        
        // Get updated theme
        let new_theme = self.theme_manager.get_active_theme()?;
        
        // Update through Floem bridge if available
        if let Some(bridge) = &self.floem_bridge {
            bridge.update_theme(new_theme)?;
        }
        
        Ok(())
    }
}

impl Clone for App {
    fn clone(&self) -> Self {
        Self {
            core_ui: self.core_ui.clone(),
            floem_bridge: None, // Don't clone the bridge
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            ui_framework: self.ui_framework.clone(),
            professional_layout: self.professional_layout.clone(),
            panel_manager: self.panel_manager.clone(),
            lapce: self.lapce.clone(),
            plugin_host: self.plugin_host.clone(),
            plugin_integrator: None, // Non cloniamo l'integratore
            layout: self.layout.clone(),
            editor: self.editor.clone(),
            subscription_id: self.subscription_id,
            headless: self.headless,
        }
    }
}
