//! Enhanced Floem 0.2 Bridge with Core Architecture Integration
//!
//! This module implements the enhanced adapter for integrating Floem 0.2 with the
//! MATRIX IDE core UI architecture, providing reactive theme updates and component communication.

use crate::theme::{Theme};
use crate::error::UiError;
use crate::core::{CoreUIArchitecture, UIEvent, EventHandler, ThemeIntegration};
use floem::peniko::Color;
use floem::style::{Style, Background, TextColor, Transition, BorderColor};
use floem::unit::DurationUnitExt;
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use std::sync::{Arc, RwLock};
use uuid::Uuid;

/// Enhanced Floem Bridge with Core Architecture Integration
pub struct FloemBridge {
    /// Core UI architecture reference
    core_ui: Arc<CoreUIArchitecture>,
    
    /// Theme integration system
    theme_integration: Arc<ThemeIntegration>,
    
    /// Current theme signal (reactive)
    theme_signal: RwSignal<Theme>,

    /// App style signal (reactive)
    app_style_signal: RwSignal<Style>,

    /// Component styles cache
    component_styles: Arc<RwLock<std::collections::HashMap<String, Style>>>,

    /// Event subscription ID
    event_subscription_id: Option<Uuid>,

    /// Bridge ID
    id: Uuid,
}

/// Componenti principali dell'interfaccia
pub enum UiComponent {
    Editor,
    Button,
    Panel,
    Sidebar,
    StatusBar,
    Toolbar,
    Dialog,
    Input,
    Label,
    Tab,
    Tree,
    List,
    Menu,
    ContextMenu,
    Dropdown,
    Checkbox,
    RadioButton,
    Slider,
    Progress,
    ScrollView,
    Frame,
}

impl FloemBridge {
    /// Create a new enhanced Floem bridge with core architecture integration
    pub fn new(core_ui: Arc<CoreUIArchitecture>) -> Result<Self, UiError> {
        let theme_integration = core_ui.theme_integration();
        let initial_theme = theme_integration.get_current_theme();
        
        let theme_signal = create_rw_signal(initial_theme.clone());
        let app_style_signal = create_rw_signal(create_app_style(&initial_theme));
        let component_styles = Arc::new(RwLock::new(create_component_styles(&initial_theme)));

        Ok(Self {
            core_ui,
            theme_integration,
            theme_signal,
            app_style_signal,
            component_styles,
            event_subscription_id: None,
            id: Uuid::new_v4(),
        })
    }
    
    /// Initialize the bridge
    pub async fn initialize(&mut self) -> Result<(), UiError> {
        // For now, we skip event subscription due to thread safety issues with Floem's Style type
        // Theme updates will be handled directly through the update_theme method
        println!("FloemBridge initialized with ID: {}", self.id);
        Ok(())
    }

    /// Update theme through the core architecture (reactive)
    pub fn update_theme(&self, theme: Theme) -> Result<(), UiError> {
        // Update theme through the theme integration system
        self.theme_integration.update_theme(theme.clone())?;
        
        // Update local reactive signals
        self.theme_signal.update(|current| *current = theme.clone());
        self.app_style_signal.update(|style| *style = create_app_style(&theme));
        
        // Update component styles cache
        {
            let mut styles = self.component_styles.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on component styles".to_string())
            })?;
            *styles = create_component_styles(&theme);
        }
        
        // Emit theme changed event through the event bus
        self.core_ui.event_bus().emit(UIEvent::ThemeChanged {
            theme_name: theme.name.clone(),
            timestamp: chrono::Utc::now(),
        })?;
        
        Ok(())
    }
    
    /// Get current theme (reactive)
    pub fn get_current_theme(&self) -> Theme {
        self.theme_signal.get()
    }

    /// Get reactive style for component using theme integration
    pub fn get_component_style(&self, component: UiComponent) -> Result<Style, UiError> {
        let component_name = match component {
            UiComponent::Editor => "editor",
            UiComponent::Button => "button",
            UiComponent::Panel => "panel",
            UiComponent::Sidebar => "sidebar",
            UiComponent::StatusBar => "status_bar",
            UiComponent::Toolbar => "toolbar",
            UiComponent::Dialog => "dialog",
            UiComponent::Input => "input",
            UiComponent::Label => "label",
            UiComponent::Tab => "tab",
            UiComponent::Tree => "tree",
            UiComponent::List => "list",
            UiComponent::Menu => "menu",
            UiComponent::ContextMenu => "context_menu",
            UiComponent::Dropdown => "dropdown",
            UiComponent::Checkbox => "checkbox",
            UiComponent::RadioButton => "radio_button",
            UiComponent::Slider => "slider",
            UiComponent::Progress => "progress",
            UiComponent::ScrollView => "scroll_view",
            UiComponent::Frame => "frame",
        };

        // Use theme integration for consistent styling
        self.theme_integration.get_component_style(component_name)
    }
    
    /// Get component style variant
    pub fn get_component_style_variant(&self, component: UiComponent, variant: &str) -> Result<Style, UiError> {
        let component_name = match component {
            UiComponent::Button => "button",
            UiComponent::Panel => "panel",
            UiComponent::Input => "input",
            _ => return self.get_component_style(component), // Fallback to base style
        };
        
        self.theme_integration.get_component_style_variant(component_name, variant)
    }

    /// Get reactive app style signal
    pub fn get_app_style_signal(&self) -> RwSignal<Style> {
        self.app_style_signal
    }
    
    /// Get app style (current value)
    pub fn get_app_style(&self) -> Style {
        self.app_style_signal.get()
    }

    /// Create custom editor style function based on current theme
    /// This function can be used with .editor_style() on TextEditor
    pub fn create_editor_style_fn(&self) -> Result<impl Fn(floem::views::EditorCustomStyle) -> floem::views::EditorCustomStyle, UiError> {
        let _theme = self.theme_signal.get();

        // Restituisce una closure che applica il tema all'EditorCustomStyle
        Ok(move |style: floem::views::EditorCustomStyle| {
            // Per ora applichiamo solo le configurazioni di base
            // In futuro possiamo aggiungere più personalizzazioni del tema
            style
        })
    }

    /// Create syntax highlighting configuration
    pub fn create_syntax_highlighting(&self) -> Result<floem::views::editor::text::SimpleStylingBuilder, UiError> {
        let theme = self.theme_signal.get();

        let mut styling = floem::views::editor::text::SimpleStylingBuilder::default();
        styling.font_size(theme.font_sizes.md as usize);
        styling.font_family(vec![
            floem::text::FamilyOwned::Name("Fira Code".to_string()),
            floem::text::FamilyOwned::Name("Consolas".to_string()),
            floem::text::FamilyOwned::Monospace,
        ]);

        Ok(styling)
    }

    /// Register style classes with Floem
    pub fn register_style_classes(&self) -> Result<(), UiError> {
        // Implementation for defining common style classes
        // This is an example of how it could work with the new architecture
        Ok(())
    }
    
    /// Get core UI architecture reference
    pub fn core_ui(&self) -> Arc<CoreUIArchitecture> {
        self.core_ui.clone()
    }
    
    /// Get theme integration reference
    pub fn theme_integration(&self) -> Arc<ThemeIntegration> {
        self.theme_integration.clone()
    }
    
    /// Get bridge ID
    pub fn id(&self) -> Uuid {
        self.id
    }
    
    /// Shutdown the bridge and cleanup resources
    pub async fn shutdown(&mut self) -> Result<(), UiError> {
        // Clear component styles cache
        {
            let mut styles = self.component_styles.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on component styles".to_string())
            })?;
            styles.clear();
        }
        
        println!("FloemBridge shutdown completed");
        Ok(())
    }
}

/// Crea lo stile principale dell'applicazione in base al tema
fn create_app_style(theme: &Theme) -> Style {
    let transition_duration = 200.millis();

    Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .transition(Background, Transition::ease_in_out(transition_duration))
        .transition(TextColor, Transition::ease_in_out(transition_duration))
}

/// Crea gli stili dei componenti in base al tema
fn create_component_styles(theme: &Theme) -> std::collections::HashMap<String, Style> {
    let mut styles = std::collections::HashMap::new();
    let transition_duration = 200.millis();

    // Stile per i pulsanti
    let button_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .color(convert_color(&theme.colors.text))
        .border(theme.borders.width_normal)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.sm)
        .hover(|s| s.background(convert_color(&theme.colors.accent).multiply_alpha(0.1)))
        .active(|s| s.background(convert_color(&theme.colors.accent).multiply_alpha(0.2)))
        .focus_visible(|s| s.border_color(convert_color(&theme.colors.accent)))
        .transition(Background, Transition::ease_in_out(transition_duration))
        .transition(BorderColor, Transition::ease_in_out(transition_duration))
        .transition(TextColor, Transition::ease_in_out(transition_duration));

    // Stile per i pannelli
    let panel_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .border(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.md);

    // Stile per la barra laterale
    let sidebar_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .border_right(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border))
        .width(250.0); // Larghezza predefinita

    // Stile per l'editor
    let editor_style = Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .size_full();

    // Stile per input di testo
    let input_style = Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .border(theme.borders.width_normal)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_small)
        .padding(theme.spacing.xs)
        .focus_visible(|s| s.border_color(convert_color(&theme.colors.accent)))
        .transition(BorderColor, Transition::ease_in_out(transition_duration));

    // Stile per le etichette
    let label_style = Style::new()
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .margin(theme.spacing.xxs);

    // Stile per frame contenitori
    let frame_style = Style::new()
        .background(convert_color(&theme.colors.background_tertiary).multiply_alpha(0.1))
        .border(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border).multiply_alpha(0.2))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.md);

    // Inserisci gli stili nella mappa
    styles.insert("button".to_string(), button_style);
    styles.insert("panel".to_string(), panel_style);
    styles.insert("sidebar".to_string(), sidebar_style);
    styles.insert("editor".to_string(), editor_style);
    styles.insert("input".to_string(), input_style);
    styles.insert("label".to_string(), label_style);
    styles.insert("frame".to_string(), frame_style);

    // Aggiungi altri stili qui...

    styles
}

/// Converte un colore del tema in formato Color di Floem
fn convert_color(color: &Color) -> floem::peniko::Color {
    // Assumiamo che il nostro Color del tema abbia componenti RGBA
    // Per ora restituiamo direttamente, ma potremmo dover convertire
    *color
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::theme::Theme;

    #[tokio::test]
    async fn test_enhanced_bridge_creation() {
        let core_ui = Arc::new(CoreUIArchitecture::new().unwrap());
        core_ui.initialize().await.unwrap();
        
        let bridge = FloemBridge::new(core_ui).unwrap();
        assert_ne!(bridge.id(), Uuid::nil());
    }
    
    #[tokio::test]
    async fn test_bridge_theme_integration() {
        let core_ui = Arc::new(CoreUIArchitecture::new().unwrap());
        core_ui.initialize().await.unwrap();
        
        let mut bridge = FloemBridge::new(core_ui).unwrap();
        bridge.initialize().await.unwrap();
        
        let initial_theme = bridge.get_current_theme();
        assert_eq!(initial_theme.name, "MATRIX Professional Dark");
        
        // Test theme update
        let light_theme = Theme::professional_light();
        bridge.update_theme(light_theme.clone()).unwrap();
        
        // Give some time for reactive updates
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        
        let updated_theme = bridge.get_current_theme();
        assert_eq!(updated_theme.name, light_theme.name);
    }
    
    #[tokio::test]
    async fn test_component_styling() {
        let core_ui = Arc::new(CoreUIArchitecture::new().unwrap());
        core_ui.initialize().await.unwrap();
        
        let bridge = FloemBridge::new(core_ui).unwrap();
        
        // Test component style retrieval
        let button_style = bridge.get_component_style(UiComponent::Button).unwrap();
        let panel_style = bridge.get_component_style(UiComponent::Panel).unwrap();
        
        // Test style variants
        let primary_button = bridge.get_component_style_variant(UiComponent::Button, "primary").unwrap();
        let danger_button = bridge.get_component_style_variant(UiComponent::Button, "danger").unwrap();
        
        // Styles should be generated without errors
    }
}
