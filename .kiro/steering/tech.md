# Technology Stack

## Core Technologies

- **Language**: Rust (latest stable edition)
- **UI Framework**: Floem 0.2 + <PERSON><PERSON><PERSON> integration
- **Build System**: Cargo workspace with unified lockfile
- **Architecture**: Event-driven, plugin-based modular system

## Key Dependencies

### UI & Graphics
- `floem = "0.2"` - Modern UI framework with editor features
- `taffy = "0.4"` - Layout engine
- `wgpu = "22.1"` - Graphics API
- `peniko = "0.2"` - 2D graphics primitives

### Core Runtime
- `tokio = "1.46"` - Async runtime with full features
- `serde = "1.0"` - Serialization with derive support
- `anyhow = "1.0"` - Error handling
- `uuid = "1.17"` - UUID generation with v4 and serde

### Plugin System
- `libloading = "0.8"` - Dynamic library loading
- `petgraph = "0.6"` - Graph data structures for DAG engine

### Data & Storage
- `dirs = "5.0"` - Platform directories
- `notify = "6.1"` - File system watching

## Build Commands

### Development
```bash
# Build entire workspace
cargo build

# Build with release optimizations
cargo build --release

# Run tests
cargo test

# Run specific crate
cargo run -p matrix-ui

# Check all code
cargo check --workspace

# Update dependencies
cargo update
```

### UI Development
```bash
# Install Node.js dependencies (for UI components)
npm install

# Update Node.js dependencies
npm update

# Run UI tests
npm test
```

### Plugin Development
```bash
# Build specific plugin
cargo build -p <plugin-name>

# Test plugin loading
cargo test -p matrix-plugin-loader

# Validate plugin manifest
cargo run --bin validate-plugin -- plugins/<plugin-name>/
```

## Workspace Structure

- All crates are workspace members with shared `Cargo.lock`
- Plugin dependencies aligned to workspace versions
- No local dependencies between plugins (only upstream dependencies)
- Uniform dependency versions across workspace to avoid conflicts

## Development Tools

- **Linting**: `cargo clippy` for code quality
- **Formatting**: `cargo fmt` for consistent style
- **Documentation**: `cargo doc` for API docs
- **Benchmarking**: `cargo bench` for performance testing

## Platform Support

- **Primary**: macOS (darwin/zsh)
- **Secondary**: Linux, Windows
- **Architecture**: x86_64, ARM64