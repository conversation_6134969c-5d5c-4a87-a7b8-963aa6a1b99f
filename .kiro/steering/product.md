# Product Overview

MATRIX IDE NEXT is a next-generation IDE built with Rust, integrating precompiled Lapce with Floem 0.2 for a modern, extensible, and reactive development environment.

## Vision

MATRIX IDE aims to become the first cognitive, local, modular, and self-sufficient IDE - not just an editor, but an operating system for development capable of understanding, optimizing, and protecting code.

## Core Principles

- **Modular Architecture**: Core engine with plugin system
- **Modern UI**: Hybrid integration of precompiled Lapce with Floem 0.2
- **AI-First**: Integrated AI assistance in development workflow
- **Performance**: Optimized for responsiveness and resource consumption
- **Privacy by Design**: Local execution, no sensitive data sent to cloud

## Key Features

- **GOD Mode**: End-to-end automation from prompt to MVP
- **Zero Hallucinations**: Symbolic verification + contextual grounding
- **Context Awareness**: Permanent, versioned memory
- **Plugin Ecosystem**: Every function (Git, Terminal, AI, LSP, File Manager, Debugger) is an autonomous plugin
- **Local AI**: Support for local LLM models (<PERSON>wen, CodeLlama, Mistral, GGUF)

## Target Users

Professional developers seeking a powerful, extensible IDE with integrated AI capabilities that respects privacy and offers maximum customization through plugins.