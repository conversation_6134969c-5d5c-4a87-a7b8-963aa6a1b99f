# Project Structure

## Root Directory Organization

```
matrix-next/
├── crates/                  # Core Rust components
├── plugins/                 # Plugin ecosystem
├── docs/                    # Documentation
├── external/                # External dependencies (cline, kilocode)
├── lapce/                   # Lapce integration
├── scripts/                 # Build and utility scripts
├── test_results/            # Test output and logs
└── target/                  # Cargo build artifacts
```

## Core Crates (`crates/`)

### Primary Components
- **`matrix-core/`** - Core engine, orchestration, plugin system, event bus
- **`matrix-ui/`** - UI layer (Floem 0.2 + Lapce), panels, components
- **`matrix-ai/`** - AI services (LLM, embeddings, prompts, verification)
- **`matrix-data/`** - Data management, storage, history, settings
- **`matrix-plugin-loader/`** - Dynamic plugin loading and lifecycle

### Supporting Components
- **`matrix-agent/`** - Agent system with DAG planner and orchestrator
- **`matrix-agent-console/`** - Console interface for agents
- **`matrix-graphics-api/`** - Graphics abstraction layer
- **`socrate/`** - Advanced agent reasoning system

## Plugin Architecture (`plugins/`)

### Structure
```
plugins/
├── official/               # Official MATRIX plugins
│   ├── ai-code-reasoner/
│   ├── ai-task-planner/
│   ├── dag-engine/
│   ├── memory-fabric/
│   └── ui-advanced-panels/
├── examples/               # Example plugins for developers
└── matrix-core/           # Core plugin functionality
```

### Plugin Conventions
- Each plugin is an independent Rust crate
- Implements `Plugin` trait from `matrix-core`
- Optional `PluginView` trait for UI components
- Must use only public traits from `matrix-graphics-api`
- No direct dependencies between plugins

## Documentation (`docs/`)

### Key Documents
- **Architecture**: `MATRIX_ARCHITECTURE_UNIFIED.md`, `BLUEPRINT.md`
- **Development**: `plugin-development.md`, `PLUGIN_TEMPLATE_GUIDE.md`
- **Testing**: `testing/` directory with comprehensive test procedures
- **Integration**: `INTEGRATION_GUIDELINES.md`

## External Dependencies (`external/`)

- **`cline/`** - Cline integration for AI capabilities
- **`kilocode/`** - Kilocode integration for additional tooling

## Naming Conventions

### Crates
- Core components: `matrix-<component>` (e.g., `matrix-core`, `matrix-ui`)
- Plugins: descriptive names (e.g., `ai-code-reasoner`, `memory-fabric`)

### Files
- Rust modules: `snake_case.rs`
- Documentation: `UPPER_CASE.md` for major docs, `lower-case.md` for guides
- Configuration: `lowercase.toml`, `lowercase.json`

### Code Structure
- Public APIs in `lib.rs` or dedicated modules
- Internal implementation in submodules
- Tests in `tests/` directory or `#[cfg(test)]` modules
- Examples in `examples/` directory

## Build Artifacts

- **`target/`** - Cargo build output (gitignored)
- **`test_results/`** - Test logs and reports
- **`node_modules/`** - Node.js dependencies for UI tooling

## Configuration Files

- **`Cargo.toml`** - Workspace configuration with unified dependencies
- **`package.json`** - Node.js dependencies for UI development
- **`.kiro/`** - Kiro IDE configuration and steering rules