# Design Document

## Overview

Il sistema UI di MATRIX IDE è progettato come un'architettura modulare che integra Floem 0.2 con <PERSON><PERSON><PERSON> precompilato, fornendo un ambiente di sviluppo cognitivo e self-sufficient. Il design segue i principi di modularità, performance e estensibilità, con un focus particolare sul Visual DAG Planner ispirato a Buildship (rowyio/buildship) ma completamente portato in Rust nativo.

L'architettura è basata su un sistema di componenti reattivi che comunicano attraverso un event bus centralizzato, permettendo l'integrazione seamless tra editor <PERSON><PERSON><PERSON>, pan<PERSON><PERSON>, plugin esterni e l'agent Cline per l'automazione intelligente.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "MATRIX IDE UI System"
        subgraph "Core Layer"
            Core[Matrix Core Engine]
            EventBus[Reactive Event Bus]
            PluginLoader[Plugin Loader]
        end
        
        subgraph "UI Layer (Floem 0.2)"
            Layout[Layout Manager]
            Theme[Theme System]
            Components[UI Components]
        end
        
        subgraph "Editor Integration"
            Lapce[Lapce Core]
            Bridge[Lapce-Floem Bridge]
            EditorAPI[Editor API]
        end
        
        subgraph "Plugin System"
            PluginDock[Plugin Dock]
            PluginSlots[Plugin Slots]
            PluginAPI[Plugin API]
        end
        
        subgraph "Panel System"
            PanelManager[Panel Manager]
            DockSystem[Dock System]
            TabSystem[Tab System]
        end
        
        subgraph "DAG System"
            DAGEditor[Visual DAG Editor]
            DAGEngine[DAG Engine]
            BuildshipPort[Buildship Port Layer]
        end
        
        subgraph "Agent Integration"
            ClineAgent[Cline Agent]
            AgentUI[Agent UI Components]
            TaskVisualization[Task Visualization]
        end
    end
    
    Core --> EventBus
    EventBus --> Layout
    Layout --> Components
    Components --> Bridge
    Bridge --> Lapce
    
    PluginLoader --> PluginDock
    PluginDock --> PluginSlots
    PluginSlots --> PluginAPI
    
    Layout --> PanelManager
    PanelManager --> DockSystem
    DockSystem --> TabSystem
    
    Components --> DAGEditor
    DAGEditor --> DAGEngine
    DAGEngine --> BuildshipPort
    
    ClineAgent --> AgentUI
    AgentUI --> TaskVisualization
    TaskVisualization --> DAGEditor
```

### Component Architecture

```mermaid
graph LR
    subgraph "UI Components Hierarchy"
        App[App Root]
        MainLayout[Main Layout]
        
        subgraph "Top Area"
            TitleBar[Title Bar]
            MenuBar[Menu Bar]
        end
        
        subgraph "Main Content"
            Sidebar[Left Sidebar]
            EditorArea[Editor Area]
            PluginDock[Plugin Dock]
        end
        
        subgraph "Bottom Area"
            PanelArea[Panel Area]
            StatusBar[Status Bar]
        end
        
        subgraph "Modal/Overlay"
            DAGModal[DAG Editor Modal]
            SettingsModal[Settings Modal]
        end
    end
    
    App --> MainLayout
    MainLayout --> TitleBar
    MainLayout --> MenuBar
    MainLayout --> Sidebar
    MainLayout --> EditorArea
    MainLayout --> PluginDock
    MainLayout --> PanelArea
    MainLayout --> StatusBar
    MainLayout --> DAGModal
    MainLayout --> SettingsModal
```

## Components and Interfaces

### 1. Visual DAG Editor (Buildship Port)

#### Core Components

```rust
// DAG Editor Core
pub struct VisualDAGEditor {
    canvas: DAGCanvas,
    node_palette: NodePalette,
    inspector: NodeInspector,
    minimap: DAGMinimap,
    toolbar: DAGToolbar,
    state: RwSignal<DAGEditorState>,
}

// Canvas System (Buildship-inspired)
pub struct DAGCanvas {
    viewport: Viewport,
    grid: Grid,
    nodes: RwSignal<Vec<DAGNode>>,
    edges: RwSignal<Vec<DAGEdge>>,
    selection: RwSignal<Selection>,
    interaction_state: RwSignal<InteractionState>,
}

// Node System
pub struct DAGNode {
    id: NodeId,
    position: Point,
    size: Size,
    node_type: NodeType,
    data: NodeData,
    ports: Vec<NodePort>,
    state: NodeState,
    style: NodeStyle,
}

// Edge System
pub struct DAGEdge {
    id: EdgeId,
    source: NodePort,
    target: NodePort,
    edge_type: EdgeType,
    style: EdgeStyle,
    validation: EdgeValidation,
}
```

#### Buildship Port Layer

```rust
// Buildship compatibility layer
pub struct BuildshipPortLayer {
    node_registry: NodeRegistry,
    template_system: TemplateSystem,
    export_system: ExportSystem,
    import_system: ImportSystem,
}

// Node Registry (from Buildship patterns)
pub struct NodeRegistry {
    categories: HashMap<String, NodeCategory>,
    node_definitions: HashMap<String, NodeDefinition>,
    custom_nodes: HashMap<String, CustomNodeDefinition>,
}

// Template System
pub struct TemplateSystem {
    templates: HashMap<String, DAGTemplate>,
    template_store: TemplateStore,
    sharing_system: SharingSystem,
}
```

#### Floem 0.2 Integration

```rust
// Floem 0.2 Canvas Implementation
impl View for DAGCanvas {
    type State = DAGCanvasState;
    
    fn build(self, cx: &mut ViewContext) -> impl IntoView {
        canvas(move |cx, size| {
            // Use Floem 0.2 canvas API
            let mut canvas = Canvas::new(size);
            
            // Render grid
            self.render_grid(&mut canvas, cx);
            
            // Render edges
            for edge in self.edges.get() {
                self.render_edge(&mut canvas, &edge, cx);
            }
            
            // Render nodes
            for node in self.nodes.get() {
                self.render_node(&mut canvas, &node, cx);
            }
            
            // Render selection
            if let Some(selection) = self.selection.get() {
                self.render_selection(&mut canvas, &selection, cx);
            }
            
            canvas
        })
        .on_event(EventListener::PointerDown, |event| {
            // Handle mouse events with Floem 0.2 event system
        })
        .on_event(EventListener::PointerMove, |event| {
            // Handle drag operations
        })
        .on_event(EventListener::KeyDown, |event| {
            // Handle keyboard shortcuts
        })
    }
}
```

### 2. Plugin Dock System

#### Architecture

```rust
// Plugin Dock Manager
pub struct PluginDockManager {
    slots: RwSignal<Vec<PluginSlot>>,
    layout: RwSignal<DockLayout>,
    plugin_registry: PluginRegistry,
    event_bus: EventBus,
}

// Plugin Slot
pub struct PluginSlot {
    id: SlotId,
    plugin_id: Option<PluginId>,
    size: RwSignal<Size>,
    position: RwSignal<Position>,
    state: RwSignal<SlotState>,
    view: Option<Box<dyn PluginView>>,
}

// Plugin View Trait
pub trait PluginView: Send + Sync {
    fn render(&self, cx: &mut ViewContext) -> impl IntoView;
    fn handle_event(&mut self, event: PluginEvent) -> Result<(), PluginError>;
    fn get_preferred_size(&self) -> Size;
    fn can_resize(&self) -> bool;
}
```

#### Plugin Communication

```rust
// Event Bus for Plugin Communication
pub struct PluginEventBus {
    channels: HashMap<PluginId, Sender<PluginEvent>>,
    global_channel: Broadcast<GlobalEvent>,
    security_manager: SecurityManager,
}

// Plugin Events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginEvent {
    Initialize(PluginConfig),
    Shutdown,
    Resize(Size),
    Focus(bool),
    DataUpdate(PluginData),
    Command(PluginCommand),
    Error(PluginError),
}

// Security Manager
pub struct SecurityManager {
    permissions: HashMap<PluginId, PermissionSet>,
    sandbox: PluginSandbox,
    resource_limits: ResourceLimits,
}
```

### 3. Modular Panel System

#### Panel Architecture

```rust
// Panel Manager
pub struct PanelManager {
    panels: RwSignal<Vec<Panel>>,
    layout: RwSignal<PanelLayout>,
    dock_areas: HashMap<DockArea, DockContainer>,
    tab_system: TabSystem,
}

// Panel Definition
pub struct Panel {
    id: PanelId,
    title: String,
    content: Box<dyn PanelContent>,
    dock_area: DockArea,
    tab_group: Option<TabGroupId>,
    state: RwSignal<PanelState>,
    preferences: PanelPreferences,
}

// Dock System
pub struct DockContainer {
    area: DockArea,
    splitters: Vec<Splitter>,
    tab_groups: Vec<TabGroup>,
    layout_tree: LayoutTree,
}

// Tab System
pub struct TabSystem {
    groups: HashMap<TabGroupId, TabGroup>,
    active_tabs: HashMap<TabGroupId, TabId>,
    tab_order: Vec<TabId>,
    drag_state: RwSignal<Option<TabDragState>>,
}
```

#### Docking Implementation

```rust
// Docking with Floem 0.2
impl View for DockContainer {
    type State = DockContainerState;
    
    fn build(self, cx: &mut ViewContext) -> impl IntoView {
        container(
            // Use Floem 0.2 layout system
            flex()
                .direction(FlexDirection::Column)
                .children(
                    self.tab_groups.iter().map(|group| {
                        tab_group_view(group.clone())
                            .flex_grow(1.0)
                            .on_drag_over(|event| {
                                // Handle tab drag over
                            })
                            .on_drop(|event| {
                                // Handle tab drop
                            })
                    })
                )
        )
        .style(|s| s.size_full())
    }
}

// Tab Group View
fn tab_group_view(group: TabGroup) -> impl IntoView {
    container(
        v_stack((
            // Tab bar
            h_stack(
                group.tabs.iter().map(|tab| {
                    tab_button(tab.clone())
                        .on_click(move |_| {
                            // Activate tab
                        })
                        .draggable(true)
                        .on_drag_start(move |event| {
                            // Start tab drag
                        })
                })
            ),
            // Content area
            container(
                group.active_content()
            ).flex_grow(1.0)
        ))
    )
}
```

### 4. Layout Integration System

#### Main Layout Manager

```rust
// Main Layout Manager
pub struct LayoutManager {
    root_layout: RwSignal<RootLayout>,
    theme_manager: ThemeManager,
    responsive_system: ResponsiveSystem,
    layout_persistence: LayoutPersistence,
}

// Root Layout Structure
pub struct RootLayout {
    title_bar: TitleBarLayout,
    main_content: MainContentLayout,
    status_bar: StatusBarLayout,
    modal_layer: ModalLayer,
}

// Main Content Layout
pub struct MainContentLayout {
    sidebar: SidebarLayout,
    editor_area: EditorAreaLayout,
    plugin_dock: PluginDockLayout,
    panel_area: PanelAreaLayout,
    splitters: Vec<Splitter>,
}
```

#### Lapce Integration Bridge

```rust
// Lapce-Floem Bridge
pub struct LapceFloemBridge {
    lapce_instance: LapceInstance,
    floem_container: FloemContainer,
    event_translator: EventTranslator,
    focus_manager: FocusManager,
}

// Event Translation
pub struct EventTranslator {
    lapce_to_floem: EventMapper<LapceEvent, FloemEvent>,
    floem_to_lapce: EventMapper<FloemEvent, LapceEvent>,
    key_bindings: KeyBindingManager,
}

// Focus Management
pub struct FocusManager {
    focus_stack: Vec<FocusTarget>,
    active_component: RwSignal<Option<ComponentId>>,
    focus_policies: HashMap<ComponentId, FocusPolicy>,
}
```

### 5. Cline Agent Integration

#### Agent UI Components

```rust
// Agent Integration Manager
pub struct AgentIntegrationManager {
    cline_client: ClineClient,
    agent_ui: AgentUI,
    task_visualizer: TaskVisualizer,
    interaction_manager: InteractionManager,
}

// Agent UI
pub struct AgentUI {
    chat_panel: ChatPanel,
    progress_panel: ProgressPanel,
    suggestion_overlay: SuggestionOverlay,
    confirmation_dialogs: ConfirmationDialogs,
}

// Task Visualizer
pub struct TaskVisualizer {
    dag_integration: DAGIntegration,
    progress_indicators: ProgressIndicators,
    status_updates: StatusUpdates,
    error_visualization: ErrorVisualization,
}
```

## Data Models

### DAG Data Models

```rust
// DAG Serialization Format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DAGDocument {
    version: String,
    metadata: DAGMetadata,
    nodes: Vec<SerializedNode>,
    edges: Vec<SerializedEdge>,
    layout: LayoutInfo,
    settings: DAGSettings,
}

// Node Data Model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializedNode {
    id: String,
    node_type: String,
    position: Point,
    size: Size,
    data: serde_json::Value,
    ports: Vec<PortDefinition>,
    metadata: NodeMetadata,
}

// Edge Data Model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializedEdge {
    id: String,
    source: PortReference,
    target: PortReference,
    edge_type: String,
    metadata: EdgeMetadata,
}
```

### Plugin Data Models

```rust
// Plugin Configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    id: String,
    name: String,
    version: String,
    author: String,
    description: String,
    permissions: PermissionSet,
    dependencies: Vec<PluginDependency>,
    ui_config: PluginUIConfig,
}

// Plugin State
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginState {
    id: PluginId,
    status: PluginStatus,
    data: serde_json::Value,
    preferences: PluginPreferences,
    resource_usage: ResourceUsage,
}
```

### Panel Data Models

```rust
// Panel Layout Configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelLayoutConfig {
    version: String,
    layouts: HashMap<String, LayoutDefinition>,
    default_layout: String,
    user_customizations: Vec<LayoutCustomization>,
}

// Layout Definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutDefinition {
    name: String,
    description: String,
    areas: HashMap<DockArea, AreaDefinition>,
    splitter_positions: Vec<SplitterPosition>,
    tab_configurations: Vec<TabConfiguration>,
}
```

## Error Handling

### Error Types

```rust
// UI System Errors
#[derive(Debug, thiserror::Error)]
pub enum UIError {
    #[error("Plugin error: {0}")]
    Plugin(#[from] PluginError),
    
    #[error("Layout error: {0}")]
    Layout(#[from] LayoutError),
    
    #[error("DAG error: {0}")]
    DAG(#[from] DAGError),
    
    #[error("Lapce integration error: {0}")]
    LapceIntegration(#[from] LapceIntegrationError),
    
    #[error("Floem error: {0}")]
    Floem(#[from] FloemError),
    
    #[error("Agent integration error: {0}")]
    AgentIntegration(#[from] AgentIntegrationError),
}

// Error Recovery System
pub struct ErrorRecoverySystem {
    recovery_strategies: HashMap<ErrorType, RecoveryStrategy>,
    error_reporter: ErrorReporter,
    fallback_ui: FallbackUI,
}
```

### Error Recovery

```rust
// Recovery Strategies
pub enum RecoveryStrategy {
    Restart(ComponentId),
    Fallback(FallbackConfig),
    Isolate(IsolationConfig),
    Notify(NotificationConfig),
    Ignore,
}

// Fallback UI
pub struct FallbackUI {
    minimal_layout: MinimalLayout,
    error_display: ErrorDisplay,
    recovery_controls: RecoveryControls,
}
```

## Testing Strategy

### Unit Testing

```rust
// Component Testing Framework
pub struct ComponentTestFramework {
    test_renderer: TestRenderer,
    mock_services: MockServices,
    assertion_helpers: AssertionHelpers,
}

// DAG Editor Testing
#[cfg(test)]
mod dag_editor_tests {
    use super::*;
    
    #[tokio::test]
    async fn test_node_creation() {
        let mut editor = DAGEditor::new_for_test();
        let node = editor.create_node(NodeType::Task, Point::new(100, 100)).await;
        assert!(node.is_ok());
        assert_eq!(editor.nodes().len(), 1);
    }
    
    #[tokio::test]
    async fn test_edge_connection() {
        let mut editor = DAGEditor::new_for_test();
        let node1 = editor.create_node(NodeType::Task, Point::new(100, 100)).await.unwrap();
        let node2 = editor.create_node(NodeType::Task, Point::new(200, 100)).await.unwrap();
        
        let edge = editor.connect_nodes(node1.output_port(), node2.input_port()).await;
        assert!(edge.is_ok());
        assert_eq!(editor.edges().len(), 1);
    }
}
```

### Integration Testing

```rust
// Integration Test Suite
#[cfg(test)]
mod integration_tests {
    use super::*;
    
    #[tokio::test]
    async fn test_plugin_dock_integration() {
        let app = TestApp::new().await;
        let plugin = TestPlugin::new("test-plugin");
        
        app.plugin_dock().install_plugin(plugin).await.unwrap();
        assert!(app.plugin_dock().is_plugin_active("test-plugin"));
        
        let slot = app.plugin_dock().get_slot("test-plugin").unwrap();
        assert!(slot.is_visible());
    }
    
    #[tokio::test]
    async fn test_lapce_floem_bridge() {
        let app = TestApp::new().await;
        let bridge = app.lapce_bridge();
        
        // Test event forwarding
        bridge.send_lapce_event(LapceEvent::FileOpen("test.rs".into())).await.unwrap();
        
        // Verify Floem receives the event
        let events = app.event_collector().collect_events().await;
        assert!(events.iter().any(|e| matches!(e, FloemEvent::FileOpen(_))));
    }
}
```

### Performance Testing

```rust
// Performance Benchmarks
#[cfg(test)]
mod performance_tests {
    use criterion::{black_box, criterion_group, criterion_main, Criterion};
    
    fn bench_dag_rendering(c: &mut Criterion) {
        c.bench_function("dag_render_1000_nodes", |b| {
            let dag = create_large_dag(1000);
            b.iter(|| {
                black_box(dag.render());
            });
        });
    }
    
    fn bench_plugin_communication(c: &mut Criterion) {
        c.bench_function("plugin_event_dispatch", |b| {
            let event_bus = PluginEventBus::new();
            let event = PluginEvent::DataUpdate(test_data());
            
            b.iter(|| {
                black_box(event_bus.dispatch(event.clone()));
            });
        });
    }
    
    criterion_group!(benches, bench_dag_rendering, bench_plugin_communication);
    criterion_main!(benches);
}
```

## Implementation Notes

### Floem 0.2 Specific Considerations

1. **Reactive System**: Utilizzare `RwSignal` e `Memo` per state management reattivo
2. **Layout System**: Sfruttare il nuovo layout engine di Floem 0.2 per performance ottimali
3. **Event Handling**: Implementare event handlers usando le nuove API di Floem 0.2
4. **Canvas Rendering**: Utilizzare le API canvas ottimizzate per il DAG editor
5. **Theme Integration**: Integrare con il sistema di theming di Floem 0.2

### Buildship Port Strategy

1. **Component Analysis**: Analizzare i componenti React di Buildship per identificare pattern
2. **API Mapping**: Mappare le API React a equivalenti Rust/Floem
3. **State Management**: Convertire Redux/Context a Floem reactive signals
4. **Event System**: Adattare gli event handlers React a Floem event system
5. **Styling**: Convertire CSS/styled-components a Floem styling system

### Performance Optimizations

1. **Virtual Scrolling**: Per liste grandi di nodi/plugin
2. **Render Batching**: Raggruppare aggiornamenti UI per ridurre re-render
3. **Memory Pooling**: Riutilizzare oggetti per ridurre allocazioni
4. **Lazy Loading**: Caricare plugin e pannelli on-demand
5. **Caching**: Cache per rendering e computazioni costose